/* global config */

const apiKEY = 'b709af18554cee13bd74d8ab20aad74a4c6d46bd520ede88b54144b40e4ff3a8';

/* eslint-disable */
async function fetchApi(url, methodtype, header, body1) {

    const urlConfig = `${config.API_URL_PREFIX}${config.API_VERSION_PREFIX}${url}`;

    const res = (/(http(s)?:\/\/.)?(www\.)?[-a-zA-Z0-9@:%.+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_+.~#?&//=]*)/g).test(url);

    try {
        const data = await fetch(
            res ? url : urlConfig, {
            method: methodtype,
            headers: header,
            body: JSON.stringify(body1)
        });

        return await data;
    } catch (e) {
        return e;
    }
}
let accessToken;

async function fetchUserApi(userAccessToken) {

    const ele = document.getElementById('preloader') ?? document.getElementById('preloader1');

    if (ele) {

        setTimeout(() => {
            ele.style.opacity = '0';
            setTimeout(() => {
                ele.style.display = 'none';
            }, 500);
        }, 500);
    }

    document.getElementById('mobile-nav-click').addEventListener('click', e => {
        e.preventDefault();
        if (document.getElementById('mobile-nav-overly').style.display === "none") {
            document.getElementById('mobile-nav-overly').style.display = 'block';
            document.body.classList.add('mobile-nav-active');
        }
        else {
            document.getElementById('mobile-nav-overly').style.display = 'none';
            document.body.classList.remove('mobile-nav-active');
        }
    });

    accessToken = userAccessToken;
    this.accessTokn = userAccessToken;

    if (userAccessToken === null || userAccessToken === 'null' || userAccessToken === undefined || userAccessToken === '') {
        const locationUrl = window.location.href;

        if (locationUrl !== '') {
            var url = new URL(locationUrl);
            const location = url.searchParams.get("access_token");

            if (location !== null || location !== '' || location !== undefined) {

                accessToken = location;
                userAccessToken = location;
                this.accessTokn = location;

                localStorage.setItem('accessToken', location);

                const urlwithout = getURLWithoutParams(window.location.href);

                window.history.replaceState(
                    history.state,
                    (document && document.title) || '',
                    urlwithout);
            }
        }
    }

    const moement = moment().seconds(0)
        .milliseconds(0)
        .format('X');

    const plaintext = `${apiKEY}:MH:${moement}`;
    const mesh256 = sha256(plaintext);

    let userResponse;

    if (accessToken) {

        const userApi = await fetchApi('customer/user_details', 'post', {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Authorization': `Bearer ${accessToken}`
        }, {
            "client_id": config.MH_CLIENT_ID,
            "credentials": mesh256,
            "package_id": "go.meethour.io",
            "domain": "meethour.io"
        });

        userResponse = await userApi.json();
    }

    if (userResponse && userResponse.success) {
        const userData = userResponse.data;

        document.getElementById('signInApi').innerHTML = `
        <li style="margin-right:10px;"> <a href="/joinmeeting">Join a Meeting</a></li>
        <li class="drop-down"><img class="avatar" src="${userData.picture}"> <a href="">${userData.name}</a>
        <ul>
          <li><a href="${config.URL_PREFIX}serviceLogin?client_id=${config.MH_CLIENT_ID}&redirect_uri=${config.URL_PREFIX}customer/dashboard&device_type=web&access_token=${this.accessTokn}"><i class="bx bxs-dashboard" ></i> Dashboard</a></li>
          <li><a href="${config.URL_PREFIX}serviceLogin?client_id=${config.MH_CLIENT_ID}&redirect_uri=${config.URL_PREFIX}customer/profile&device_type=web&access_token=${this.accessTokn}" ><i class="bx bx-user" ></i> Profile</a> </li>
          <li onclick="logoutClick()"><a href="#" style="cursor: pointer;"><i class="bx bx-log-out"></i> Logout</a></li> 
        </ul>
      </li>`;

        document.getElementById('mobilesignInApi').innerHTML = `
      <li style="margin-right: 25px;"> <a href="/joinmeeting">Join a Meeting</a></li>
      <li class="drop-down" style="margin-left: 17px;"><img class="avatar" src="${userData.picture}" style="width: 45px; height: 42px; float: left;"> <a href="">${userData.name}</a>
      <ul class="drop-down1">
        <li><a href="${config.URL_PREFIX}serviceLogin?client_id=${config.MH_CLIENT_ID}&redirect_uri=${config.URL_PREFIX}customer/dashboard&device_type=web&access_token=${this.accessTokn}"><i class="bx bxs-dashboard" ></i> Dashboard</a></li>
        <li><a href="${config.URL_PREFIX}serviceLogin?client_id=${config.MH_CLIENT_ID}&redirect_uri=${config.URL_PREFIX}customer/profile&device_type=web&access_token=${this.accessTokn}" ><i class="bx bx-user" ></i> Profile</a> </li>
        <li onclick="logoutClick()"><a href="#"style="cursor: pointer;"><i class="bx bx-log-out"></i> Logout</a></li> 
      </ul>
    </li>`;
    } else {
        document.getElementById('signIn').addEventListener('click', e => {
            e.preventDefault();
            const apiFrame = `${config.URL_PREFIX}serviceLogin?client_id=${config.MH_CLIENT_ID}&redirect_uri=${window.location.href}&device_type=web&response_type=get`;

            window.location.replace(apiFrame);
        });

        document.getElementById('signInMobile').addEventListener('click', e => {
            e.preventDefault();
            const apiFrame = `${config.URL_PREFIX}serviceLogin?client_id=${config.MH_CLIENT_ID}&redirect_uri=${window.location.href}&device_type=web&response_type=get`;

            window.location.replace(apiFrame);
        });
    }

    const urlwithout = getURLWithoutParams(window.location.href);

    window.history.replaceState(
        history.state,
        (document && document.title) || '',
        urlwithout);


}
function contactSale() {
    const linkBelow = `${window.location.origin}/contact.html`
    window.location.replace(linkBelow)
}

function dashboardClick() {
    const urlConfig = `${config.URL_PREFIX}serviceLogin?client_id=${config.MH_CLIENT_ID}&redirect_uri=${config.URL_PREFIX}customer/dashboard&device_type=web&access_token=${this.accessTokn}`;

    window.location.replace(urlConfig);
}

function profileClick() {
    const urlConfig = `${config.URL_PREFIX}serviceLogin?client_id=${config.MH_CLIENT_ID}&redirect_uri=${config.URL_PREFIX}customer/profile&device_type=web&access_token=${this.accessTokn}`;

    window.location.replace(urlConfig);
}

window.onload = fetchUserApi(localStorage.getItem("accessToken"));

function logoutClick() {
    localStorage.removeItem("accessToken");
    window.location.reload();

}


function getURLWithoutParams(url1) {
    let url = new URL(url1);
    const { hash, search } = url;

    if ((hash && hash.length > 1) || (search && search.length > 1)) {
        url = new URL(url.href);
        url.hash = '';
        url.search = '';

        // XXX The implementation of URL at least on React Native appends ? and
        // # at the end of the href which is not desired.
        let { href } = url;

        if (href) {
            href.endsWith('#') && (href = href.substring(0, href.length - 1));
            href.endsWith('?') && (href = href.substring(0, href.length - 1));

            // eslint-disable-next-line no-param-reassign
            url.href === href || (url = new URL(href));
        }
    }

    return url;
}

function hospitalClicker() {
    const urlConfig = `${config.URL_PREFIX}serviceLogin?client_id=${config.MH_CLIENT_ID}&redirect_uri=${config.URL_PREFIX}customer/mysubscription&device_type=web${this.accessTokn ? `&access_token=${this.accessTokn}` : ''}`;

    window.location.replace(urlConfig);
}

function contactClicker() {
    const urlConf = `${window.location.origin}/contact.html`
    window.location.replace(urlConf)
}
