<html xmlns="http://www.w3.org/1999/html">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="content-type" content="text/html;charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!--#include virtual="/base.html" -->
    <link rel="stylesheet" href="css/all.css">
    <script>
      document.addEventListener('DOMContentLoaded', () => {
          if (!MeetHourJS.app) {
            return;
          }

          const url = new URL(window.location.href);
          const params = new URLSearchParams(url.search);
          const showAvatar = params.get('showAvatar') === 'true';
          const showJoinActions = params.get('showJoinActions') === 'true';
          const showSkipPrejoin = params.get('showSkipPrejoin') === 'true';
          const css = params.get('style');
          const style = document.createElement('style');
          style.appendChild(document.createTextNode(css));
          document.head.appendChild(style);

          MeetHourJS.app.renderEntryPoint({
              Component: MeetHourJS.app.entryPoints.PREJOIN,
              props: {
                showAvatar,
                showJoinActions,
                showSkipPrejoin
              }
          })
      })
    </script>
    <!--#include virtual="/title.html" -->
    <script id="Config" src="https://api.meethour.io/libs/v2.4.6/config.js?apiKey=b709af18554cee13bd74d8ab20aad74a4c6d46bd520ede88b54144b40e4ff3a8" ></script>
    <script id="interfaceConfig" src="https://api.meethour.io/libs/v2.4.6/interface_config.js?apiKey=b709af18554cee13bd74d8ab20aad74a4c6d46bd520ede88b54144b40e4ff3a8"></script>
    <script src="https://api.meethour.io/libs/v2.4.6/lib-meet-hour.min.js?v=139"></script>
    <script src="libs/app.bundle.min.js?v=139"></script>
  </head>
  <body>
    <div id="react"></div>
  </body>
</html>
