import React from "react";

export const VideoIcon = () => {
  return (
    <>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="35px"
        height="35px"
        viewBox="0 0 24 24"
      >
        <path
          fill="none"
          stroke="#fff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="0.95"
          d="M3 15.75v-7.5a2 2 0 0 1 2-2h8.5a2 2 0 0 1 2 2v7.5a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2m17.168-8.759l-4 3.563a.5.5 0 0 0-.168.373v1.778a.5.5 0 0 0 .168.373l4 3.563a.5.5 0 0 0 .832-.374V7.365a.5.5 0 0 0-.832-.374"
        />
      </svg>
    </>
  );
};

export const MyCalyIcon = () => {
  return (
    <>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="26px"
        height="26px"
        viewBox="0 0 24 24"
      >
        <path
          fill="#fff"
          d="M19.5 4h-3V2.5a.5.5 0 0 0-1 0V4h-7V2.5a.5.5 0 0 0-1 0V4h-3A2.503 2.503 0 0 0 2 6.5v13A2.503 2.503 0 0 0 4.5 22h15a2.502 2.502 0 0 0 2.5-2.5v-13A2.502 2.502 0 0 0 19.5 4M21 19.5a1.5 1.5 0 0 1-1.5 1.5h-15A1.5 1.5 0 0 1 3 19.5V11h18zm0-9.5H3V6.5C3 5.672 3.67 5 4.5 5h3v1.5a.5.5 0 0 0 1 0V5h7v1.5a.5.5 0 0 0 1 0V5h3A1.5 1.5 0 0 1 21 6.5z"
        />
      </svg>
    </>
  );
};

export const BracketsCurlyIcon = () => {
  return (
    <>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="26px"
        height="26px"
        viewBox="0 0 256 256"
      >
        <path
          fill="#fff"
          d="M39.91 128a27.7 27.7 0 0 1 9.49 11.13C54 148.62 54 160.51 54 172c0 24.27 1.21 38 26 38a6 6 0 0 1 0 12c-16.88 0-27.81-5.6-33.4-17.13C42 195.38 42 183.49 42 172c0-24.27-1.21-38-26-38a6 6 0 0 1 0-12c24.79 0 26-13.73 26-38c0-11.49 0-23.38 4.6-32.87C52.19 39.6 63.12 34 80 34a6 6 0 0 1 0 12c-24.79 0-26 13.73-26 38c0 11.49 0 23.38-4.6 32.87A27.7 27.7 0 0 1 39.91 128M240 122c-24.79 0-26-13.73-26-38c0-11.49 0-23.38-4.6-32.87C203.81 39.6 192.88 34 176 34a6 6 0 0 0 0 12c24.79 0 26 13.73 26 38c0 11.49 0 23.38 4.6 32.87a27.7 27.7 0 0 0 9.49 11.13a27.7 27.7 0 0 0-9.49 11.13c-4.6 9.49-4.6 21.38-4.6 32.87c0 24.27-1.21 38-26 38a6 6 0 0 0 0 12c16.88 0 27.81-5.6 33.4-17.13c4.6-9.49 4.6-21.38 4.6-32.87c0-24.27 1.21-38 26-38a6 6 0 0 0 0-12"
        />
      </svg>
    </>
  );
};

export const DollarIcon = () => {
  return (
    <>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="26px"
        height="26px"
        viewBox="0 0 36 36"
      >
        <path
          fill="#fff"
          d="M26 21.15a6.91 6.91 0 0 0-4.38-3.32a26 26 0 0 0-2.62-.64V8.12A10.05 10.05 0 0 1 23.86 10a1 1 0 0 0 1.33-1.5A11.75 11.75 0 0 0 19 6.1V3a1 1 0 0 0-2 0v3c-4.4.1-6.83 2.29-7.57 4.18A5.56 5.56 0 0 0 11.66 17A13.2 13.2 0 0 0 17 18.84V28a12.3 12.3 0 0 1-7.14-2.74a1 1 0 1 0-1.37 1.44A14.09 14.09 0 0 0 17 30v3a1 1 0 0 0 2 0v-3c2.82-.19 6.07-1.09 7.3-4.76a5.33 5.33 0 0 0-.3-4.09m-13.21-5.83a3.57 3.57 0 0 1-1.49-4.39c.11-.3 1.23-2.81 5.7-2.93v8.8a10.7 10.7 0 0 1-4.21-1.48m11.61 9.24c-.72 2.14-2.32 3.17-5.4 3.4v-8.73c.64.14 1.3.3 2 .51a5 5 0 0 1 3.19 2.32a3.34 3.34 0 0 1 .21 2.5"
          class="clr-i-outline clr-i-outline-path-1"
        />
        <path fill="none" d="M0 0h36v36H0z" />
      </svg>
    </>
  );
};

export const DeveloperIcon = () => {
  return (
    <>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="26px"
        height="26px"
        viewBox="0 0 1856 1408"
      >
        <path
          fill="#fff"
          d="m585 1143l-50 50q-10 10-23 10t-23-10L23 727q-10-10-10-23t10-23l466-466q10-10 23-10t23 10l50 50q10 10 10 23t-10 23L192 704l393 393q10 10 10 23t-10 23M1176 76L803 1367q-4 13-15.5 19.5T764 1389l-62-17q-13-4-19.5-15.5T680 1332L1053 41q4-13 15.5-19.5T1092 19l62 17q13 4 19.5 15.5T1176 76m657 651l-466 466q-10 10-23 10t-23-10l-50-50q-10-10-10-23t10-23l393-393l-393-393q-10-10-10-23t10-23l50-50q10-10 23-10t23 10l466 466q10 10 10 23t-10 23"
        />
      </svg>
    </>
  );
};

export const EnterpriseSelfIcon = () => {
  return (
    <>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="26px"
        height="26px"
        viewBox="0 0 36 36"
      >
        <path
          fill="#fff"
          d="M10.52 34h-3a1 1 0 0 1-.88-1.44L12.55 21H6a1 1 0 0 1-.85-1.54l10.68-17a1 1 0 0 1 .81-.46h13.43a1 1 0 0 1 .77 1.69L21.78 14h5.38a1 1 0 0 1 .73 1.66l-16.63 18a1 1 0 0 1-.74.34m-1.34-2h.91l14.77-16h-5.27a1 1 0 0 1-.77-1.69L27.88 4H17.19L7.77 19h6.43a1 1 0 0 1 .88 1.44Z"
          class="clr-i-outline clr-i-outline-path-1"
        />
        <path fill="none" d="M0 0h36v36H0z" />
      </svg>
    </>
  );
};

export const FeaturesIcon = () => {
  return (
    <>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="26px"
        height="26px"
        viewBox="0 0 32 32"
      >
        <path
          fill="#fff"
          d="M2 7v8h8V7zm10 0v8h8V7zm10 0v8h8V7zM4 9h4v4H4zm10 0h4v4h-4zm10 0h4v4h-4zM2 17v8h8v-8zm10 0v8h8v-8zm10 0v8h8v-8zM4 19h4v4H4zm10 0h4v4h-4zm10 0h4v4h-4z"
        />
      </svg>
    </>
  );
};

export const VideoPlayIcon = () => {
  return (
    <>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="26px"
        height="26px"
        viewBox="0 0 24 24"
      >
        <g
          fill="none"
          stroke="#fff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="1"
        >
          <circle cx="12" cy="12" r="10" />
          <path d="m10 8l6 4l-6 4z" />
        </g>
      </svg>
    </>
  );
};

export const VirtualClassroomIcon = () => {
  return (
    <>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="26px"
        height="26px"
        viewBox="0 0 36 36"
      >
        <path
          fill="#fff"
          d="M17.9 17.3c2.7 0 4.8-2.2 4.8-4.9s-2.2-4.8-4.9-4.8S13 9.8 13 12.4c0 2.7 2.2 4.9 4.9 4.9m-.1-7.7c.1 0 .1 0 0 0c1.6 0 2.9 1.3 2.9 2.9s-1.3 2.8-2.9 2.8c-1.6 0-2.8-1.3-2.8-2.8c0-1.6 1.3-2.9 2.8-2.9"
          class="clr-i-outline clr-i-outline-path-1"
        />
        <path
          fill="#fff"
          d="M32.7 16.7c-1.9-1.7-4.4-2.6-7-2.5h-.8c-.2.8-.5 1.5-.9 2.1c.6-.1 1.1-.1 1.7-.1c1.9-.1 3.8.5 5.3 1.6V25h2v-8z"
          class="clr-i-outline clr-i-outline-path-2"
        />
        <path
          fill="#fff"
          d="M23.4 7.8c.5-1.2 1.9-1.8 3.2-1.3c1.2.5 1.8 1.9 1.3 3.2c-.4.9-1.3 1.5-2.2 1.5c-.2 0-.5 0-.7-.1c.1.5.1 1 .1 1.4v.6c.2 0 .4.1.6.1c2.5 0 4.5-2 4.5-4.4c0-2.5-2-4.5-4.4-4.5c-1.6 0-3 .8-3.8 2.2c.5.3 1 .7 1.4 1.3"
          class="clr-i-outline clr-i-outline-path-3"
        />
        <path
          fill="#fff"
          d="M12 16.4c-.4-.6-.7-1.3-.9-2.1h-.8c-2.6-.1-5.1.8-7 2.4L3 17v8h2v-7.2c1.6-1.1 3.4-1.7 5.3-1.6c.6 0 1.2.1 1.7.2"
          class="clr-i-outline clr-i-outline-path-4"
        />
        <path
          fill="#fff"
          d="M10.3 13.1c.2 0 .4 0 .6-.1v-.6c0-.5 0-1 .1-1.4c-.2.1-.5.1-.7.1c-1.3 0-2.4-1.1-2.4-2.4c0-1.3 1.1-2.4 2.4-2.4c1 0 1.9.6 2.3 1.5c.4-.5 1-1 1.5-1.4c-1.3-2.1-4-2.8-6.1-1.5c-2.1 1.3-2.8 4-1.5 6.1c.8 1.3 2.2 2.1 3.8 2.1"
          class="clr-i-outline clr-i-outline-path-5"
        />
        <path
          fill="#fff"
          d="m26.1 22.7l-.2-.3c-2-2.2-4.8-3.5-7.8-3.4c-3-.1-5.9 1.2-7.9 3.4l-.2.3v7.6c0 .9.7 1.7 1.7 1.7h12.8c.9 0 1.7-.8 1.7-1.7v-7.6zm-2 7.3H12v-6.6c1.6-1.6 3.8-2.4 6.1-2.4c2.2-.1 4.4.8 6 2.4z"
          class="clr-i-outline clr-i-outline-path-6"
        />
        <path fill="none" d="M0 0h36v36H0z" />
      </svg>
    </>
  );
};

export const VideoKYCIcon = () => {
  return (
    <>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="26px"
        height="26px"
        viewBox="0 0 1920 1536"
      >
        <path
          fill="#fff"
          d="M640 448q0 80-56 136t-136 56t-136-56t-56-136t56-136t136-56t136 56t56 136m1024 384v448H256v-192l320-320l160 160l512-512zm96-704H160q-13 0-22.5 9.5T128 160v1216q0 13 9.5 22.5t22.5 9.5h1600q13 0 22.5-9.5t9.5-22.5V160q0-13-9.5-22.5T1760 128m160 32v1216q0 66-47 113t-113 47H160q-66 0-113-47T0 1376V160Q0 94 47 47T160 0h1600q66 0 113 47t47 113"
        />
      </svg>
    </>
  );
};

export const EdTechIcon = () => {
  return (
    <>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="26px"
        height="26px"
        viewBox="0 0 640 512"
      >
        <path
          fill="#fff"
          d="M160 64c0-35.3 28.7-64 64-64h352c35.3 0 64 28.7 64 64v288c0 35.3-28.7 64-64 64H336.8c-11.8-25.5-29.9-47.5-52.4-64H384v-32c0-17.7 14.3-32 32-32h64c17.7 0 32 14.3 32 32v32h64V64H224v49.1C205.2 102.2 183.3 96 160 96zm0 64a96 96 0 1 1 0 192a96 96 0 1 1 0-192m-26.7 224h53.3c73.7 0 133.4 59.7 133.4 133.3c0 14.7-11.9 26.7-26.7 26.7H26.7C11.9 512 0 500.1 0 485.3C0 411.7 59.7 352 133.3 352"
        />
      </svg>
    </>
  );
};

export const HeartPulseIcon = () => {
  return (
    <>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="26px"
        height="26px"
        viewBox="0 0 24 24"
      >
        <g
          fill="none"
          stroke="#fff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="1"
        >
          <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2c-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z" />
          <path d="M3.22 12H9.5l.5-1l2 4.5l2-7l1.5 3.5h5.27" />
        </g>
      </svg>
    </>
  );
};

export const TeleHealthIcon = () => {
  return (
    <>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="22.75px"
        height="26px"
        viewBox="0 0 448 512"
      >
        <path
          fill="#fff"
          d="M224 256a128 128 0 1 0 0-256a128 128 0 1 0 0 256m-96 55.2C54 332.9 0 401.3 0 482.3C0 498.7 13.3 512 29.7 512h388.6c16.4 0 29.7-13.3 29.7-29.7c0-81-54-149.4-128-171.1V362c27.6 7.1 48 32.2 48 62v40c0 8.8-7.2 16-16 16h-16c-8.8 0-16-7.2-16-16s7.2-16 16-16v-24c0-17.7-14.3-32-32-32s-32 14.3-32 32v24c8.8 0 16 7.2 16 16s-7.2 16-16 16h-16c-8.8 0-16-7.2-16-16v-40c0-29.8 20.4-54.9 48-62v-57.1c-6-.6-12.1-.9-18.3-.9h-91.4c-6.2 0-12.3.3-18.3.9v65.4c23.1 6.9 40 28.3 40 53.7c0 30.9-25.1 56-56 56s-56-25.1-56-56c0-25.4 16.9-46.8 40-53.7zM144 448a24 24 0 1 0 0-48a24 24 0 1 0 0 48"
        />
      </svg>
    </>
  );
};

export const DocumentationIcon = () => {
  return (
    <>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="26px"
        height="26px"
        viewBox="0 0 24 24"
      >
        <path
          fill="#fff"
          d="m22.47 18.82l-1-3.86l-3.15-11.59a1 1 0 0 0-1.22-.71l-3.87 1a1 1 0 0 0-.73-.33h-10a1 1 0 0 0-1 1v16a1 1 0 0 0 1 1h10a1 1 0 0 0 1-1v-8l2.2 8.22a1 1 0 0 0 1 .74a1.15 1.15 0 0 0 .26 0L21.79 20a1 1 0 0 0 .61-.47a1.05 1.05 0 0 0 .07-.71m-16 .55h-3v-2h3Zm0-4h-3v-6h3Zm0-8h-3v-2h3Zm5 12h-3v-2h3Zm0-4h-3v-6h3Zm0-8h-3v-2h3Zm2.25-1.74l2.9-.78l.52 1.93l-2.9.78Zm2.59 9.66l-1.55-5.8l2.9-.78l1.55 5.8Zm1 3.86l-.52-1.93l2.9-.78l.52 1.93Z"
        />
      </svg>
    </>
  );
};

export const BlogIcon = () => {
  return (
    <>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="19.5px"
        height="26px"
        viewBox="0 0 384 512"
      >
        <path
          fill="#fff"
          d="M64 464c-8.8 0-16-7.2-16-16V64c0-8.8 7.2-16 16-16h160v80c0 17.7 14.3 32 32 32h80v288c0 8.8-7.2 16-16 16zM64 0C28.7 0 0 28.7 0 64v384c0 35.3 28.7 64 64 64h256c35.3 0 64-28.7 64-64V154.5c0-17-6.7-33.3-18.7-45.3l-90.6-90.5C262.7 6.7 246.5 0 229.5 0zm56 256c-13.3 0-24 10.7-24 24s10.7 24 24 24h144c13.3 0 24-10.7 24-24s-10.7-24-24-24zm0 96c-13.3 0-24 10.7-24 24s10.7 24 24 24h144c13.3 0 24-10.7 24-24s-10.7-24-24-24z"
        />
      </svg>
    </>
  );
};

export const MailSharpIcon = () => {
  return (
    <>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="15px"
        height="15px"
        viewBox="0 0 24 24"
        style={{ marginRight: "5px" }}
      >
        <path fill="#1acc8d" d="M2 20V4h20v16zm10-7l8-5V6l-8 5l-8-5v2z" />
      </svg>
    </>
  );
};

export const HelpDeskIcon = () => {
  return (
    <>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="15px"
        height="15px"
        viewBox="0 0 26 26"
        style={{ marginRight: "5px" }}
      >
        <g fill="#1acc8d">
          <path d="M14 19.25a1.25 1.25 0 1 1-2.5 0a1.25 1.25 0 0 1 2.5 0" />
          <path
            fill-rule="evenodd"
            d="M12.71 7.065c-.807 0-1.524.24-2.053.614c-.51.36-.825.826-.922 1.308a.75.75 0 1 1-1.47-.297c.186-.922.762-1.696 1.526-2.236c.796-.562 1.82-.89 2.919-.89c2.325 0 4.508 1.535 4.508 3.757c0 1.292-.768 2.376-1.834 3.029a.75.75 0 0 1-.784-1.28c.729-.446 1.118-1.093 1.118-1.749c0-1.099-1.182-2.256-3.008-2.256m0 5.265a.75.75 0 0 1 .75.75v1.502a.75.75 0 1 1-1.5 0V13.08a.75.75 0 0 1 .75-.75"
            clip-rule="evenodd"
          />
          <path
            fill-rule="evenodd"
            d="M15.638 11.326a.75.75 0 0 1-.258 1.029l-2.285 1.368a.75.75 0 1 1-.77-1.287l2.285-1.368a.75.75 0 0 1 1.028.258"
            clip-rule="evenodd"
          />
          <path
            fill-rule="evenodd"
            d="M13 24.5c6.351 0 11.5-5.149 11.5-11.5S19.351 1.5 13 1.5S1.5 6.649 1.5 13S6.649 24.5 13 24.5m0 1c6.904 0 12.5-5.596 12.5-12.5S19.904.5 13 .5S.5 6.096.5 13S6.096 25.5 13 25.5"
            clip-rule="evenodd"
          />
        </g>
      </svg>
    </>
  );
};

export const ChatIcon = () => {
  return (
    <>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="15px"
        height="15px"
        viewBox="0 0 32 32"
        style={{ marginRight: "5px" }}
      >
        <path
          fill="#1acc8d"
          d="M15.985 5.972c-7.563 0-13.695 4.077-13.695 9.106c0 2.877 2.013 5.44 5.147 7.108c-.446 1.48-1.336 3.117-3.056 4.566c0 0 4.016-.266 6.852-3.143c.163.04.332.07.497.106a4.49 4.49 0 0 1-.247-1.443c0-3.393 3.776-6.05 8.6-6.05c3.463 0 6.378 1.376 7.75 3.406c1.168-1.34 1.847-2.893 1.847-4.553c0-5.028-6.132-9.105-13.695-9.105zM27.68 22.274c0-2.79-3.4-5.053-7.6-5.053c-4.195 0-7.598 2.264-7.598 5.054c0 2.79 3.403 5.053 7.6 5.053c.928 0 1.813-.116 2.636-.32c1.573 1.598 3.8 1.745 3.8 1.745c-.953-.804-1.446-1.713-1.694-2.534c1.738-.925 2.856-2.347 2.856-3.944z"
        />
      </svg>
    </>
  );
};

export const PhoneIcon = () => {
  return (
    <>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="15px"
        height="15px"
        viewBox="0 0 24 24"
        style={{ marginRight: "5px" }}
      >
        <path
          fill="#1acc8d"
          d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24c1.12.37 2.33.57 3.57.57c.55 0 1 .45 1 1V20c0 .55-.45 1-1 1c-9.39 0-17-7.61-17-17c0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1c0 1.25.2 2.45.57 3.57c.11.35.03.74-.25 1.02z"
        />
      </svg>
    </>
  );
};

export const InfoIcon = () => {
  return (
    <>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="15px"
        height="15px"
        viewBox="0 0 24 24"
        style={{ marginRight: "5px" }}
      >
        <path
          fill="#1acc8d"
          d="M11.5 16.5h1V11h-1zm.5-6.923q.262 0 .439-.177t.176-.439t-.177-.438T12 8.346t-.438.177t-.177.439t.177.438t.438.177M12.003 21q-1.867 0-3.51-.708q-1.643-.709-2.859-1.924t-1.925-2.856T3 12.003t.709-3.51Q4.417 6.85 5.63 5.634t2.857-1.925T11.997 3t3.51.709q1.643.708 2.859 1.922t1.925 2.857t.709 3.509t-.708 3.51t-1.924 2.859t-2.856 1.925t-3.509.709"
        />
      </svg>
    </>
  );
};
export const MobileMenuCloseIcon = () => {
  return (
    <>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        viewBox="0 0 24 24"
      >
        <path
          fill="none"
          stroke="#000000"
          stroke-dasharray="12"
          stroke-dashoffset="12"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2.3"
          d="M12 12l7 7M12 12l-7 -7M12 12l-7 7M12 12l7 -7"
        >
          <animate
            fill="freeze"
            attributeName="stroke-dashoffset"
            dur="0.3s"
            values="12;0"
          />
        </path>
      </svg>
    </>
  );
};
