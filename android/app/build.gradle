apply plugin: 'com.android.application'
apply plugin: 'com.facebook.react'

// Crashlytics integration is done as part of Firebase now, so it gets
// automatically activated with google-services.json
if (googleServicesEnabled) {
    apply plugin: 'com.google.firebase.crashlytics'
}

// Use the number of seconds/10 since Jan 1 2019 as the versionCode.
// This lets us upload a new build at most every 10 seconds for the
// next ~680 years.
// https://stackoverflow.com/a/38643838
def vcode = (int) (((new Date().getTime() / 1000) - 1546297200) / 10)

android {

    buildFeatures {
        buildConfig = true
    }

    namespace "go.meethour.io"
    compileSdk rootProject.ext.compileSdkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion
    packagingOptions {
        
    pickFirst '**/libjsc.so'
    pickFirst '**/libc++_shared.so'
    pickFirst '**/libjscexecutor.so'
        jniLibs {
            excludes += ['lib/*/libhermes*.so']
        }
    }

    defaultConfig {
        applicationId 'go.meethour.io'
        versionCode vcode
        versionName project.appVersion

        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion

        ndk {
            abiFilters 'armeabi-v7a', 'arm64-v8a', 'x86', 'x86_64'
        }
        multiDexEnabled true
    }

    signingConfigs {
        debug {
            storeFile file('debug.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }
    }

    buildTypes {
        debug {
            buildConfigField "boolean", "GOOGLE_SERVICES_ENABLED", "${googleServicesEnabled}"
            buildConfigField "boolean", "LIBRE_BUILD", "${rootProject.ext.libreBuild}"
        }
        release {
            // Uncomment the following line for singing a test release build.
            //signingConfig signingConfigs.debug
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules-release.pro'
            buildConfigField "boolean", "GOOGLE_SERVICES_ENABLED", "${googleServicesEnabled}"
            buildConfigField "boolean", "LIBRE_BUILD", "${rootProject.ext.libreBuild}"
        }
    }

    sourceSets {
        main {
            java {
                if (rootProject.ext.libreBuild) {
                    srcDir "src"
                    exclude "**/GoogleServicesHelper.java"
                }
            }
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {

    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation("com.facebook.react:react-android")
    debugImplementation 'com.squareup.leakcanary:leakcanary-android:2.13'

    if (!rootProject.ext.libreBuild) {
        implementation 'com.google.android.gms:play-services-auth:19.2.0'

        // Firebase
        //  - Crashlytics
        //  - Dynamic Links
        implementation 'com.google.firebase:firebase-analytics:17.5.0'
        implementation 'com.google.firebase:firebase-crashlytics:17.2.1'
        implementation 'com.google.firebase:firebase-dynamic-links:19.1.0'
    }
    implementation 'com.google.firebase:firebase-ads:23.6.0'
    implementation project(':sdk')
}

gradle.projectsEvaluated {

    // Run React packager
    android.applicationVariants.all { variant ->
        def targetName = variant.name.capitalize()

        def currentRunPackagerTask = tasks.create(
            name: "run${targetName}ReactPackager",
            type: Exec) {
            group = "react"
            description = "Run the React packager."

            doFirst {
                println "Starting the React packager..."

                def androidRoot = file("${projectDir}/../")

                // Set up the call to the script
                workingDir androidRoot

                // Run the packager
                commandLine("scripts/run-packager.sh")
            }

            // Set up dev mode
            def devEnabled = !targetName.toLowerCase().contains("release")

            // Only enable for dev builds
            enabled devEnabled
        }

        def packageTask = variant.packageApplicationProvider.get()

        packageTask.dependsOn(currentRunPackagerTask)
    }

}

if (googleServicesEnabled) {
    apply plugin: 'com.google.gms.google-services'
}
