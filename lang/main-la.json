{"addPeople": {"add": "invitare", "addContacts": "Contactus tua invitare", "contacts": "contactus", "copyInvite": "Exemplum testimonii invitationem", "copyLink": "Effingo foederis link", "copyStream": "Exemplar vivere streaming link", "countryNotSupported": "Nos non sustinere hoc destination adhuc.", "countryReminder": "Vocatis extra US? Confirme te codicem rusticum incipere!", "defaultEmail": "<PERSON><PERSON>", "disabled": "Populus non potes invitare.", "doYouWantToRemoveThisPerson": "Hanc personam vis removere", "failedToAdd": "Deficio addere participantium", "footerText": "Dialling est erret.", "googleCalendar": "Google Calendarium", "googleEmail": "Google Electronicus", "inviteMoreHeader": "Tu solus unus in conventu", "inviteMoreMailSubject": "Iungere {{appName}} foederis", "inviteMorePrompt": "Invita magis populus", "linkCopied": "Link copied ut clipboard", "loading": "Investigatione pro hominibus et Numeros telephonicos", "loadingNumber": "Validating phone numerus", "loadingPeople": "Investigatione ad populum invitare", "loadingText": "Loading ...", "noResults": "Nullae eventus investigationis matching", "noValidNumbers": "Please enter a numerus telephonicus", "outlookEmail": "Outlook Email", "phoneNumbers": "Numeros telephonicos", "searchNumbers": "Numeros telephonicos addere", "searchPeople": "Quaere populum", "searchPeopleAndNumbers": "Quaere hominibus vel addere eorum phone numerus", "searching": "Quaerendo...", "sendWhatsa[pp": "whatsapp", "shareInvite": "Share testimonii invitationem", "shareInviteP": "Share testimonii invitationem cum Password", "shareLink": "Participes testimonii link to invitare alios", "shareStream": "Share the Live Streising link", "sipAddresses": "ad sorbendum praebe oratio", "telephone": "Telephonum: {{number}}", "title": "Invitare ad hunc conventum", "yahooEmail": "Yahoo Email"}, "audioDevices": {"bluetooth": "Bluetooth", "headphones": "Headphones", "none": "Nulla audio cogitationes available", "phone": "Telephono", "speaker": "Orator"}, "audioOnly": {"audioOnly": "Minimum Sed"}, "breakoutRooms": {"actions": {"add": "Addere conclave (Beta)", "autoAssign": "Automate assignare ad conclavia", "close": "<PERSON><PERSON><PERSON>", "join": "<PERSON><PERSON><PERSON>", "leaveBreakoutRoom": "Relinquare conclave", "more": "<PERSON><PERSON><PERSON>", "remove": "Amovere", "rename": "Renominare", "renameBreakoutRoom": "Renominare conclave", "sendToBreakoutRoom": "Mitte participem ad:"}, "breakoutList": "Index conclavium", "buttonLabel": "Conclavia", "defaultName": "Conclave #{{index}}", "hideParticipantList": "Occultare index participum", "mainRoom": "Conclave principale", "notifications": {"joined": "Iungendo ad conclave \"{{name}}\"", "joinedMainRoom": "Iungendo ad conclave principale", "joinedTitle": "Conclavia"}, "showParticipantList": "Ostendere index participum", "showParticipants": "Show participantium", "title": "Conclavia"}, "calendarSync": {"addMeetingURL": "Addere contione link", "confirmAddLink": "Visne ligamentum huic eventui Meet <PERSON><PERSON>?", "error": {"appConfiguration": "Integratio calendarii non recte configuratur.", "generic": "Error inciderunt. Quaeso reprehendo tuum calendarium occasus vel reficiens calendarium experire.", "notSignedIn": "Error occurrit cum authenticating certe videre calendarium. Quaeso reprehendo tuum calendarium occasus et logging in iterum conare."}, "join": "<PERSON><PERSON><PERSON>", "joinTooltip": "Iungere conventum", "nextMeeting": "deinde testimonii", "noEvents": "<PERSON><PERSON>a eventa ventura accedant.", "ongoingMeeting": "ongoing testimonii", "permissionButton": "Open occasus", "permissionMessage": "Permissio Calendarii postulatur videre conventus tuos in app.", "refresh": "Renovare calendarium", "today": "hodie"}, "carmode": {"actions": {"selectSoundDevice": "Lego sonus fabrica"}, "labels": {"buttonLabel": "Car modus", "title": "Car modus", "videoStopped": "Tuum video intercluditur"}}, "chat": {"enter": "Intra chat cubiculum", "error": "Error: epistula tua non missa est. Ratio: {{error}}", "fieldPlaceHolder": "Typus nuntium tuum hic", "message": "<PERSON><PERSON><PERSON>", "messageAccessibleTitle": "{{user}} dicit:", "messageAccessibleTitleMe": "me dicit;", "messageTo": "Privatus nuntius ad {{recipient}}", "messagebox": "Typus nuntium", "nickname": {"popover": "Elige cognomen", "title": "Intrant cognomen ut chat"}, "noMessagesMessage": "Nullae epistulae in conventu adhuc sunt. Colloquium hic incipe!", "privateNotice": "Privatus nuntius ad {{recipient}}", "smileysPanel": "Panel emoji", "tabs": {"chat": "Cha<PERSON>", "polls": "Polls"}, "title": "Curabitur ac Polls", "titleWithPolls": "Curabitur ac Polls", "you": "tu"}, "chromeExtensionBanner": {"buttonText": "Install Chrome Tractus", "close": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dontShowAgain": "Hoc ne iterum ostende mihi", "installExtensionText": "Extensio inaugurare pro Google Calendario et Officio 365 integratione"}, "clickandpledge": {"errorDesc": "Please enter valid Click and pledge Connect GUID. Pro magis details visita - https://connect.clickandpledge.com/", "errorNotification": "Aliquam Click et pignus GUID", "title": "C & P Iungo donationis O<PERSON>", "titlenative": "<PERSON>lick et pignus"}, "connectingOverlay": {"joiningRoom": "Coniungens te ad conventum tuum..."}, "connection": {"ATTACHED": "adnexa", "AUTHENTICATING": "authenticating", "AUTHFAIL": "Defecit authenticas", "CONNECTED": "Coniuncta", "CONNECTING": "Connectens", "CONNFAIL": "Connection defuit", "DISCONNECTED": "Disiungitur", "DISCONNECTING": "Disconnecting", "ERROR": "Error", "FETCH_SESSION_ID": "Sessionem obtinendam id...", "GET_SESSION_ID_ERROR": "Accipe sessionem-id errorem: {{code}}", "GOT_SESSION_ID": "Obtinendae sessionis id... Done", "LOW_BANDWIDTH": "Video pro {{displayName}} ablatum est ad bandwidth servandum"}, "connectionindicator": {"address": "Oratio:", "audio_ssrc": "Audio SSRC:", "bandwidth": "Sed Aestimatur:", "bitrate": "Bitrate:", "bridgeCount": "Servo comiti:", "codecs": "Codecs (A/V);", "connectedTo": "Coniunctus est:", "e2e_rtt": "E2E rtt:", "framerate": "Artus rate:", "less": "Ostende minus", "localaddress": "Locorum inscriptio:", "localaddress_plural": "Inscriptiones locales:", "localport": "Portus localis:", "localport_plural": "Portus localis:", "maxEnabledResolution": "mitte max", "more": "Monstra plura", "packetloss": "Con<PERSON>ci detrimentum;", "participant_id": "Praesent dapibus tincidunt nisl,", "quality": {"good": "bonum", "inactive": "Ut ultrices vestibulum", "lost": "<PERSON><PERSON>", "nonoptimal": "Nonoptimal", "poor": "<PERSON><PERSON><PERSON>"}, "remoteaddress": "Oratio remota:", "remoteaddress_plural": "Inscriptiones remotae:", "remoteport": "Portus remotus;", "remoteport_plural": "<PERSON>us remoti;", "resolution": "Consilium:", "savelogs": "Servo acta", "status": "Nexus:", "transport": "Transvehere:", "transport_plural": "Transportus:", "video_ssrc": "Vide SSRC:"}, "dateUtils": {"earlier": "An<PERSON><PERSON>", "today": "hodie", "yesterday": "heri"}, "deepLinking": {"appNotInstalled": "Utere nostra {{app}} app mobili ut hunc conventum iungere in telephono tuo.", "continueWithBrowser": "Continue cum Pasco", "description": "Nihil factum est? Nos conventum tuum in {{app}} escritorio app emittere conati sumus. Iterum conare aut depone in {app}} interreti app.", "descriptionWithoutWeb": "Nihil factum est? Nos conventum tuum in {{app}} escritorio app emittere conati sumus.", "downloadApp": "Download in app", "ifDoNotHaveApp": "Si non habes in app adhuc;", "ifHaveApp": "Si habes iam rationem app;", "ifYouDontHaveTheAppYet": "Si non habent app adhuc", "joinInApp": "Hoc est usura testimonii iungere app", "joinMeetingWithDesktopApp": "Adiungere testimonii cum Desktop App", "launchMeetingInDesktopApp": "Launch testimonii in Desktop App", "launchWebButton": "Lorem in web", "title": "Tuum conventum deducis in {{app}}...", "tryAgainButton": "Conare in desktop"}, "defaultLink": "e.g. {{url}}", "defaultNickname": "ex. <PERSON><PERSON><PERSON>", "deviceError": {"cameraError": "Deficio accedere camera", "cameraPermission": "Error obtinendae camera permission", "microphoneError": "Deficio accedere te tortor ligula, facilisis", "microphonePermission": "Error obtinendae tortor ligula permission"}, "deviceSelection": {"noPermission": "Permissio non datur", "previewUnavailable": "<PERSON><PERSON><PERSON><PERSON> unavailable", "selectADevice": "Eligere fabrica", "testAudio": "Test sonus ludere"}, "dialOut": {"statusMessage": "nunc est {{status}}"}, "dialog": {"Back": "Retro", "Cancel": "Inrito", "IamHost": "Ego sum hospes", "Ok": "Ok", "Remove": "<PERSON><PERSON>", "Share": "<PERSON><PERSON>", "Submit": "Submitto", "WaitForHostMsg": "Colloquium <b>{{room}}</b> nondum incepit. Si tu es hospes, tunc quaeso authenticitatis. Alio<PERSON>, quaeso, ad exercitum pervenias exspecta.", "WaitForHostMsgWOk": "Colloquium <b>{{room}}</b> nondum incepit. Si igitur hospes es Ok signo authenticitatis insistere velit. Alio<PERSON>, quaeso, ad exercitum pervenias exspecta.", "WaitforModerator": "Placere expectare ad Moderatorem pervenire", "WaitforModeratorOk": "Redire", "WaitingForHost": "Exspecto exercitum .. .", "WaitingForHostTitle": "Exspecto exercitum .. .", "Yes": "<PERSON>a", "accessibilityLabel": {"liveStreaming": "Vivere Stream"}, "add": "Addico", "allow": "<PERSON>itur \"", "alreadySharedVideoMsg": "Alius particeps iam video participationem habens. Hoc colloquium unum tantum ad tempus video commune permittit.", "alreadySharedVideoTitle": "Tantum unum participatur video licet ad tempus", "applicationWindow": "<PERSON><PERSON> fenestra", "authenticationRequired": "Authenticas requiratur", "cameraConstraintFailedError": "Camera tua quibusdam angustiis quaesitis non satisfacit.", "cameraNotFoundError": "Camera non inveni.", "cameraNotSendingData": "Camera accedere non possumus. Quaeso reprehendo si alia applicatio hac arte utens, aliam machinam ex menu uncinis elige vel applicationem reload conare.", "cameraNotSendingDataTitle": "'Non accedere camera", "cameraPermissionDeniedError": "Non permisisti utendi camera. Adhuc colloquium coniungere potes sed alii te non videbunt. Button camera utere in talea inscriptionis ut hanc figere.", "cameraTimeoutError": "Non video fontem incipere. Timeout occurrit!", "cameraUnknownError": "Camera ignota ratione uti non potest.", "cameraUnsupportedResolutionError": "Camera tua solutionem video requiri non sustinet.", "cannotToggleScreenSharingNotSupported": "Non potest toggle screen sharing: non valet.", "close": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "closingAllTerminals": "Claudebant omnes Terminals", "conferenceDisconnectMsg": "Retiacula tua connexionem reprimere velis. Reconnecting in {{seconds}} sec...", "conferenceDisconnectTitle": "divisus es.", "conferenceReloadMsg": "Hoc nos figere conamur. Reconnecting in {{seconds}} sec...", "conferenceReloadTitle": "Infeliciter, aliquid erravit.", "confirm": "Confirmo", "confirmNo": "Non", "confirmYes": "<PERSON>a", "connectError": "Oops! Aliquid erravit et ad colloquium coniungere non potuimus.", "connectErrorWithMsg": "Oops! Aliquid erravit et ad colloquium coniungere non potuimus: {{msg}}", "connecting": "Connectens", "contactSupport": "Contactus firmamentum", "copied": "<PERSON>pied", "copy": "<PERSON><PERSON><PERSON>", "customAwsRecording": "Custom AWS Recording", "deleteCache": "Delere cache", "dismiss": "<PERSON><PERSON><PERSON>", "displayNameRequired": "Salve! Quid est tibi nomen?", "displayUserName": "", "donationCNPLabel": "Intrant Iungo Forma URL vel Widget URL", "donationCNotificationTitle": "Donate per <PERSON> et pignus", "donationLabel": "Intra donationem url Stipendium", "donationNotificationDescription": "Confer nos ad causam nostram defendendam", "donationNotificationTitle": "Donate per Donorbox", "done": "Factum", "e2eeDescription": "Finis-ad-finem Encryption est currently EXPERIMENTALIS. Memento quaeso quod in fine ad finem encryption convertens efficaciter inactivabit servo lateri providens officia ut: notare, vivere effusis et phone participationem. Meminerint etiam conventum solum laborem pro hominibus e navigatoribus coniungendis cum auxilio fluminum insertabilium.", "e2eeLabel": "Admitte finem-ad-finem Encryption", "e2eeWarning": "MONITUM: Non omnes participes huius conventus videntur habere auxilium pro Encryption End-ad-End. Si perfice, videre nec audire te poterunt.", "embedMeeting": "<PERSON><PERSON> testimonii", "enterCdonation": "Exemplum: https://connect.clickandpledge.com/w/Form/283ah-0069-441a-ba0f-d42a9dec9821", "enterDisplayEmail": "Inscriptio ID", "enterDisplayName": "<PERSON><PERSON><PERSON>", "enterDisplayNameToJoin": "Please enter nomen tuum ad iungere", "enterDonation": "Exemplum: https://donorbox.org/donate-an-organisation", "enterMeetingId": "Intra testimonii ID", "enterMeetingPassword": "Intra testimonii Password", "error": "Error", "errorMeetingID": "Add testimonii ID", "errorMeetingPassword": "Adde testimonii Password", "forceMuteEveryoneDialog": "Visne esne omnium tortorem cohibere praeter moderatores? Ipsum tortor ligula non possunt reserare, sed vim mutam semel solves, muta & loqui possunt.", "forceMuteEveryoneElseDialog": "Muta eos tum inactivandi tortor ligula", "forceMuteEveryoneElseTitle": "Numen mutos omnes praeter {{whom}}?", "forceMuteEveryoneElsesVideoDialog": "Cum camera debilis est, solent cameram suam efficere", "forceMuteEveryoneElsesVideoTitle": "Vis mutae camerae omnium praeter {{whom}}?", "forceMuteEveryoneSelf": "te ipsum", "forceMuteEveryoneStartMuted": "Omnis incipit vis transmutari posthac", "forceMuteEveryoneTitle": "Vis muti omnes?", "forceMuteEveryonesVideoDialog": "Certusne esne me vis huius participem claudere video? Ille/Ea/video reserare non poterit", "forceMuteEveryonesVideoTitle": "Vis mutus omnium Video?", "forceMuteParticipantBody": "Numen muta participem.", "forceMuteParticipantButton": "<PERSON><PERSON>", "forceMuteParticipantDialog": "Certusne esne me tortorem huius participem cohibere vis? Ille tortor ligula non poterit reserare, sed vim mutam semel solves, ipse mutum & loqui potest.", "forceMuteParticipantTitle": "Vis muta hunc participem?", "forceMuteParticipantsVideoBody": "Participantes video avertit se et adsueverunt posse rursus vertere", "forceMuteParticipantsVideoButton": "Inactivare camera", "forceMuteParticipantsVideoTitle": "Inactivare camera huius particeps?", "gracefulShutdown": "Ministerium nostrum currently ad sustentationem descendit. Mox iterum conare.", "grantModeratorDialog": "Certus esne hunc participem moderatorem facere vis?", "grantModeratorTitle": "dona moderatoris", "guestUser": "<PERSON><PERSON><PERSON> user", "hangUpLeaveReason": "Hoc testimonii finita est a Moderatore", "hideShareAudioHelper": "Aut ne hoc alternis", "incorrectPassword": "Recta username et password", "incorrectRoomLockPassword": "Recta password", "internalError": "Oops! Aliquid abiit iniuriam. Error sequens occurrit: {{error}}", "internalErrorTitle": "Internus error", "kickMessage": "heus! e obviam remotus es!", "kickParticipantButton": "Aufer User", "kickParticipantDialog": "Certus esne hunc participem amovere vis?", "kickParticipantTitle": "Aufer hunc participem?", "kickTitle": "heus! Remota sunt de conventu", "liveStreaming": "Vivamus streaming", "liveStreamingDisabledBecauseOfActiveRecordingTooltip": "Non potest cum memoria agit", "liveStreamingDisabledForGuestTooltip": "Hospes non potest incipere vivere streaming.", "liveStreamingDisabledTooltip": "<PERSON><PERSON> consequat tincidunt lacus.", "localUserControls": "Locus user controls", "lockMessage": "Ad colloquium incassum cohibere.", "lockRoom": "Add occurrens $ t (lockRoomPasswordUppercase)", "lockTitle": "<PERSON><PERSON><PERSON><PERSON> defuit", "login": "<PERSON><PERSON>", "logoutQuestion": "Visne vero colloquio concludere ac prohibere?", "logoutTitle": "<PERSON><PERSON>", "maxUsersLimitReached": "Terminus ad maximum numerum participantium ventum est. Colloquium plenum est. Please contact the meeting owner or try again later!", "maxUsersLimitReachedTitle": "Maxime terminus participantium pervenit", "meetHourRecording": "Horam Occursum Recordatio", "meetingID": "occurrens ID", "meetingIDandPassword": "Intra testimonii id quod Password", "meetingPassword": "<PERSON><PERSON><PERSON>", "messageErrorApi": "oops! Ali<PERSON> erravit", "messageErrorInvalid": "Aliquam Credentials", "messageErrorNotModerator": "Oops! non huius conventus moderatorem", "messageErrorNull": "Username vel Password est inanis", "micConstraintFailedError": "Tortor ligula non satiat quibusdam angustiis requisitis.", "micNotFoundError": "Tortor ligula non inveni.", "micNotSendingData": "Vade ad computatrum tuum occasus ut unmute tuum mic et aptet suo gradu", "micNotSendingDataTitle": "Tua mic est transmutari tua ratio occasus", "micPermissionDeniedError": "Non permisisti ut tortor ligula utendi tuo. Adhuc colloquium coniungere potes sed alii te non audiunt. Button camera utere in talea inscriptionis ut hanc figere.", "micTimeoutError": "Non potuit initium audio principium. Timeout occurrit!", "micUnknownError": "Tortor ignota ratione uti non potest.", "muteEveryoneDialog": "Certus esne omnes mutum esse vis? Illas immutare non poteris, sed se inmutare aliquando possunt.", "muteEveryoneElseDialog": "<PERSON><PERSON> obmutato, eos immutare non poteris, sed se aliquando immutare possunt.", "muteEveryoneElseTitle": "<PERSON><PERSON> omnes praeter {{whom}}?", "muteEveryoneElsesVideoDialog": "Cum camera debilis est, eam retro vertere non poteris, sed aliquando eam vertere potest.", "muteEveryoneElsesVideoTitle": "Inactivare cameram omnium nisi {{whom}}?", "muteEveryoneSelf": "te ipsum", "muteEveryoneStartMuted": "Omnis incipit transmutari posthac", "muteEveryoneTitle": "Mutemus omnes?", "muteEveryonesVideoDialog": "Visne certe disable cameram omnium? Retorquere eam non poteris, sed aliquando retro vertere possunt.", "muteEveryonesVideoDialogOk": "inactivare", "muteEveryonesVideoTitle": "Inactivare omnium camera?", "muteParticipantBody": "<PERSON>las immutare non poteris, sed se inmutare aliquando possunt.", "muteParticipantButton": "<PERSON><PERSON>", "muteParticipantDialog": "Certus esne hunc participem mutum esse vis? Illas immutare non poteris, sed se inmutare aliquando possunt.", "muteParticipantTitle": "Mutum hoc participem?", "muteParticipantsVideoBody": "Cameram retro vertere non poteris, sed aliquando eam retorquere possunt.", "muteParticipantsVideoButton": "Inactivare camera", "muteParticipantsVideoDialog": "Visne certe hanc participem cameram averte? Cameram retro vertere non poteris, sed aliquando eam retorquere possunt.", "muteParticipantsVideoTitle": "Inactivare camera huius particeps?", "noDropboxToken": "Non valet Dropbox indicium", "noScreensharingInAudioOnlyMode": "Non solum modus in audio screensharing", "password": "Password", "passwordLabel": "Conventus a Moderatore clausum est. Please enter the $t(lockRoomPassword) to join.", "passwordNotSupported": "Ponere conventum $t(lockRoomPassword) non valet.", "passwordNotSupportedTitle": "$ T (lockRoomPasswordUppercase) non praebetur", "passwordRequired": "$ T (lockRoomPasswordUppercase) requiratur", "permissionCameraRequiredError": "Camera permissio requiritur ut colloquia cum video participare. Quaeso da in Occasus", "permissionErrorTitle": "permission requiratur", "permissionMicRequiredError": "Microphone permissio requiritur ut colloquia cum audio participare. Quaeso da in Occasus", "popupError": "Navigatorium tuum pop-up fenestras ex hoc situ obstruit. Quaeso, ut pop-ups in occasus securitatis navigatri tui et iterum conare.", "popupErrorTitle": "Pop-sursum clausus", "readMore": "plus", "recording": "Recordatio", "recordingDisabledBecauseOfActiveLiveStreamingTooltip": "Non potest vivere dum amnis active", "recordingDisabledForGuestTooltip": "Hospes non potest incipere tabulas.", "recordingDisabledTooltip": "Satus memoria erret.", "rejoinNow": "Nunc incipe", "remoteControlAllowedMessage": "{{user}} petitionem tuam remotam imperium accepit!", "remoteControlDeniedMessage": "{{user}} petitionem tuam remotum imperium repudiavit!", "remoteControlErrorMessage": "Error occurrit cum licentiam a {{user}} postulare conatur!", "remoteControlRequestMessage": "Dabisne {{user}} e longinquo escritorio tuo cohibere?", "remoteControlShareScreenWarning": "Nota quod si urgeas \"Permitte\" te in screen communicabis!", "remoteControlStopMessage": "Remota potestate sessionis finita est!", "remoteControlTitle": "Desktop remote control", "remoteUserControls": "User remotis imperium usoris {{username}}", "removeCDonation": "Click ac pignus guid remotum", "removeCDonationD": "Donatio nexus remotus est feliciter", "removeDonation": "Donorbox donationis link remota", "removeDonationD": "Donatio nexus remotus est feliciter", "removePassword": "Remove $ T (lockRoomPassword)", "removeSharedVideoMsg": "Certusne esne te videre commune removere tuum?", "removeSharedVideoTitle": "Remove participatur video", "reservationError": "Reservatio ratio error", "reservationErrorMsg": "Error code: {{code}}, relatum: {{msg}}", "restartInitiatedBecauseOfBridgeFailure": "<PERSON><PERSON> initiatus propter defectum pontis", "retry": "Retry", "revokeModeration": "Revocare user ut Moderator?", "revokeModerationTitle": "<PERSON><PERSON><PERSON>", "screenSharingAudio": "Share Audio", "screenSharingFailed": "Oops! <PERSON><PERSON> erravit, non potuimus incipere velum communicationem!", "screenSharingFailedTitle": "Screen participatio defecit!", "screenSharingPermissionDeniedError": "Oops! Aliquid abiit iniuriam cum permissionum participatione screen. Quaeso reload et iterum conare.", "screenSharingUser": "{{displayName}} est Currently Socius screen", "sendPrivateMessage": "Tu nuper nuntium privatum accepisti. Nonne privatim ad illud respondere voluisti, vel nuntium tuum ad coetum mittere vis?", "sendPrivateMessageCancel": "Mitte ad coetus", "sendPrivateMessageOk": "<PERSON>tte secreto", "sendPrivateMessageTitle": "Secreto mittere?", "serviceUnavailable": "Service unavailable", "sessTerminated": "Vocationem terminabitur", "sessionRestarted": "Vocate ponte<PERSON> restarted", "shareAudio": "continue", "shareAudioTitle": "Quomodo participes audio?", "shareAudioWarningD1": "vos postulo ut prohibere screen communicationem antequam communicare audio tuum.", "shareAudioWarningD2": "vos postulo ut sileo vestri screen participationem et optionem \"communis audio\" deprime.", "shareAudioWarningH1": "Si vis communicare sicut audio;", "shareAudioWarningTitle": "Vos postulo ut prohibere screen communicationem antequam communicatio audio", "shareMediaWarningGenericH2": "Si vis participes tui screen et audio", "shareScreenWarningD1": "vos postulo ut prohibere audio participationem tuam antequam communicans screen.", "shareScreenWarningD2": "debes prohibere communicationem audio, incipere velum communicare et \"communicare audio\" optionem.", "shareScreenWarningH1": "Si vis communicare iustus vestri screen:", "shareScreenWarningTitle": "Vos postulo ut prohibere audio sharing ante communicantes vestri screen", "shareVideoLinkError": "Quaeso te praebere nexum rectam Youtube.", "shareVideoTitle": "Participes YouTube", "shareYourScreen": "Share your screen", "shareYourScreenDisabled": "Screen communicatio debilis.", "shareYourScreenDisabledForGuest": "Hospes non tegumentum communicare.", "startLiveStreaming": "Vivamus amnis + Recording", "startRecording": "<PERSON>tus Book", "startRemoteControlErrorMessage": "Error occurrit dum incipit sessionem remotam potestatem!", "stopLiveStreaming": "Stoping", "stopRecording": "Subsisto memoria", "stopRecordingWarning": "Certusne esne tabulas obsistere?", "stopStreamingWarning": "Certus esne te vivere effusis prohibere?", "streamKey": "Vivamus stream key", "switchInProgress": "In cursu cursualis.", "thankYou": "<PERSON><PERSON><PERSON> tibi ago pro utendo {{appName}}!", "token": "indicium", "tokenAuthFailed": "<PERSON><PERSON><PERSON>, hanc vocationem iungere non licet.", "tokenAuthFailedTitle": "Defecit authenticas", "transcribing": "Transcribere", "unforceMuteEveryoneDialog": "Certusne esne tortor ligulam omnium reserare vis? possunt unmute & loqui.", "unforceMuteEveryoneElseDialog": "Mutos solve eos et permittant tortor ligula", "unforceMuteEveryoneElseTitle": "Undo Force Mute omnes praeter {{whom}}?", "unforceMuteEveryoneElsesVideoDialog": "Cum camera est enable, cameram suam efficere poterunt", "unforceMuteEveryoneElsesVideoTitle": "Admitte cu<PERSON>vis camerae nisi {{whom}}?", "unforceMuteEveryoneSelf": "te ipsum", "unforceMuteEveryoneTitle": "Mutum undo vis omnium tortor ligula?", "unforceMuteEveryonesVideoDialog": "Certusne es vis ut video omnium reserare?", "unforceMuteEveryonesVideoTitle": "Admitte omnium camera?", "unforceMuteParticipantBody": "Mute participem solve.", "unforceMuteParticipantButton": "Undo Force Mutus", "unforceMuteParticipantDialog": "Certus esne vis ut video huius participem reserare?.", "unforceMuteParticipantTitle": "Mute hunc participem Force solve?", "unforceMuteParticipantsVideoBody": "Participes video in se convertentur et iterum redire poterunt", "unforceMuteParticipantsVideoButton": "Admitte camera", "unforceMuteParticipantsVideoTitle": "Re<PERSON><PERSON><PERSON> huius particeps video?", "unlockRoom": "Remove foederis $ T (lockRoomPassword)", "user": "User", "userIdentifier": "Id identifier", "userPassword": "User Password", "videoLink": "Video Link", "viewUpgradeOptions": "View upgrade optiones", "viewUpgradeOptionsContent": "Ut illimitata accessum ad features premium sicut memorias, transcriptiones, RTMP Streaming & ultra, opus est ut consilium tuum upgrade.", "viewUpgradeOptionsTitle": "Invenisti premium pluma!", "yourEntireScreen": "Totum screen"}, "documentSharing": {"title": "Liveepad"}, "donorbox": {"errorDesc": "Please enter expeditionem URL Donorbox validam. Pro magis details visita - www.donorbox.org", "errorNotification": "Aliquam Donorbox URL", "title": "Add DonorBox Stipendium URL", "titlenative": "Donorbox"}, "e2ee": {"labelToolTip": "Audio et Video Communication in hac vocatione finis est encrypted"}, "embedMeeting": {"title": "Hoc conventu Embed"}, "feedback": {"average": "Mediocris", "bad": "<PERSON><PERSON>", "detailsLabel": "Dic plura de eo.", "good": "bonum", "rateExperience": "Rate testimonii experientiam tuum", "star": "<PERSON><PERSON>", "veryBad": "Ipsum Bad", "veryGood": "Good"}, "giphy": {"giphy": "<PERSON><PERSON><PERSON>", "noResults": "Eventus inventus :(", "search": "Quaerere GIPHY"}, "helpView": {"header": "Auxilium centrum"}, "incomingCall": {"answer": "Exaudio", "audioCallTitle": "advenientis vocationem", "decline": "<PERSON><PERSON><PERSON>", "productLabel": "ex Occursum hora", "videoCallTitle": "Advenientis vocationem video"}, "info": {"accessibilityLabel": "Monstrare info", "addPassword": "Add $ t (lockRoomPassword)", "cancelPassword": "Cancel $ t (lockRoomPassword)", "conferenceURL": "Link:", "copyNumber": "Exemplar numerus", "country": "Patria", "dialANumber": "Ut conventum tuum iungas, unum ex his numeris horologio et clavum ingredere.", "dialInConferenceID": "ACUS:", "dialInNotSupported": "<PERSON><PERSON><PERSON>, dialing in nunc non valet.", "dialInNumber": "Dial-in:", "dialInSummaryError": "Error ducens horologio in info nunc. Mox iterum conare.", "dialInTollFree": "Free vectigal", "genericError": "Whoops, aliquid erravit.", "inviteLiveStream": "Ut rivum huius conventus videre, preme hanc connexionem: {{url}}", "invitePhone": "Ad phone loco iungere, hoc sonum: {{number}},,{{conferenceID}}#", "invitePhoneAlternatives": "Quaeris alium numerum dial?\nVide occurrens horologio in numeris: {{url}}\n\n\nSi etiam dialing-per telephonum cubiculum, coniunge sine coniunctione ad auditionem: {{silentUrl}}", "inviteSipEndpoint": "Ut electronica HAUSTUS iungas, hoc incipe: {{sipUri}}", "inviteTextiOSInviteUrl": "Preme nexum sequentem iungere: {{inviteUrl}}.", "inviteTextiOSJoinSilent": "Si dialing-in per telephonum cella, hac nexu utere ut sine connectens auditionem: {{silentUrl}}.", "inviteTextiOSPersonal": "{{name}} te ad conventum invitat.", "inviteTextiOSPhone": "Ad per telephonum iungere, hoc numero utere: {{number}},,{{conferenceID}}#. Si alium numerum quaeris, haec plenitudo est: {{didUrl}}.", "inviteURLFirstPartGeneral": "Invitatus es ut contionem iungeres.", "inviteURLFirstPartPersonal": "{{name}} te ad conventum invitat.", "inviteURLSecondPart": "Coniunge conventui:\n{{url}}", "label": "Alternis-in info", "liveStreamURL": "live stream;", "moreNumbers": "Plures numeri", "noNumbers": "Nulla dial in numeris.", "noPassword": "<PERSON><PERSON><PERSON>", "noRoom": "<PERSON>ullus locus ad horologium in intonsum definitum est.", "numbers": "Horologio", "password": "$ T (lockRoomPasswordUppercase):", "sip": "HAUSTUS oratio", "title": "<PERSON><PERSON>", "tooltip": "Share link and dial-in info hoc conventu"}, "inlineDialogFailure": {"msg": "<PERSON><PERSON> offendimus.", "retry": "Iterum conare", "support": "Subsidium", "supportMsg": "Si hoc facit, rium"}, "inviteDialog": {"alertText": "Defecit aliquos participes invitare.", "header": "invitare", "searchCallOnlyPlaceholder": "Intra numerus telephonicus", "searchPeopleOnlyPlaceholder": "Search for participantium", "searchPlaceholder": "Participem vel numerus telephonicus", "send": "<PERSON><PERSON>"}, "jitsiHome": "{{logo}} Logo, nexus cum protocollo", "keyboardShortcuts": {"focusLocal": "Focus in vestri video", "focusRemote": "Focus in alia persona video", "fullScreen": "View seu exire plenam screen", "keyboardShortcuts": "Compendia Compendia", "localRecording": "Monstra vel celare loci memoria controls", "mute": "Mutum vel unmute tuum tortor ligula, facilisis", "pushToTalk": "Ventilabis loqui", "raiseHand": "Leva manum tuam vel summittere", "showSpeakerStats": "Ostende speaker stats", "toggleChat": "Aperi vel claudere chat", "toggleFilmstrip": "Ostende vel celare video ailnthubms", "toggleParticipantsPane": "Monstra vel abscondere participantium pane", "toggleScreensharing": "Switch inter camera et screen sharing", "toggleShortcuts": "Monstra vel celare claviaturae", "videoMute": "Satus vel prohibere camera", "videoQuality": "<PERSON>uro qualis vocatio"}, "liveChatView": {"header": "24x7 Ago Support"}, "liveStreaming": {"addStream": "Destination Add", "busy": "In effusis opibus liberandis laboramus. Aliquam paulisper.", "busyTitle": "Omnes streamers sunt currently occupatus", "changeSignIn": "rationum Cie.", "choose": "Elige amnis vivere", "chooseCTA": "Eligendi effusis optio. Nunc initium es pro {{email}}.", "enterLinkedInUrlWithTheKey": "Intra LinkedIn url cum clavis", "enterStreamKey": "Intra YouTube rivum clavem hic vivam.", "enterStreamKeyFacebook": "Intra tuum Facebook hic clavis amnis vive.", "enterStreamKeyInstagram": "Enter your Instagram live key stream here.", "enterStreamKeyYouTube": "Intra tuam {{youtube}} clavem amnis hic vivas.", "error": "Vivamus varius vestibulum incassum. Quaeso, iterum conare.", "errorAPI": "Error occurrit cum accessu tuo YouTube radiophonicus. Aliquam colligationem iterum.", "errorLiveStreamNotEnabled": "Vivere Gratis non est enabled in {{email}}. Quaeso fac ut vivant effusis vel stipes in rationem cum vivo effusione para.", "expandedOff": "Vivum effusis constiterit", "expandedOn": "Conventus nunc in YouTube confluebat.", "expandedPending": "Vivamus sit amet gravida erat.", "failToStartAutoLiveStreaming": "Deficient incipere Auto Vivere Gratis", "failToStartAutoRecording": "Deficient incipere Auto Book", "failedToStart": "Vivere Gratis defecit incipere", "getStreamKeyManually": "Rivos vivos arcessere non poteramus. Tuam vivam vivam clavem e YouTube accipias.", "googlePrivacyPolicy": "Google Privacy Policy", "invalidStreamKey": "Vivere potest clavis amnis falsa.", "limitNotificationDescriptionNative": "Tuus effusis limitibus ad {{limit}}} min finietur. Nam infinitus effusis experiri {{app}}.", "limitNotificationDescriptionWeb": "Ob altitudinem tuam postulant effusis limitatur ad {{limit}}} min. Pro infinitis effusis experiri <a href={{url}} rel='noopener noreferrer' target='_blank'>{{app}}</a>.", "makeSureYouHaveEnoughStorageAvailableOnYourAccount": "Fac tibi satis repono promptum in ratione vestra.", "note": "<PERSON>a", "off": "Vivere <PERSON>is c<PERSON>", "offBy": "{{name}} constitit vivam effusionem", "on": "live streaming started", "onBy": "{{name}} incepit vivere streaming", "pending": "<PERSON><PERSON>...", "pleaseContactSupportForAssistance": "Quaeso auxilium contactus auxilium.", "serviceName": "Vivere Gratis servitium", "signIn": "Sign apud Googles", "signInCTA": "Sign in or enter your key stream from YouTube.", "signOut": "Exire", "signedInAs": "Vos es currently signati in ut:", "start": "Recordatio + live Stream", "startService": "Satus Service", "streamIdHelp": "Quid hoc est?", "unavailableTitle": "live streaming unavailable", "youtubeTerms": "YouTube termini muneris"}, "lobby": {"admit": "Admitte", "admitAll": "admitte omnia", "allow": "<PERSON>itur \"", "backToKnockModeButton": "<PERSON><PERSON><PERSON>, quaerere pro iungere", "dialogTitle": "VESTIBULUM modus", "disableDialogContent": "VESTIBULUM modus est currently enabled. Pluma hoc efficit ut participes inutiles conventum tuum iungere non possint. Visne disable eam?", "disableDialogSubmit": "inactivare", "emailField": "Nulla inscriptio electronica", "enableDialogPasswordField": "Set password (libitum)", "enableDialogSubmit": "Admitte", "enableDialogText": "VESTIBULUM modus sinit te conventum tuum custodire solum permittens homines intrare post approbationem formalem a moderatore.", "enterPasswordButton": "Intra testimonii password", "enterPasswordTitle": "Intra password ut iungere testimonii", "invalidPassword": "Ali<PERSON>m password", "joinRejectedMessage": "Rogatio adiuncta a moderatore repudiata est.", "joinTitle": "<PERSON><PERSON><PERSON> testimonii", "joinWithPasswordMessage": "<PERSON><PERSON> iungere cum password, sis opperiri...", "joiningMessage": "Conventum iunges ut quam primum aliquis tuam petitionem acceperit", "joiningTitle": "Postulantes iungere conventum...", "joiningWithPasswordTitle": "Coniungens cum password...", "knockButton": "Ask ad Join", "knockTitle": "Aliquis vult iungere conventum", "knockingParticipantList": "Participem pulsans album", "nameField": "Intra nomen tuum", "notificationLobbyAccessDenied": "{{targetParticipantName}} reiectus est ut iungere ab {{originParticipantName}}", "notificationLobbyAccessGranted": "{{targetParticipantName}} iungere concessum est a {{originParticipantName}}", "notificationLobbyDisabled": "VESTIBULUM erret ab {{originParticipantName}}", "notificationLobbyEnabled": "VESTIBULUM datum est ab {{originParticipantName}}", "notificationTitle": "VESTIBULUM", "passwordField": "Intra testimonii password", "passwordJoinButton": "<PERSON><PERSON><PERSON>", "reject": "<PERSON><PERSON><PERSON>", "rejectAll": "Repudiare omnia", "toggleLabel": "Admitte VESTIBULUM"}, "localRecording": {"clientState": {"off": "Off", "on": "In", "unknown": "Ignotum"}, "dialogTitle": "Locus Record Imperium", "duration": "<PERSON><PERSON><PERSON>", "durationNA": "N / est", "encoding": "modum translitterandi", "label": "<PERSON>r", "labelToolTip": "Locus memoria versatur", "localRecording": "Locus Record", "me": "Me", "messages": {"engaged": "Locus Aide versatus.", "finished": "Recordatio sessionis {{token}} complevit. Mitte tabella memoriae moderatori.", "finishedModerator": "Recordatio sessionis {{token}} complevit. Locus semita servata est. Rogamus ergo alios participes ad suas tabulas submittere.", "notModerator": "Non es moderator. Non potes incipere vel desinere loci memoria."}, "moderator": "Moderator", "no": "Non", "participant": "Participem", "participantStats": "Participem Stats", "sessionToken": "<PERSON><PERSON><PERSON>", "start": "<PERSON>tus Book", "stop": "Subsisto Book", "yes": "<PERSON>a"}, "lockRoomPassword": "Password", "lockRoomPasswordUppercase": "Password", "lonelyMeetingExperience": {"button": "alios invitare", "youAreAlone": "Tu solus unus in conventu"}, "me": "me", "notify": {"OldElectronAPPTitle": "Securitatis vulnerabilitas!", "connectedOneMember": "{{name}} <PERSON><PERSON> i<PERSON>ur", "connectedThreePlusMembers": "{{name}} et {{count}} alii conventus iuncti sunt", "connectedTwoMembers": "{{first}} et {{second}} conventum iungebat", "disconnected": "disiungitur", "focus": "Colloquium focus", "focusFail": "{{component}} non praesto - Retry in {{ms}} sec", "grantedTo": "Moderator iura {ad}} concessa!", "groupTitle": "Acta Vicimediorum", "hostAskedUnmute": "Ho<PERSON><PERSON> vellem te unmute", "invitedOneMember": "{{name}} vocatus est", "invitedThreePlusMembers": "{{name}} et {{count}} alii vocati sunt", "invitedTwoMembers": "{{first}} et {{second}} vocati sunt", "kickParticipant": "{{kicked}} sublatum est ab {{kicker}}", "me": "Me", "moderationInEffectCSDescription": "Suscita manum tuam, si vis participes tui video", "moderationInEffectCSTitle": "Contentus participatio erret a moderatore", "moderationInEffectDescription": "Suscipe manum, quaeso, si vis loqui", "moderationInEffectTitle": "Tortor tortor a moderatore mutatur", "moderationInEffectVideoDescription": "Quaeso manum tuam si vis esse visibilis video", "moderationInEffectVideoTitle": "Video transmutari a moderatore", "moderationRequestFromModerator": "Ho<PERSON><PERSON> vellem te unmute", "moderationRequestFromParticipant": "<PERSON>ult loqui", "moderationStartedTitle": "<PERSON><PERSON><PERSON><PERSON> co<PERSON>i", "moderationStoppedTitle": "Sobrietas", "moderationToggleDescription": "ab {{participantDisplayName}}", "moderator": "Moderatoris iura concessa sunt!", "muted": "Colloquium incepisti mutum.", "mutedRemotelyDescription": "Semper mutus potes cum loqui paratus es. Revocatur mutus, cum facta es ad strepitum a conventu.", "mutedRemotelyTitle": "Mutatus es ab {{participantDisplayName}}!", "mutedTitle": "Tu transmutari!", "newDeviceAction": "<PERSON><PERSON>", "newDeviceAudioTitle": "Novum audio fabrica detecta", "newDeviceCameraTitle": "Nova camera deprehensa", "oldElectronClientDescription1": "Videris uti vetere versione Clientis Occursum hora, quae vulnerabilitates securitatis scit. Confirme te update ad nostram", "oldElectronClientDescription2": "tardus constructum", "oldElectronClientDescription3": "nunc!", "passwordRemovedRemotely": "$ T (lockRoomPasswordUppercase) remota ab alio participem", "passwordSetRemotely": "$ T (lockRoomPasswordUppercase) set ab alio participem", "raiseHandAction": "Excita manus", "raisedHand": "{{name}} loqui velim.", "reactionSounds": "inactivare sonos", "reactionSoundsForAll": "Inactivare sonos pro omnibus", "screenShareNoAudio": "Capsulae auditionis participes non in tentoriis delectu fenestrae coercitae sunt.", "screenShareNoAudioTitle": "Ratio audio participare non potuit!", "somebody": "quis", "startSilentDescription": "Recipe conventum ad enable audio", "startSilentTitle": "Coniunxisti cum nullo audio output!", "suboptimalBrowserWarning": "Timemus conventum vestrum experientiam magnam hic non futuram esse. Quaerimus vias emendandi hoc, sed usque dum una ex <a href='{{recommendedBrowserPageLink}}' target='_blank'> navigatores plene confirmaverunt</a>.", "suboptimalExperienceTitle": "Pasco <PERSON>", "unmute": "Unmute", "videoMutedRemotelyDescription": "Semper rursus vertere potes.", "videoMutedRemotelyTitle": "Camera tua ab {{participantDisplayName}} debilitata est!"}, "participantsPane": {"actions": {"allow": "Attendees ad patitur:", "askUnmute": "<PERSON><PERSON> ut unmute", "blockEveryoneMicCamera": "Obstructionum omnium microform et camera", "breakoutRooms": "Breakout rooms", "forceMute": "For Mutus audio", "forceMuteAll": "Vis muta omnia", "forceMuteAllVideo": "Opprimere video muta omnes", "forceMuteEveryoneElse": "Vis muti omnes", "forceMuteEveryoneElseVideo": "Vis muti omnes Video", "forceMuteVideo": "Video muta Video", "invite": "invitare aliquem", "mute": "<PERSON><PERSON>", "muteAll": "muta omnia", "muteEveryoneElse": "Mutum quisque aliud", "startModeration": "Unmute se vel satus video", "stopEveryonesVideo": "Subsisto omnium video", "stopVideo": "Subsisto video", "unForceMute": "muta vis undo Audio", "unForceMuteAll": "Undo Force muta omnia", "unForceMuteVideo": "Undo vis muta Video", "unblockEveryoneMicCamera": "Unblock omnium mic et camera", "unforceMuteAllVideo": "Undo Video Video muta omnes", "unforceMuteEveryoneElse": "<PERSON>tus abrogare vis", "unforceMuteEveryoneElseVideo": "Undo vim mutas omnes Video"}, "close": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "header": "Praesent", "headings": {"lobby": "VESTIBULUM", "participantsList": "Participantium congressus ({{count}})", "waitingLobby": "VESTIBULUM exspectans ({{count}})"}, "search": "Quaerere participantium"}, "passwordDigitsOnly": "Usque ad {{number}} numeri", "passwordSetRemotely": "set ab alio participem", "polls": {"answer": {"skip": "<PERSON><PERSON>", "submit": "Submitto"}, "by": "Per {{ name }}", "create": {"addOption": "Addere Bene", "answerPlaceholder": "Optio {{index}}", "cancel": "Inrito", "create": "<PERSON><PERSON><PERSON>", "pollOption": "Optio suffragia {{index}}", "pollQuestion": "Poll Quaestio", "questionPlaceholder": "Quaeritur quaestio", "removeOption": "Aufer optio", "send": "<PERSON><PERSON>"}, "notification": {"description": "Patefacio capitum tab ad suffragium", "title": "Novum capita huic conventui addita sunt"}, "results": {"changeVote": "Mutare suffragium", "empty": "Nulla suffragia in conventu adhuc sunt. Hic initium capitum!", "hideDetailedResults": "Details celare", "showDetailedResults": "<PERSON><PERSON> singula", "vote": "Suffragium"}}, "poweredby": "© Occurrit Hour LLC", "prejoin": {"alreadyOneConferenceIsRunningInBackground": "Iam unum colloquium in campo currit.", "audioAndVideoError": "Audio et video errorem;", "audioDeviceProblem": "Difficultas cum audio fabrica", "audioOnlyError": "Audio errorem;", "audioTrackError": "Non potuit creare semita audio.", "callMe": "me voca", "callMeAtNumber": "Me ad hunc numerum voca;", "calling": "Vocatio", "configuringDevices": "Vestibulum iaculis.", "connectedWithAudioQ": "Cum audio coniunctus es?", "connection": {"good": "Conexio interretialem tuam bonum spectat!", "nonOptimal": "Conexio tua non bene", "poor": "<PERSON> pauper conexio"}, "connectionDetails": {"audioClipping": "Exspectamus auditionem tuam ut curta sint.", "audioHighQuality": "Exspectamus auditionem vestram excellentem qualitatem habere.", "audioLowNoVideo": "Exspectamus qualitatem audio tuam esse humilem et nullam video.", "goodQuality": "Awesome! Media tua qualitas magna futura est.", "noMediaConnectivity": "Viam invenire non potuimus ad connexionem instrumentorum constituendam pro hoc experimento. Hoc est typice causatum a firewall vel NAT.", "noVideo": "Exspectamus ut video te terribilem fore.", "undetectable": "Si adhuc in navigatro facere non potes, commendamus te fac oratores tuos, tortor ligula ac camera, recte constituere, quod iura navigatro tuo ad microphone et camera utendi concessis, et versionem navigatri usque ad -date. Si vocationis molestiam adhuc habes, applicatione elit interreti debes.", "veryPoorConnection": "Exspectamus vocationem vestram qualis est vere terribilis.", "videoFreezing": "Exspectamus video tuum ut congelatur, nigrescat, et iniciatur.", "videoHighQuality": "Exspectamus tuum video habere bonam qualitatem.", "videoLowQuality": "Exspectamus tuum video habere qualitatem humilem in terminis ratis rate et resolutionis.", "videoTearing": "Te exspectamus ut video pixelated vel artificia visualia."}, "copyAndShare": "Effingo & participes testimonii link", "dashboard": "Ashboardday", "daysAgo": "{{daysCount}} days ago", "dialInMeeting": "Alternis in conventu", "dialInPin": "Dial in conventu ac PIN codicem intrant:", "dialing": "Dialing", "doNotShow": "Aut ne hoc screen iterum", "enterMeetingIdOrLink": "Intra occurrens id vel Link", "errorDialOut": "Non e dial", "errorDialOutDisconnected": "Non e dial. Disiungitur", "errorDialOutFailed": "Non e dial. Vocationem defecit", "errorDialOutStatus": "Error questus de horologio status", "errorMissingEmail": "Please enter vestri email ut iungere conventu", "errorMissingName": "Please enter nomen tuum ad conventu", "errorNameLength": "Please enter atleast III epistolas in nomine tuo", "errorStatusCode": "Error dialing sicco, status Code: {{status}}", "errorValidation": "Numerus sanatio defecit", "features": "Features", "guestNotAllowedMsg": "Hospes non licet hunc conventum iungere", "iWantToDialIn": "Volo horologio", "initiated": "Vocatio initiatus", "invalidEmail": "<PERSON><PERSON><PERSON>", "joinAMeeting": "<PERSON><PERSON><PERSON> testimonii", "joinAudioByPhone": "Iungere cum telephono audio", "joinMeeting": "<PERSON><PERSON><PERSON> testimonii", "joinMeetingGuest": "Adiungere testimonii ut Hospes", "joinWithoutAudio": "Iungere sine audio", "keyboardShortcuts": "Admitte Compendia", "linkCopied": "Link copied ut clipboard", "logout": "<PERSON><PERSON>", "lookGood": "Hoc tortor ligula tua sonat recte laborat", "maximumAllowedParticipantsErr": "Maxime permissum participantium in hoc conventiculo pervenerunt. Contactus conventus Organizer.", "meetingReminder": "Conventus incipiet {{time}}. Quaeso coniunge rursus vel ante tempus Scheduled.", "multipleConferenceInitiation": "Multiplex Conferentiarum initiationis", "oops": "Oops!", "oppsMaximumAllowedParticipantsErr": "Oops! Maximum participantium modus permissus ventum est. Quaeso exspecta participiculas excedere vel contactum auctoris conventus.", "or": "or *", "parallelMeetingsLicencesErr": "'Non testimonii incipere. Fac tibi licentiam activam ad congressus parallelos iungere", "peopleInTheCall": "Populus in vocationem", "pleaseEnterEmail": "Please enter Email", "pleaseEnterFullName": "Please enter nomen", "premeeting": "Pre testimonii", "profile": "Set", "readyToJoin": "Paratus i<PERSON>?", "recentMeetings": "Recens meetings", "screenSharingError": "Error participatio screen:", "showScreen": "Admitte pre screen testimonii", "signinsignup": "Sign in / Sign Up", "startWithPhone": "Satus telephonum audio", "subScriptionInactiveErr": "Subscriptio tua est Ut ultrices vestibulum. 'Non testimonii incipere.", "systemUpgradedInformation": "Systema nostrum upgraded ad 2.0 versionem. Dictum est hoc conventu per participationem huius testimonii password", "userNotAllowedToJoin": "User non licebat copulare", "videoOnlyError": "Vides errorem;", "videoTrackError": "Posse creare video semita.", "viewAllNumbers": "Omnes numeri", "waitForModeratorMsg": "Quaeso expectare dum Moderator vocationi iungitur.", "waitForModeratorMsgDynamic": "Quaeso exspecta dum {{Moderator}} vocationi iungitur.", "youAreNotAllowed": "Non licet"}, "presenceStatus": {"busy": "Occupatus", "calling": "Vocavit...", "connected": "Coniuncta", "connecting": "Connectens...", "connecting2": "Connectens*...", "disconnected": "Disiungitur", "expired": "<PERSON><PERSON><PERSON><PERSON>", "ignored": "neglectus", "initializingCall": "Initializing vocationem ...", "invalidToken": "<PERSON><PERSON><PERSON>", "invited": "invitatus", "rejected": "reprobum", "ringing": "<PERSON>ni<PERSON>...", "signInAsHost": "Signum in ut Hostiam"}, "profile": {"avatar": "Avatar", "setDisplayNameLabel": "Ostentationem tuam nomen tuum", "setEmailInput": "Intra electronicam", "setEmailLabel": "<PERSON>a inscriptio", "title": "Set"}, "raisedHand": "<PERSON><PERSON><PERSON> loqui", "recording": {"authDropboxText": "Upload ad Dropbox", "availableS3Space": "Usus spatii: {{s3_used_space}} ex {{s3_free_space}}", "availableSpace": "Praesto spatio: {{spaceLeft}} MB (proxime {{duration}} minuta recording)", "beta": "Beta", "busy": "Operantes sumus in recording liberandis facultatibus. Aliquam paulisper.", "busyTitle": "Omnes commentariis sunt currently occupatus", "consentDialog": {"disclaimer": "Et recording potest esse in documentis, disciplina, aut alia res proposita. Nisi vos non consentiunt, placere relinquere conventum nunc.", "message": "Hoc testimonii est ut memoriae. Per continuing participare, vos consentiunt ad esse memoriae.", "title": "Recordatio consensu"}, "copyLink": "Effingo Link", "error": "Recordatio defecit. Quaeso, iterum conare.", "errorFetchingLink": "Error emensus in memoria ligamen.", "expandedOff": "Recordatio constiterit", "expandedOn": "Conventus est currently memoratur.", "expandedPending": "Recordatio inchoatur...", "failedToStart": "Recordatio defecit incipere", "fileSharingdescription": "Share memoria in conventu participantium", "limitNotificationDescriptionNative": "Ob altitudinem tuam electronicam postulationem ad {{limit}}} modum min finietur. Nam tabulas infinitas experiri <3>{{app}}</3>.", "limitNotificationDescriptionWeb": "Ob altitudinem tuam electronicam postulationem ad {{limit}}} modum min finietur. Nam tabulas infinitas experiri <a href={{url}} rel='noopener noreferrer' target='_blank'>{{app}}</a>.", "linkGenerated": "Vinculum generavimus ad amussim tuam.", "live": "VIVERE", "loggedIn": "Initium ut {{userName}}", "off": "Recordatio cess<PERSON>", "offBy": "{{name}} memoria obturaverunt", "on": "<PERSON>atio co<PERSON>i", "onBy": "{{name}} memoria incepit", "pending": "Praeparans notare conventum...", "rec": "REC", "recLive": "VIVERE + REC", "serviceDescription": "Tua memoria salvus erit in memoria muneris", "serviceDescriptionCloud": "CUBRABILIS", "serviceName": "<PERSON><PERSON><PERSON> m<PERSON>", "signIn": "Signum", "signOut": "Exire", "unavailable": "Oops! Nomen {serviceName}} sit amet unavailable. Nos erant 'opus in solvendis exitus. Mox iterum conare.", "unavailableTitle": "Recordatio unavailable", "uploadToCloud": "Upload ad nubem"}, "sectionList": {"pullToRefresh": "Trahere ad reficiendum"}, "security": {"about": "Potes addere $t(lockRoomPassword) conventui tuo. Participes necesse erit dare $t(lockRoomPassword) antequam conventum coniungi liceat.", "aboutReadOnly": "Moderator participantium conventui $t(lockRoomPassword) addere potest. Participes necesse erit dare $t(lockRoomPassword) antequam conventum coniungi liceat.", "insecureRoomNameWarning": "Locus nomen tutum est. Participes inutiles colloquio tuo coniungi possunt. Considera securitate foederis utens in securitate deprimendo.", "securityOptions": "Securitas optiones"}, "settings": {"calendar": {"about": "Integratio calendarii {{appName} adhibetur ut calendarium secure accedere possit ut eventa ventura legere possit.", "disconnect": "Disconnect", "microsoftSignIn": "Sign in cum Microsoft", "signedIn": "In praesenti accessu rerum calendariorum pro {{email}}. Preme Disconnect button sub ut desinas accedere certe calendarii.", "title": "Calendarium"}, "desktopShareFramerate": "Desktop frame rate sharing", "desktopShareHighFpsWarning": "Articuli altioris rate pro escritorio communicandi, ut bandwidth tuam afficere possent. Vos postulo ut sileo screen participem pro novis fundis ut effectum sortiatur.", "desktopShareWarning": "Vos postulo ut sileo screen participem pro novis fundis ut effectum sortiatur.", "devices": "Cogitationes", "followMe": "Quisque sequitur me", "framesPerSecond": "tabulae per-secundum", "incomingMessage": "Epistula advenientis", "language": "Linguae", "languageSettings": "<PERSON><PERSON>", "loggedIn": "Initium ut {{name}}", "microphones": "Microphones", "moderator": "Moderator", "more": "More", "name": "Nomen", "noDevice": "<PERSON><PERSON><PERSON>", "noLanguagesAvailable": "Nulla linguarum available", "participantJoined": "Participem Joined", "participantLeft": "Participem Reliquit", "playSounds": "Sonus ludere in", "sameAsSystem": "Eadem ac ratio ({{label}}})", "selectAudioOutput": "Audio output", "selectCamera": "Camera", "selectLanguage": "Linguae Selectae", "selectMic": "<PERSON><PERSON><PERSON>", "sounds": "Sonos", "speakers": "Orator", "startAudioMuted": "Omnis incipit transmutari", "startVideoMuted": "Omnis incipit occultatum", "talkWhileMuted": "Loqui dum transmutari", "title": "<PERSON><PERSON><PERSON>"}, "settingsView": {"advanced": "Provecta", "alertCancel": "Inrito", "alertOk": "Ok", "alertTitle": "Monitum", "alertURLText": "Ingressus servo URL invalidum est", "buildInfoSection": "Informationes aedificate", "conferenceSection": "Colloquium", "disableCallIntegration": "Inactivare patria vocationem integrationem", "disableCrashReporting": "Inactivare fragore renuntiationes", "disableCrashReportingWarning": "Visne vero disable fragore renuntiationes? Occasus applicabitur post te sileo app.", "disableP2P": "Inactivare pari-Ad-Per modus", "displayName": "Propono nomen", "email": "Email", "header": "<PERSON><PERSON><PERSON>", "profileSection": "Set", "serverURL": "Servo URL", "showAdvanced": "Monstra provectus occasus", "startWithAudioMuted": "Satus audio transmutari", "startWithVideoMuted": "Satus video transmutari", "version": "Verso"}, "share": {"dialInfoText": "=====\n\nIustus volo horologio in in phone?\n\n{{defaultDialInNumber}}Click this link to see the dial in phone number of this meeting\n{{dialInfoPageUrl}}", "mainText": "Preme nexum sequentem ad conventum iungere:\n{{roomUrl}}"}, "speaker": "Orator", "speakerStats": {"hours": "{{count}}h", "minutes": "{{count}}m", "name": "Nomen", "seconds": "{{count}}s", "speakerStats": "Orator Stats", "speakerTime": "Orator Tempus"}, "startupoverlay": {"genericTitle": "Conventus uti tortor ligula et camera indiget.", "policyText": "", "title": "{{app}} tortor ligula ac camera uti debet."}, "suspendedoverlay": {"rejoinKeyTitle": "<PERSON><PERSON><PERSON><PERSON>", "text": "Preme <i>Reiunge</i> puga reconnect.", "title": "Interpellatum video vocationem tuam quia hic computator obdormivit."}, "toolbar": {"Settings": "<PERSON><PERSON><PERSON>", "Share": "<PERSON><PERSON>", "accessibilityLabel": {"Settings": "Toggle occasus", "audioOnly": "Toggle audio solum", "audioRoute": "<PERSON><PERSON> sonus fabrica", "boo": "<PERSON><PERSON>", "callQuality": "Curo Video Quality", "carmode": "Car modus", "cc": "Toggle imago", "chat": "Open / Chat prope", "clap": "Plaudite", "collapse": "<PERSON><PERSON><PERSON>", "document": "Toggle documentum participatur", "donationCLP": "C & P Iungo Occasus", "donationLink": "DonorBox Occasus", "download": "Download nostra apps", "embedMeeting": "<PERSON><PERSON> testimonii", "expand": "Expendo", "feedback": "Relinquere feedback", "fullScreen": "Toggle plenus screen", "genericIFrame": "Toggle applicationem participatur", "giphy": "Toggle <PERSON><PERSON><PERSON>", "grantModerator": "<PERSON><PERSON>sta Moderator", "hangup": "Relinquere conventu", "help": "Auxilium", "invite": "invitare populum", "kick": "calcitrare participem", "laugh": "<PERSON><PERSON>", "leaveConference": "<PERSON><PERSON><PERSON><PERSON> testimonii", "like": "Pollice", "lobbyButton": "Admitte / disable VESTIBULUM modus", "localRecording": "Toggle loci imperium controls", "lockRoom": "Toggle testimonii password", "moreActions": "Plures actiones", "moreActionsMenu": "Plures actiones menu", "moreOptions": "Plures optiones monstrare", "mute": "Mutus / Unmute", "muteEveryone": "muta omnis", "muteEveryoneElse": "Mutum quisque aliud", "muteEveryoneElsesVideo": "Inactivare aliorum camera", "muteEveryonesVideo": "Inactivare omnium camera", "participants": "Praesent", "party": "Factio Popper", "pip": "Toggle Book-in-Picture modus", "privateMessage": "Mitte privata nuntium", "profile": "Edit profile tuum", "raiseHand": "Excita / demitte manum tuam", "reactionsMenu": "Open / Close reactiones menu", "recording": "Toggle memoria", "remoteMute": "muta particeps", "remoteVideoMute": "Inactivare camera participem", "removeDonation": "Aufer DonorBox", "rmoveCDonation": "Remove C & P*", "security": "Securitas optiones", "selectBackground": "Lego Maecenas vitae", "shareRoom": "invitare aliquem", "shareYourScreen": "Satus / Nolite communicare screen", "shareaudio": "Share Audio", "sharedvideo": "Toggle YouTube Video Socius", "shortcuts": "Toggle Shortcuts", "show": "Ostende in scaena", "speakerStats": "Toggle speaker mutant", "surprised": "Admir<PERSON>", "tileView": "Toggle ille sententiam", "toggleCamera": "Toggle camera", "toggleFilmstrip": "Toggle Filmstrip", "toggleReactions": "Toggle profectae", "videoblur": "Toggle video LABES", "videomute": "Satus / Nolite camera"}, "addPeople": "Addere populum tuum voca", "audioOnlyOff": "Sed modus humilis inactivare", "audioOnlyOn": "Sed modus humilis activare", "audioRoute": "<PERSON><PERSON> sonus fabrica", "audioSettings": "Audio occasus", "authenticate": "authenticitate", "boo": "<PERSON><PERSON>", "callQuality": "Curo Video Quality", "chat": "Open / Chat prope", "clap": "Plaudite", "closeChat": "<PERSON><PERSON>", "closeParticipantsPane": "Participantium prope pane", "closeReactionsMenu": "Prope profectae menu", "disableNoiseSuppression": "Inactivare strepitus suppressio", "disableReactionSounds": "Vos can disable reactionem sonos pro hoc conventu", "documentClose": "Close LivePAD", "documentOpen": "Share Livepad", "donationCLP": "C & P Iungo Occasus", "donationLink": "DonorBox Occasus", "download": "Download nostra apps", "e2ee": "Finis-ad-finem Encryption", "embedMeeting": "<PERSON><PERSON> testimonii", "enterFullScreen": "View Full Screen", "enterTileView": "Intra ille sententiam", "exitFullScreen": "Exit plena Screen", "exitTileView": "Exitus ille visum", "feedback": "Relinquere feedback", "genericIFrameClose": "Subsisto WhiteBoard", "genericIFrameOpen": "Share Whiteboard", "genericIFrameWeb": "Tabula alba", "hangUpText": "Certus esne vis suspendere?", "hangUpforEveryOne": "Hangup pro omnibus", "hangUpforMe": "Hangup solum mihi", "hangup": "<PERSON><PERSON><PERSON><PERSON>", "help": "Auxilium", "hideReactions": "Celare reactionem", "iOSStopScreenShareAlertMessage": "Nolite misericordiam ante HangUp screen participatio.", "iOSStopScreenShareAlertTitle": "Subsisto 'collaborative Socius", "invite": "invitare populum", "inviteViaCalendar": "Invitare per calendarium", "laugh": "<PERSON><PERSON>", "leaveConference": "<PERSON><PERSON><PERSON><PERSON> testimonii", "like": "Pollice", "lobbyButtonDisable": "VESTIBULUM modus inactivare", "lobbyButtonEnable": "Admitte VESTIBULUM modus", "login": "<PERSON><PERSON>", "logout": "<PERSON><PERSON>", "lowerYourHand": "Summitte manum tuam", "moreActions": "Plures actiones", "moreOptions": "Plures optiones", "mute": "Mutus / Unmute", "muteEveryone": "muta omnis", "muteEveryonesVideo": "Inactivare omnium camera", "noAudioSignalDesc": "Si non industria ab occasus systematis vel ferramentis mutas, cogita mutandi fabrica.", "noAudioSignalDescSuggestion": "Si non industria ab occasus systematis vel ferramentis mutas, cogita mutandi machinas suggessit.", "noAudioSignalDialInDesc": "Etiam horologio uti potes:", "noAudioSignalDialInLinkDesc": "Alternis-in numeris", "noAudioSignalTitle": "<PERSON><PERSON>a in ex mi!", "noisyAudioInputDesc": "Sonat ut tortor ligula tua sonum facit, quaeso considera muta vel mutando machinam.", "noisyAudioInputTitle": "Videtur tortor ligula tua strepere!", "openChat": "Open Chat", "openReactionsMenu": "Aperta profectae menu", "participants": "Praesent", "party": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pip": "Picture in picture modus", "privateMessage": "Mitte privata nuntium", "profile": "Edit profile tuum", "raiseHand": "Excita / demitte manum tuam", "raiseYourHand": "Extende manum tuam", "reactionBoo": "<PERSON><PERSON> boo <PERSON>em", "reactionClap": "<PERSON>tte plaudunt reactionem", "reactionLaugh": "<PERSON>tte risu reactionem", "reactionLike": "Mitte pollice reactionem", "reactionParty": "Mitte pars popper reactionem", "reactionSurprised": "<PERSON><PERSON> mirari reactionem", "removeDonation": "Aufer DonorBox", "rmoveCDonation": "Remove C & P*", "security": "Securitas optiones", "selectBackground": "Lego Maecenas vitae", "shareRoom": "invitare aliquem", "shareaudio": "Share Audio", "sharedvideo": "Participes YouTube", "shortcuts": "View Vulgate", "showReactions": "Ostende reactionem", "speakerStats": "Orator Stats", "startScreenSharing": "Satus screen sharing", "startSubtitles": "Satus subtitles", "stopAudioSharing": "Subsisto audio sharing", "stopScreenSharing": "Subsisto screen sharing", "stopSharedVideo": "Subsisto YouTube video", "stopSubtitles": "Subsisto imago", "surprised": "Admir<PERSON>", "talkWhileMutedPopup": "<PERSON><PERSON> dicere? Tu obmutescis.", "tileViewToggle": "Toggle ille sententiam", "toggleCamera": "Toggle camera", "videoSettings": "Video Occasus", "videomute": "Satus / Nolite camera", "voiceCommand": "Open Voice Imperii", "whiteBoardOpen": "Share Whiteboard", "zoomin": "In zoom", "zoomout": "Re<PERSON><PERSON>"}, "transcribing": {"ClosedCaptions": "Clausa captionum", "LiveCaptions": "Captions Vivus", "NoCaptionsAvailable": "Nullae captiones praesto sunt", "OpenCloseCaptions": "Open / Close Captions", "Transcribing": "Transcribing", "TranscribingNotAvailable": "Transcribing not available", "TranscriptionLangDefaultNote": "Nota: transcribenda est unavailable in lectus testimonii linguae {{language}}, sic erit default ad Anglis.", "TranscriptionLanguageCannotBeChangedOngoingCall": "Transcription lingua non mutatur in permanentis vocationem. Misericordiam Great ut effectum.", "TranscriptionLanguageCantChange": "Transcription lingua potest '\\ c't <PERSON><PERSON>e", "Transcriptions": "Transcriptiones", "Translate": "Transferre", "TranslateTo": "Transferre ad", "Translating": "Translatio", "TranslationNotAvailable": "Translation not available", "ccButtonTooltip": "Satus / Desine subtitles", "error": "Transcribendo defuit. Quaeso, iterum conare.", "expandedLabel": "Transcribendo sit amet in", "failedToStart": "Defecit ut satus transcribendo", "labelToolTip": "Conventus est transcribi", "off": "Transcribendo cessaverunt", "pending": "Praeparans conventum transcribere ...", "sourceLanguageDesc": "Nunc sermo conventus constitutus est ad {{sourceLanguage}}", "sourceLanguageHere": "Hic mutare potes", "start": "Satus ostendens imago", "stop": "Subsisto ostendens imago", "subtitlesOff": "Off", "subtitlesTitle": "Subtituli", "tr": "TRE", "transcriptionQuotaExceeded": "Quota transcriptionis pro hoc mense excessit", "transcriptionQuotaExceededTitle": "Quota transcriptionis excessit"}, "userMedia": {"androidGrantPermissions": "Elige <b><i>Permitte</i></b> cum navigatrum tuum petit permissiones.", "chromeGrantPermissions": "Elige <b><i>Permitte</i></b> cum navigatrum tuum petit permissiones.", "edgeGrantPermissions": "<b><i>Ita</i></b> cum navigatrum tuum petit permissiones.", "electronGrantPermissions": "<PERSON><PERSON><PERSON><PERSON>, concede utendi camera et tortor ligula", "firefoxGrantPermissions": "<b><i>Selectae Fabricae partis</i></b> cum navigatrum tuum petit permissiones.", "iexplorerGrantPermissions": "Elige <b><i>OK</i></b> cum navigatrum tuum petit permissiones.", "nwjsGrantPermissions": "<PERSON><PERSON><PERSON><PERSON>, concede utendi camera et tortor ligula", "operaGrantPermissions": "Elige <b><i>Permitte</i></b> cum navigatrum tuum petit permissiones.", "react-nativeGrantPermissions": "Elige <b><i>Permitte</i></b> cum navigatrum tuum petit permissiones.", "safariGrantPermissions": "Elige <b><i>OK</i></b> cum navigatrum tuum petit permissiones."}, "videoSIPGW": {"busy": "In liberandis opibus laboramus. Aliquam paulisper.", "busyTitle": "Locus officium est currently occupatus", "errorAlreadyInvited": "{{displayName}} iam invitatus", "errorInvite": "Colloquium nondum constitutum est. Mox iterum conare.", "errorInviteFailed": "Nos erant 'opus in solvendis exitus. Mox iterum conare.", "errorInviteFailedTitle": "Invitare {{displayName}} defecit", "errorInviteTitle": "Error vocat locus", "pending": "{{displayName}} invitatus est"}, "videoStatus": {"audioOnly": "AUD", "audioOnlyExpanded": "Sed modus in low es. Hoc modo tantum audies et tegumentum communicabis.", "callQuality": "Video", "hd": "HD", "hdTooltip": "Viewing alta definitione video", "highDefinition": "Princeps definitionem", "labelTooiltipNoVideo": "Non video", "labelTooltipAudioOnly": "<PERSON><PERSON><PERSON> modus enabled", "ld": "Ld", "ldTooltip": "Videre humilis definitionem video", "lowDefinition": "Minimum definition", "onlyAudioAvailable": "Tantum audio is available", "onlyAudioSupported": "Tantum audio in hoc navigatro support.", "sd": "SD", "sdTooltip": "Videre vexillum definitionem video", "standardDefinition": "Latin definition", "uhd": "UHD", "uhdTooltip": "Videre ultra alta definitione video", "uhighDefinition": "Ultra High Definition"}, "videothumbnail": {"connectionInfo": "Nexum info", "domute": "<PERSON><PERSON>", "domuteOthers": "Mutum quisque aliud", "domuteVideo": "Inactivare camera", "domuteVideoOfOthers": "Inactivare camera de omnibus aliis", "flip": "Flip", "grantModerator": "<PERSON><PERSON>sta Moderator", "kick": "Aufer usor", "moderator": "Moderator", "mute": "Participem est transmutari", "muted": "Muted", "remoteControl": "Satus / Desine remote imperium", "show": "Ostende in scaena", "videoMuted": "Camera debilitatum", "videomute": "Participem constiterit camera"}, "virtualBackground": {"addBackground": "Addere background", "appliedCustomImageTitle": "Fasciculi Custom Image", "apply": "Applicare", "blur": "LABOR", "customImg": "<PERSON><PERSON>m", "deleteImage": "Delere imaginem", "desktopShare": "Desktop participes", "desktopShareError": "Non potuit creare desktop participes", "enableBlur": "Admitte LABES", "image1": "<PERSON><PERSON>", "image2": "Neutrum murum album", "image3": "Alba vacuus locus", "image4": "Nigrum area lucerna", "image5": "Mons", "image6": "<PERSON>", "image7": "<PERSON><PERSON>", "none": "<PERSON><PERSON><PERSON>", "pleaseWait": "Placere expectare ...", "removeBackground": "Removere background", "slightBlur": "<PERSON><PERSON>", "switchBackgroundTitle": "Switch background", "title": "<PERSON><PERSON> Backgrounds", "uploadedImage": "Imago {index}} uploaded", "virtualImagesTitle": "Rectum inaedificata Imagines", "webAssemblyWarning": "WebAssembly non valet"}, "voicecommand": {"activePIPLabel": "Active pip", "clickOnMic": "Click on the Mic and Voice a command", "hints": {"StopScreenSharing": "Subsisto 'collaborative text sharing", "closeLivePad": "Close LivePAD", "closeWhiteboard": "Close Whiteboard", "closeYoutube": "Close YouTube", "hints": "innuit", "invitePeople": "invitare <PERSON>", "lowerHand": "<PERSON><PERSON><PERSON>", "openChatBox": "Aperi Chat arca archa", "openClickAndPledge": "Apertum click et pignus", "openDonorbox": "Open Donorbox", "openFullScreen": "Apertum plenum screen", "openLivePad": "Aperi Livepad", "openLivestream": "Open Livestream", "openParticipantPane": "APPARATUS", "openRecording": "Open Record", "openSettings": "Open occasus", "openSpeakerStats": "Aperta speaker <PERSON><PERSON>", "openVideoQualityDialog": "Apertum video qualis dialogo", "openVirtualBackground": "Aperta virtual background", "openWhiteboard": "Whitboard aperire", "openYoutube": "Open YouTube", "raiseHand": "Excita manus", "removeClickAndPledge": "Aufer click et pignus", "removeDonorbox": "Aufer Donorbox", "startScreenSharing": "Satus 'collaborative text sharing"}, "inActivePIPLabel": "In Active ACINUS", "pleaseWaitWeAreRecording": "Quaeso expecta sumus Book", "vcLabel": "Vox Imperii", "voiceCommandForMeethour": "Vox Imperii For Occursum hora"}, "volumeSlider": "Volume lapsus", "welcomepage": {"accessibilityLabel": {"join": "ICTUS ad iungere", "roomname": "Intra testimonii ID"}, "addMeetingName": "Add nomen testimonii", "appDescription": "Perge, video chat cum tota turma. Nam invitare omnes scis. {{app}} plene encryptum est, 100 fons apertus video solutionem conferendi ut tota die, omni die, gratis uti possis — nulla ratione opus est.", "audioVideoSwitch": {"audio": "Vox", "video": "Video"}, "calendar": "Calendarium", "connectCalendarButton": "Coniungere fastis", "connectCalendarText": "Calendarium tuum ut omnes conventus tuos in {{app}} videas. Plus, {provisorem}} conventicula ad calendarium tuum adde et eas uno clic incipe.", "developerPlan": "Elit Plan", "enterRoomTitle": "Satus Novae Conventus seu Intra Existere volutpat Name", "enterprisePlan": "Enterprise Plan", "enterpriseSelfHostPlan": "Enterprise Self Host Plan", "features": "Features", "footer": {"allRightsReserved": "All rights reserved", "androidAppDownload": "Android App Download", "apiDocumentation": "API Documenta", "app": "App", "blog": "Blog", "company": "Societas", "contact": "<PERSON><PERSON>", "copyright": "Copyright", "copyrightText": "Copyright MMXX - MMXXIV occursum hora LLC. All rights reserved", "developers": "Developers", "disclaimer": "Disclaimer", "download": "Download", "email": "Email", "faqs": "FAQs", "followUs": "<PERSON><PERSON><PERSON>", "helpDesk": "Auxilium Desk", "home": "In domo", "iOSAppDownload": "iOS App Download", "inTheNews": "In nuntium", "integrations": "Integrations", "knowledgeBase": "Scientia Base", "meethour": "<PERSON><PERSON>um", "meethourLLC": "Occursum Hour LLC", "officeAddress": "8825 Stanford , Suite 205 Columbia, MD 21045", "phone": "Telephono", "privacyPolicy": "Privacy Policy", "productPresentation": "Product Presentation", "refundCancellationPolicy": "Refugium & receptae Policy", "termsConditions": "Terms & Conditions", "testimonials": "Testimonia", "webMobileSDK": "Web & Mobile SDK", "whoAreYou": "Quis es"}, "forHospitals": "Pro Hospitalibus", "freeBannerDescription": "Free & Unlimited HD Quality Video Conference like never before. Coniunge alicunde foederis.", "freePlan": "Liberum consilium", "getHelp": "FAQs", "go": "Create an Jo<PERSON> testimonii", "goSmall": "Create an Jo<PERSON> testimonii", "header": {"accelerateDevelopmentWithPrebuiltSDKs": "Accelerate progressionem cum praestructum SDKs.", "apiStatus": "API Status", "appointmentSchedulingVideoConference": "Constitutio Scheduling & Video conferentia.", "blog": "Blog", "customIntegrationDedicatedSupport": "Custom integration & dedicated firmamentum", "customTailoredVideoMeetings": "Custom Tailored Video Meetings.", "developer": "Elit", "developers": "Developers", "documentation": "Documenta", "eMail": "E-mail", "edTech": "Edtech", "engagingOnlineLearningForEducators": "Tenens Online Learning for Educators.", "engagingVirtualEventExperiences": "Aliquam suscipit lorem ipsum.", "enterprise": "Inceptum", "enterpriseSelfHost": "Enterprise sui exercitum", "features": "Features", "fitness": "Opportunitas", "free": "Libero", "fundraiseEffortlesslyWithinVideoConferences": "Fundraise sine labore Within Video Conferences.", "fundraisingDonate": "Fundraising / Donate", "fundraisingDonateOnline": "Fundraising / Donate Online", "getStarted": "<PERSON><PERSON><PERSON>", "hdQualityVideoConferenceApp": "HD Quality Video Conference App", "help": "Auxilium", "helpDesk": "Auxilium Desk", "highQualityLiveEventStreaming": "Summus qualitas vivere res effusis.", "hostVideoConferenceOnYourServers": "Exercitium video colloquium de servientibus tuis.", "industries": "Industria", "integrateVideoCallWithinYourWebsiteApp": "Integrate Video Voca intra website/app.", "interactiveVirtualLearningSolutions": "Interactive Rectum Doctrina Solutions.", "joinAMeeting": "<PERSON><PERSON><PERSON> testimonii", "knowledgeBase": "Scientia Base", "liveStreaming": "Vivamus streaming", "meethour": "<PERSON><PERSON>um", "myCaly": "meum Caly", "myCalyPricing": "Mycaly Morbi cursus sapien.", "mycaly": "MYCALY", "noAdsRecordingLiveStreaming": "Nemo ads + recording + vivet.", "noTimeLimitGroupCalls": "Non terminum in 1:1 & Group advocat.", "preBuiltSDKs": "Pre-Bedificatum SDKs", "pricing": "MORBUS", "pro": "Pro", "products": "Productus", "resources": "Opibus", "scheduleADemo": "Schedule est demo", "simplifiedAPIReferences": "Simplicior API References", "smoothVideoOnboardingExperience": "<PERSON><PERSON> video onboarding experientia.", "solutions": "Solutions", "stayuptodateWithOurBlog": "Mane up-to-date nostra blog", "systemHealthStatusandUpdates": "Systema sanitatis status ac updates", "tailoredSolutionsForYourHealthcareNeeds": "Tailored solutions propter curis necessitates.", "telehealth": "Telhealth", "useCases": "<PERSON><PERSON>", "videoConference": "Video Conference", "videoConferencePlans": "Vide Conference Consilia", "videoConferencePricing": "Vide colloquium cursus sapien.", "videoConferencing": "Video Conference", "videoKYC": "Video kyc", "virtualClassrooms": "Lorem Ipsum Curabitur aliquet", "virtualEvents": "Lorem ipsum Events", "virtualSolutionForHomeFitness": "Virtual Solutio pro Domo Fitness.", "webinarSessionsWithIndustryLeaders": "Sessiones Webinar cum Industry Principes.", "webinars": "Webinars"}, "headerSubtitle": "Secure et HD qualis conventus", "headerTitle": "<PERSON><PERSON>um", "info": "Alternis-in info", "invalidMeetingID": "Aliquam id testimonii", "jitsiOnMobile": "Horam conveniant in mobili - download nostri apps ac satus a contione usquam", "join": "CREARE / JOIN", "joinAMeeting": "<PERSON><PERSON><PERSON> testimonii", "logo": {"calendar": "Calendarium logo", "desktopPreviewThumbnail": "Desktop Preview Thumbnail", "googleLogo": "Google logo", "logoDeepLinking": "<PERSON><PERSON><PERSON> logo", "microsoftLogo": "Microsoft logo", "policyLogo": "Consilium logo"}, "meetingDate": "occurrens Date", "meetingDetails": "<PERSON><PERSON><PERSON>", "meetingIsReady": "Conventum est paratus", "mobileDownLoadLinkAndroid": "Download mobile app Android", "mobileDownLoadLinkFDroid": "Download mobile app pro F-Droid", "mobileDownLoadLinkIos": "Download mobile app iOS", "moderatedMessage": "Vel <a href=\"{{url}}\" rel=\"noopener noreferrer\" target=\"_blank\">book contione URL</a> in antecessum ubi solus es moderator.", "oopsDeviceClockorTimezoneErr": "Oops! Aliquid abiit iniuriam. Fac tuom horologium / timezone est accurate", "oopsThereSeemsToBeProblem": "X videtur esse problema", "pleaseWaitForTheStartMeeting": "Quaeso expecta {moderator}} ut hunc conventum incipias.", "preRegistrationMsg": "Hic conventus requirit Pre-adnotationem. Teipsum in navigatro subcriptio et ab invitato nexum electronicum retrahe.", "pricing": "MORBUS", "privacy": "Secretum", "privateMeetingErr": "Coetus privatus iungendi tibi videtur. Quaeso iungere per signum in utens inscriptione electronica adhibita.", "proPlan": "proPlan", "recentList": "<PERSON><PERSON>", "recentListDelete": "Delere ingressum", "recentListEmpty": "Recens index tuus nunc vacuus est. Loqui cum turma tua et omnes recentes conventus hic invenies.", "reducedUIText": "Welcome ad {{app}}!", "registerNow": "Register Now", "roomNameAllowedChars": "Nomen conventus nullum ex his characteribus continere debet: ?, &, :, ', «, %, #.", "roomname": "Intra testimonii ID", "roomnameHint": "Intra nomen vel domicilium cubiculi quod vis iungere. Nomen facere potes, modo sciant homines quos convenitis ut idem nomen ineant.", "scheduleAMeeting": "Schedule testimonii", "sendFeedback": "Mitte feedback", "shiftingVirtualMeetToReality": "Remotio Rectum Meet ad rem", "solutions": "Solutions", "startMeeting": "Satus foederis", "terms": "Terms", "timezone": "Timezone", "title": "100% Free Infinitus, End to End Encrypted, and HD Quality Video Conference Solutio", "tryNowItsFree": "<PERSON><PERSON>, Est Free", "waitingInLobby": "Exspectans in VESTIBULUM. {{moderator}} te mox dimittet.", "youtubeHelpTutorial": "YouTube Auxilium Tutorials"}}