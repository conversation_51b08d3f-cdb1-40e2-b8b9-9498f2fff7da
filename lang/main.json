{"addPeople": {"add": "Invite", "addContacts": "Invite your contacts", "contacts": "contacts", "copyInvite": "Copy meeting invitation", "copyLink": "Copy meeting link", "copyStream": "Copy live streaming link", "countryNotSupported": "We do not support this destination yet.", "countryReminder": "Calling outside the US? Please make sure you start with the country code!", "defaultEmail": "Your Default Email", "disabled": "You can't invite people.", "doYouWantToRemoveThisPerson": "Do you want to remove this person", "failedToAdd": "Failed to add participants", "footerText": "Dialing out is disabled.", "googleCalendar": "Google Calendar", "googleEmail": "Google Email", "inviteMoreHeader": "You are the only one in the meeting", "inviteMoreMailSubject": "Join {{appName}} meeting", "inviteMorePrompt": "Invite more people", "linkCopied": "Link copied to clipboard", "loading": "Searching for people and phone numbers", "loadingNumber": "Validating phone number", "loadingPeople": "Searching for people to invite", "loadingText": "Loading...", "noResults": "No matching search results", "noValidNumbers": "Please enter a phone number", "outlookEmail": "Outlook Email", "phoneNumbers": "phone numbers", "searching": "Searching...", "searchNumbers": "Add phone numbers", "searchPeople": "Search for people", "searchPeopleAndNumbers": "Search for people or add their phone numbers", "sendWhatsa[pp": "WhatsApp", "shareInvite": "Share meeting invitation", "shareInviteP": "Share meeting invitation with <PERSON><PERSON>", "shareLink": "Share the meeting link to invite others", "shareStream": "Share the live streaming link", "sipAddresses": "sip addresses", "telephone": "Telephone: {{number}}", "title": "Invite people to this meeting", "yahooEmail": "Yahoo Email"}, "audioDevices": {"bluetooth": "Bluetooth", "headphones": "Headphones", "none": "No audio devices available", "phone": "Phone", "speaker": "Speaker"}, "audioOnly": {"audioOnly": "Low bandwidth"}, "breakoutRooms": {"actions": {"add": "Add breakout room (Beta)", "autoAssign": "Auto assign to breakout rooms", "close": "Close", "join": "Join", "leaveBreakoutRoom": "Leave breakout room", "more": "More", "remove": "Remove", "rename": "<PERSON><PERSON>", "renameBreakoutRoom": "Rename breakout room", "sendToBreakoutRoom": "Send participant to:"}, "breakoutList": "Breakout list", "buttonLabel": "Breakout rooms", "defaultName": "Breakout room #{{index}}", "hideParticipantList": "Hide participant list", "mainRoom": "Main room", "notifications": {"joined": "Joining the \"{{name}}\" breakout room", "joinedMainRoom": "Joining the main room", "joinedTitle": "Breakout Rooms"}, "showParticipantList": "Show participant list", "showParticipants": "Show Participants", "title": "Breakout Rooms"}, "calendarSync": {"addMeetingURL": "Add a meeting link", "confirmAddLink": "Do you want to add a Meet Hour link to this event?", "error": {"appConfiguration": "Calendar integration is not properly configured.", "generic": "An error has occurred. Please check your calendar settings or try refreshing the calendar.", "notSignedIn": "An error occurred while authenticating to see calendar events. Please check your calendar settings and try logging in again."}, "join": "Join", "joinTooltip": "Join the meeting", "nextMeeting": "next meeting", "noEvents": "There are no upcoming events scheduled.", "ongoingMeeting": "ongoing meeting", "permissionButton": "Open settings", "permissionMessage": "The Calendar permission is required to see your meetings in the app.", "refresh": "Refresh calendar", "today": "Today"}, "carmode": {"actions": {"selectSoundDevice": "Select sound device"}, "labels": {"buttonLabel": "Car mode", "title": "Car mode", "videoStopped": "Your video is stopped"}}, "chat": {"enter": "Enter chat room", "error": "Error: your message was not sent. Reason: {{error}}", "fieldPlaceHolder": "Type your message here", "message": "Message", "messageAccessibleTitle": "{{user}} says:", "messageAccessibleTitleMe": "me says:", "messagebox": "Type a message", "messageTo": "Private message to {{recipient}}", "nickname": {"popover": "Choose a nickname", "title": "Enter a nickname to use chat"}, "noMessagesMessage": "There are no messages in the meeting yet. Start a conversation here!", "privateNotice": "Private message to {{recipient}}", "smileysPanel": "Emoji panel", "tabs": {"chat": "Cha<PERSON>", "polls": "Polls"}, "title": "Chat and Polls", "titleWithPolls": "Chat and Polls", "you": "you"}, "chromeExtensionBanner": {"buttonText": "Install Chrome Extension", "close": "Close", "dontShowAgain": "Don’t show me this again", "installExtensionText": "Install the extension for Google Calendar and Office 365 integration"}, "clickandpledge": {"errorDesc": "Please enter valid Click and Pledge Connect GUID. For more details visit - https://connect.clickandpledge.com/", "errorNotification": "Invalid Click and Pledge GUID", "title": "C&P Connect Donation Settings", "titlenative": "<PERSON><PERSON> and Pledge"}, "connectingOverlay": {"joiningRoom": "Connecting you to your meeting..."}, "connection": {"ATTACHED": "Attached", "AUTHENTICATING": "Authenticating", "AUTHFAIL": "Authentication failed", "CONNECTED": "Connected", "CONNECTING": "Connecting", "CONNFAIL": "Connection failed", "DISCONNECTED": "Disconnected", "DISCONNECTING": "Disconnecting", "ERROR": "Error", "FETCH_SESSION_ID": "Obtaining session-id...", "GET_SESSION_ID_ERROR": "Get session-id error: {{code}}", "GOT_SESSION_ID": "Obtaining session-id... Done", "LOW_BANDWIDTH": "Video for {{displayName}} has been turned off to save bandwidth"}, "connectionindicator": {"address": "Address:", "audio_ssrc": "Audio SSRC:", "bandwidth": "Estimated bandwidth:", "bitrate": "Bitrate:", "bridgeCount": "Server count: ", "codecs": "Codecs (A/V): ", "connectedTo": "Connected to:", "e2e_rtt": "E2E RTT:", "framerate": "Frame rate:", "less": "Show less", "localaddress": "Local address:", "localaddress_plural": "Local addresses:", "localport": "Local port:", "localport_plural": "Local ports:", "maxEnabledResolution": "send max", "more": "Show more", "packetloss": "Packet loss:", "participant_id": "Participant id:", "quality": {"good": "Good", "inactive": "Inactive", "lost": "Lost", "nonoptimal": "Nonoptimal", "poor": "Poor"}, "remoteaddress": "Remote address:", "remoteaddress_plural": "Remote addresses:", "remoteport": "Remote port:", "remoteport_plural": "Remote ports:", "resolution": "Resolution:", "savelogs": "Save logs", "status": "Connection:", "transport": "Transport:", "transport_plural": "Transports:", "video_ssrc": "Video SSRC:"}, "dateUtils": {"earlier": "Earlier", "today": "Today", "yesterday": "Yesterday"}, "deepLinking": {"appNotInstalled": "Use our {{app}} mobile app to join this meeting on your phone.", "continueWithBrowser": "Continue with <PERSON><PERSON><PERSON>", "description": "Nothing happened? We tried launching your meeting in the {{app}} desktop app. Try again or launch it in the {{app}} web app.", "descriptionWithoutWeb": "Nothing happened? We tried launching your meeting in the {{app}} desktop app.", "downloadApp": "Download the app", "ifDoNotHaveApp": "If you don't have the app yet:", "ifHaveApp": "If you already have the app:", "ifYouDontHaveTheAppYet": "If you don't have the app yet", "joinInApp": "Join this meeting using the app", "joinMeetingWithDesktopApp": "Join Meeting with Desktop App", "launchMeetingInDesktopApp": "Launch Meeting in Desktop App", "launchWebButton": "Launch in web", "title": "Launching your meeting in {{app}}...", "tryAgainButton": "Try again in desktop"}, "defaultLink": "e.g. {{url}}", "defaultNickname": "ex. <PERSON>", "deviceError": {"cameraError": "Failed to access your camera", "cameraPermission": "Error obtaining camera permission", "microphoneError": "Failed to access your microphone", "microphonePermission": "Error obtaining microphone permission"}, "deviceSelection": {"noPermission": "Permission not granted", "previewUnavailable": "Preview unavailable", "selectADevice": "Select a device", "testAudio": "Play a test sound"}, "dialog": {"accessibilityLabel": {"liveStreaming": "Live Stream"}, "add": "Add", "allow": "Allow", "alreadySharedVideoMsg": "Another participant is already sharing a video. This conference allows only one shared video at a time.", "alreadySharedVideoTitle": "Only one shared video is allowed at a time", "applicationWindow": "Application window", "authenticationRequired": "Authentication required", "Back": "Back", "cameraConstraintFailedError": "Your camera does not satisfy some of the required constraints.", "cameraNotFoundError": "Camera was not found.", "cameraNotSendingData": "We are unable to access your camera. Please check if another application is using this device, select another device from the settings menu or try to reload the application.", "cameraNotSendingDataTitle": "Unable to access camera", "cameraPermissionDeniedError": "You have not granted permission to use your camera. You can still join the conference but others won't see you. Use the camera button in the address bar to fix this.", "cameraTimeoutError": "Could not start video source. Timeout occured!", "cameraUnknownError": "Cannot use camera for an unknown reason.", "cameraUnsupportedResolutionError": "Your camera does not support required video resolution.", "Cancel": "Cancel", "cannotToggleScreenSharingNotSupported": "Cannot toggle screen sharing: not supported.", "close": "Close", "closingAllTerminals": "Closing all Terminals", "conferenceDisconnectMsg": "You may want to check your network connection. Reconnecting in {{seconds}} sec...", "conferenceDisconnectTitle": "You have been disconnected.", "conferenceReloadMsg": "We're trying to fix this. Reconnecting in {{seconds}} sec...", "conferenceReloadTitle": "Unfortunately, something went wrong.", "confirm": "Confirm", "confirmNo": "No", "confirmYes": "Yes", "connectError": "Oops! Something went wrong and we couldn't connect to the conference.", "connectErrorWithMsg": "Oops! Something went wrong and we couldn't connect to the conference: {{msg}}", "connecting": "Connecting", "contactSupport": "Contact support", "copied": "<PERSON>pied", "copy": "Copy", "customAwsRecording": "Custom Aws Recording", "deleteCache": "Delete cache ", "dismiss": "<PERSON><PERSON><PERSON>", "displayNameRequired": "Hi! What’s your name?", "displayUserName": "", "donationCNotificationTitle": "Donate via Click and Pledge", "donationCNPLabel": "Enter Connect Form URL or Widget URL", "donationLabel": "Enter Donation Campaign url", "donationNotificationDescription": "Donate us to support our cause ", "donationNotificationTitle": "Donate via Donorbox", "done": "Done", "e2eeDescription": "End-to-End Encryption is currently EXPERIMENTAL. Please keep in mind that turning on end-to-end encryption will effectively disable server-side provided services such as: recording, live streaming and phone participation. Also keep in mind that the meeting will only work for people joining from browsers with support for insertable streams.", "e2eeLabel": "Enable End-to-End Encryption", "e2eeWarning": "WARNING: Not all participants in this meeting seem to have support for End-to-End encryption. If you enable it they won't be able to see nor hear you.", "embedMeeting": "Embed meeting", "enterCdonation": "Example: https://connect.clickandpledge.com/w/Form/283ah-0069-441a-ba0f-d42a9dec9821", "enterDisplayEmail": "Email ID", "enterDisplayName": "Full Name", "enterDisplayNameToJoin": "Please enter your name to join", "guestUser": "Guest User", "enterDonation": "Example: https://donorbox.org/donate-an-organisation", "enterMeetingId": "Enter Meeting ID", "enterMeetingPassword": "Enter Meeting Password", "error": "Error", "errorMeetingID": "Add Meeting ID", "errorMeetingPassword": "Add Meeting Password", "forceMuteEveryoneDialog": "Are you sure you want to lock microphone of everyone except moderators? They won’t be able to unlock microphone themself, but once you undo force mute, they can unmute & talk.", "forceMuteEveryoneElseDialog": "Mute them as well as disable their microphone", "forceMuteEveryoneElsesVideoDialog": "Once the camera is disabled, They wont be able to enable their camera", "forceMuteEveryoneElsesVideoTitle": "Force Mute everyone's camera except {{whom}}?", "forceMuteEveryoneElseTitle": "Force Mute everyone except {{whom}}?", "forceMuteEveryoneSelf": "yourself", "forceMuteEveryoneStartMuted": "Everyone starts force muted from now on", "forceMuteEveryonesVideoDialog": "Are you sure you want to lock video of this participant? He/She won’t be able to unlock video", "forceMuteEveryonesVideoTitle": "Force Mute everyone's Video?", "forceMuteEveryoneTitle": "Force Mute everyone?", "forceMuteParticipantBody": "Force Mute participant.", "forceMuteParticipantButton": "Force Mute", "forceMuteParticipantDialog": "Are you sure you want to lock microphone of this participant? He/She won’t be able to unlock microphone, but once you undo force mute, he/she can unmute & talk.", "forceMuteParticipantsVideoBody": "THe participants video will turned off and they wont be able to turn back on again", "forceMuteParticipantsVideoButton": "Disable camera", "forceMuteParticipantsVideoTitle": "Disable camera of this participant?", "forceMuteParticipantTitle": "Force Mute this participant?", "gracefulShutdown": "Our service is currently down for maintenance. Please try again later.", "grantModeratorDialog": "Are you sure you want to make this participant a moderator?", "grantModeratorTitle": "Grant moderator", "hangUpLeaveReason": "This Meeting has been ended by Moderator", "hideShareAudioHelper": "Don't show this dialog again", "IamHost": "I am the host", "incorrectPassword": "Incorrect username or password", "incorrectRoomLockPassword": "Incorrect password", "internalError": "Oops! Something went wrong. The following error occurred: {{error}}", "internalErrorTitle": "Internal error", "kickMessage": "Ouch! You have been removed out of the meet!", "kickParticipantButton": "Remove User", "kickParticipantDialog": "Are you sure you want to remove this participant?", "kickParticipantTitle": "Remove this participant?", "kickTitle": "Ouch! You have been removed out of the meeting", "liveStreaming": "Live Streaming", "liveStreamingDisabledBecauseOfActiveRecordingTooltip": "Not possible while recording is active", "liveStreamingDisabledForGuestTooltip": "Guests can't start live streaming.", "liveStreamingDisabledTooltip": "Start live stream disabled.", "localUserControls": "Local user controls", "lockMessage": "Failed to lock the conference.", "lockRoom": "Add meeting $t(lockRoomPasswordUppercase)", "lockTitle": "Lock failed", "login": "<PERSON><PERSON>", "logoutQuestion": "Are you sure you want to logout and stop the conference?", "logoutTitle": "Logout", "maxUsersLimitReached": "The limit for maximum number of participants has been reached. The conference is full. Please contact the meeting owner or try again later!", "maxUsersLimitReachedTitle": "Maximum participants limit reached", "meetHourRecording": "Meet Hour Recording", "meetingID": "Meeting ID", "meetingIDandPassword": "Enter Meeting ID and Password", "meetingPassword": "Meeting Password", "messageErrorApi": "oops! Something Went Wrong", "messageErrorInvalid": "Invalid Credentials", "messageErrorNotModerator": "Oops! you are not a moderator of this meeting", "messageErrorNull": "Username or Password is Empty", "micConstraintFailedError": "Your microphone does not satisfy some of the required constraints.", "micNotFoundError": "Microphone was not found.", "micNotSendingData": "Go to your computer's settings to unmute your mic and adjust its level", "micNotSendingDataTitle": "Your mic is muted by your system settings", "micPermissionDeniedError": "You have not granted permission to use your microphone. You can still join the conference but others won't hear you. Use the camera button in the address bar to fix this.", "micTimeoutError": "Could not start audio source. Timeout occured!", "micUnknownError": "Cannot use microphone for an unknown reason.", "muteEveryoneDialog": "Are you sure you want to mute everyone? You won't be able to unmute them, but they can unmute themselves at any time.", "muteEveryoneElseDialog": "Once muted, you won't be able to unmute them, but they can unmute themselves at any time.", "muteEveryoneElsesVideoDialog": "Once the camera is disabled, you won't be able to turn it back on, but they can turn it back on at any time.", "muteEveryoneElsesVideoTitle": "Disable everyone's camera except {{whom}}?", "muteEveryoneElseTitle": "Mute everyone except {{whom}}?", "muteEveryoneSelf": "yourself", "muteEveryoneStartMuted": "Everyone starts muted from now on", "muteEveryonesVideoDialog": "Are you sure you want to disable everyone's camera? You won't be able to turn it back on, but they can turn it back on at any time.", "muteEveryonesVideoDialogOk": "Disable", "muteEveryonesVideoTitle": "Disable everyone's camera?", "muteEveryoneTitle": "Mute everyone?", "muteParticipantBody": "You won't be able to unmute them, but they can unmute themselves at any time.", "muteParticipantButton": "Mute", "muteParticipantDialog": "Are you sure you want to mute this participant? You won't be able to unmute them, but they can unmute themselves at any time.", "muteParticipantsVideoBody": "You won't be able to turn the camera back on, but they can turn it back on at any time.", "muteParticipantsVideoButton": "Disable camera", "muteParticipantsVideoDialog": "Are you sure you want to turn off this participant's camera? You won't be able to turn the camera back on, but they can turn it back on at any time.", "muteParticipantsVideoTitle": "Disable camera of this participant?", "muteParticipantTitle": "Mute this participant?", "noDropboxToken": "No valid Dropbox token", "noScreensharingInAudioOnlyMode": "No screensharing in audio only mode", "Ok": "OK", "password": "Password", "passwordLabel": "The meeting has been locked by a Moderator. Please enter the $t(lockRoomPassword) to join.", "passwordNotSupported": "Setting a meeting $t(lockRoomPassword) is not supported.", "passwordNotSupportedTitle": "$t(lockRoomPasswordUppercase) not supported", "passwordRequired": "$t(lockRoomPasswordUppercase) required", "permissionCameraRequiredError": "Camera permission is required to participate in conferences with video. Please grant it in Settings", "permissionErrorTitle": "Permission required", "permissionMicRequiredError": "Microphone permission is required to participate in conferences with audio. Please grant it in Settings", "popupError": "Your browser is blocking pop-up windows from this site. Please enable pop-ups in your browser's security settings and try again.", "popupErrorTitle": "Pop-up blocked", "readMore": "more", "recording": "Recording", "recordingDisabledBecauseOfActiveLiveStreamingTooltip": "Not possible while a live stream is active", "recordingDisabledForGuestTooltip": "Guests can't start recordings.", "recordingDisabledTooltip": "Start recording disabled.", "rejoinNow": "Rejoin now", "remoteControlAllowedMessage": "{{user}} accepted your remote control request!", "remoteControlDeniedMessage": "{{user}} rejected your remote control request!", "remoteControlErrorMessage": "An error occurred while trying to request remote control permissions from {{user}}!", "remoteControlRequestMessage": "Will you allow {{user}} to remotely control your desktop?", "remoteControlShareScreenWarning": "Note that if you press \"Allow\" you will share your screen!", "remoteControlStopMessage": "The remote control session ended!", "remoteControlTitle": "Remote desktop control", "remoteUserControls": "Remote user controls of {{username}}", "Remove": "Remove", "removeCDonation": "Click and Pledge guid removed", "removeCDonationD": "The donation link has been removed successfully", "removeDonation": "Donorbox donation link removed", "removeDonationD": "The donation link has been removed successfully", "removePassword": "Remove $t(lockRoomPassword)", "removeSharedVideoMsg": "Are you sure you would like to remove your shared video?", "removeSharedVideoTitle": "Remove shared video", "reservationError": "Reservation system error", "reservationErrorMsg": "Error code: {{code}}, message: {{msg}}", "restartInitiatedBecauseOfBridgeFailure": "Restart initiated because of a bridge failure", "retry": "Retry", "revokeModeration": "Revoke the user as a Moderator?", "revokeModerationTitle": "Revoke Moderation", "screenSharingAudio": "Share audio", "screenSharingFailed": "Oops! Something went wrong, we weren’t able to start screen sharing!", "screenSharingFailedTitle": "Screen sharing failed!", "screenSharingPermissionDeniedError": "Oops! Something went wrong with your screen sharing permissions. Please reload and try again.", "screenSharingUser": "{{displayName}} is Currently Sharing the Screen", "sendPrivateMessage": "You recently received a private message. Did you intend to reply to that privately, or you want to send your message to the group?", "sendPrivateMessageCancel": "Send to the group", "sendPrivateMessageOk": "Send privately", "sendPrivateMessageTitle": "Send privately?", "serviceUnavailable": "Service unavailable", "sessionRestarted": "Call restarted by the bridge", "sessTerminated": "Call terminated", "Share": "Share", "shareAudio": "Continue", "shareAudioTitle": "How to share audio", "shareAudioWarningD1": "you need to stop screen sharing before sharing your audio.", "shareAudioWarningD2": "you need to restart your screen sharing and check the \"share audio\" option.", "shareAudioWarningH1": "If you want to share just audio:", "shareAudioWarningTitle": "You need to stop screen sharing before sharing audio", "shareMediaWarningGenericH2": "If you want to share your screen and audio", "shareScreenWarningD1": "you need to stop audio sharing before sharing your screen.", "shareScreenWarningD2": "you need to stop audio sharing, start screen sharing and check the \"share audio\" option.", "shareScreenWarningH1": "If you want to share just your screen:", "shareScreenWarningTitle": "You need to stop audio sharing before sharing your screen", "shareVideoLinkError": "Please provide a correct youtube link.", "shareVideoTitle": "Share Youtube", "shareYourScreen": "Share your screen", "shareYourScreenDisabled": "Screen sharing disabled.", "shareYourScreenDisabledForGuest": "Guests can't screen share.", "startLiveStreaming": "Live stream + Recording", "startRecording": "Start Recording", "startRemoteControlErrorMessage": "An error occurred while trying to start the remote control session!", "stopLiveStreaming": "Stop Streaming", "stopRecording": "Stop recording", "stopRecordingWarning": "Are you sure you would like to stop the recording?", "stopStreamingWarning": "Are you sure you would like to stop the live streaming?", "streamKey": "Live stream key", "Submit": "Submit", "switchInProgress": "Switch in progress.", "thankYou": "Thank you for using {{appName}}!", "token": "token", "tokenAuthFailed": "Sorry, you're not allowed to join this call.", "tokenAuthFailedTitle": "Authentication failed", "transcribing": "Transcribing", "unforceMuteEveryoneDialog": "Are you sure you want to unlock microphone of everyone? they can unmute & talk.", "unforceMuteEveryoneElseDialog": "<PERSON>do Mute them and let them enable their microphone", "unforceMuteEveryoneElsesVideoDialog": "Once the camera is enable, They will be able to enable their camera", "unforceMuteEveryoneElsesVideoTitle": "Enable everyone's camera except {{whom}}?", "unforceMuteEveryoneElseTitle": "Undo Force Mute everyone except {{whom}}?", "unforceMuteEveryoneSelf": "yourself", "unforceMuteEveryonesVideoDialog": "Are you sure you want to unlock video of everyone?", "unforceMuteEveryonesVideoTitle": "Enable everyone's camera?", "unforceMuteEveryoneTitle": "Undo Force Mute everyone's microphone?", "unforceMuteParticipantBody": "Undo Mute participant.", "unforceMuteParticipantButton": "Undo Force Mute", "unforceMuteParticipantDialog": "Are you sure you want to unlock video of this participant?.", "unforceMuteParticipantsVideoBody": "The participants video will be turned on and they will be able to turn back on again", "unforceMuteParticipantsVideoButton": "Enable camera", "unforceMuteParticipantsVideoTitle": "Unlock video of this participant?", "unforceMuteParticipantTitle": "Undo Force Mute this participant?", "unlockRoom": "Remove meeting $t(lockRoomPassword)", "user": "User", "userIdentifier": "User identifier", "userPassword": "User password", "videoLink": "Video link", "viewUpgradeOptions": "View upgrade options", "viewUpgradeOptionsContent": "To get unlimited access to premium features like recording, transcriptions, RTMP Streaming & more, you'll need to upgrade your plan.", "viewUpgradeOptionsTitle": "You discovered a premium feature!", "WaitForHostMsg": "The conference <b>{{room}}</b> has not yet started. If you are the host then please authenticate. Otherwise, please wait for the host to arrive.", "WaitForHostMsgWOk": "The conference <b>{{room}}</b> has not yet started. If you are the host then please press Ok to authenticate. Otherwise, please wait for the host to arrive.", "WaitforModerator": "Please wait for the Moderator to arrive", "WaitforModeratorOk": "Go back", "WaitingForHost": "Waiting for the host ...", "WaitingForHostTitle": "Waiting for the host ...", "Yes": "Yes", "yourEntireScreen": "Your entire screen"}, "dialOut": {"statusMessage": "is now {{status}}"}, "documentSharing": {"title": "LivePad"}, "donorbox": {"errorDesc": "Please enter valid Donorbox campaign URL. For more details visit - www.donorbox.org", "errorNotification": "Invalid Donorbox URL", "title": "Add DonorBox Campaign URL", "titlenative": "DONORBOX"}, "e2ee": {"labelToolTip": "Audio and Video Communication on this call is end-to-end encrypted"}, "embedMeeting": {"title": "Embed this meeting"}, "feedback": {"average": "Average", "bad": "Bad", "detailsLabel": "Tell us more about it.", "good": "Good", "rateExperience": "Rate your meeting experience", "star": "Star", "veryBad": "Very Bad", "veryGood": "Very Good"}, "giphy": {"giphy": "GIPHY", "noResults": "No results found :(", "search": "Search GIPHY"}, "helpView": {"header": "Help center"}, "incomingCall": {"answer": "Answer", "audioCallTitle": "Incoming call", "decline": "<PERSON><PERSON><PERSON>", "productLabel": "from Meet Hour", "videoCallTitle": "Incoming video call"}, "info": {"accessibilityLabel": "Show info", "addPassword": "Add $t(lockRoomPassword)", "cancelPassword": "Cancel $t(lockRoomPassword)", "conferenceURL": "Link:", "copyNumber": "Copy number", "country": "Country", "dialANumber": "To join your meeting, dial one of these numbers and then enter the pin.", "dialInConferenceID": "PIN:", "dialInNotSupported": "Sorry, dialing in is currently not supported.", "dialInNumber": "Dial-in:", "dialInSummaryError": "Error fetching dial-in info now. Please try again later.", "dialInTollFree": "Toll Free", "genericError": "Whoops, something went wrong.", "inviteLiveStream": "To view the live stream of this meeting, click this link: {{url}}", "invitePhone": "To join by phone instead, tap this: {{number}},,{{conferenceID}}#\n", "invitePhoneAlternatives": "Looking for a different dial-in number?\nSee meeting dial-in numbers: {{url}}\n\n\nIf also dialing-in through a room phone, join without connecting to audio: {{silentUrl}}", "inviteSipEndpoint": "To join using the SIP address, enter this: {{sipUri}}", "inviteTextiOSInviteUrl": "Click the following link to join: {{inviteUrl}}.", "inviteTextiOSJoinSilent": "If you are dialing-in through a room phone, use this link to join without connecting to audio: {{silentUrl}}.", "inviteTextiOSPersonal": "{{name}} is inviting you to a meeting.", "inviteTextiOSPhone": "To join via phone, use this number: {{number}},,{{conferenceID}}#. If you are looking for a different number, this is the full list: {{didUrl}}.", "inviteURLFirstPartGeneral": "You are invited to join a meeting.", "inviteURLFirstPartPersonal": "{{name}} is inviting you to a meeting.\n", "inviteURLSecondPart": "\nJoin the meeting:\n{{url}}\n", "label": "Dial-in info", "liveStreamURL": "Live stream:", "moreNumbers": "More numbers", "noNumbers": "No dial-in numbers.", "noPassword": "None", "noRoom": "No room was specified to dial-in into.", "numbers": "Dial-in Numbers", "password": "$t(lockRoomPasswordUppercase):", "sip": "SIP address", "title": "Share", "tooltip": "Share link and dial-in info for this meeting"}, "inlineDialogFailure": {"msg": "We stumbled a bit.", "retry": "Try again", "support": "Support", "supportMsg": "If this keeps happening, reach out to"}, "inviteDialog": {"alertText": "Failed to invite some participants.", "header": "Invite", "searchCallOnlyPlaceholder": "Enter phone number", "searchPeopleOnlyPlaceholder": "Search for participants", "searchPlaceholder": "Participant or phone number", "send": "Send"}, "jitsiHome": "{{logo}} Logo, links to  Homepage", "keyboardShortcuts": {"focusLocal": "Focus on your video", "focusRemote": "Focus on another person's video", "fullScreen": "View or exit full screen", "keyboardShortcuts": "Keyboard shortcuts", "localRecording": "Show or hide local recording controls", "mute": "Mute or unmute your microphone", "pushToTalk": "Push to talk", "raiseHand": "Raise or lower your hand", "showSpeakerStats": "Show speaker stats", "toggleChat": "Open or close the chat", "toggleFilmstrip": "Show or hide video thumbnails", "toggleParticipantsPane": "Show or hide the participants pane", "toggleScreensharing": "Switch between camera and screen sharing", "toggleShortcuts": "Show or hide keyboard shortcuts", "videoMute": "Start or stop your camera", "videoQuality": "Manage call quality"}, "liveChatView": {"header": "24x7 Live Support"}, "liveStreaming": {"addStream": "Add Destination", "busy": "We're working on freeing streaming resources. Please try again in a few minutes.", "busyTitle": "All streamers are currently busy", "changeSignIn": "Switch accounts.", "choose": "Choose a live stream", "chooseCTA": "Choose a streaming option. You're currently logged in as {{email}}.", "enterLinkedInUrlWithTheKey": "Enter LinkedIn url with the key", "enterStreamKey": "Enter your YouTube live stream key here.", "enterStreamKeyFacebook": "Enter your Facebook live stream key here.", "enterStreamKeyInstagram": "Enter your Instagram live stream key here.", "enterStreamKeyYouTube": "Enter your {{youtube}} live stream key here.", "error": "Live Streaming failed. Please try again.", "errorAPI": "An error occurred while accessing your YouTube broadcasts. Please try logging in again.", "errorLiveStreamNotEnabled": "Live Streaming is not enabled on {{email}}. Please enable live streaming or log into an account with live streaming enabled.", "expandedOff": "The live streaming has stopped", "expandedOn": "The meeting is currently being streamed to YouTube.", "expandedPending": "The live streaming is being started...", "failedToStart": "Live Streaming failed to start", "failToStartAutoLiveStreaming": "Fail to Start Auto Live Streaming", "failToStartAutoRecording": "Fail to Start Auto Recording", "getStreamKeyManually": "We weren’t able to fetch any live streams. Try getting your live stream key from YouTube.", "googlePrivacyPolicy": "Google Privacy Policy", "invalidStreamKey": "Live stream key may be incorrect.", "limitNotificationDescriptionNative": "Your streaming will be limited to {{limit}} min. For unlimited streaming try {{app}}.", "limitNotificationDescriptionWeb": "Due to high demand your streaming will be limited to {{limit}} min. For unlimited streaming try <a href={{url}} rel='noopener noreferrer' target='_blank'>{{app}}</a>.", "makeSureYouHaveEnoughStorageAvailableOnYourAccount": "Make sure you have enough storage available on your account.", "note": "Note", "off": "Live Streaming stopped", "offBy": "{{name}} stopped the live streaming", "on": "Live Streaming started", "onBy": "{{name}} started the live streaming", "pending": "Starting Live Stream...", "pleaseContactSupportForAssistance": "Please contact support for assistance.", "serviceName": "Live Streaming service", "signedInAs": "You are currently signed in as:", "signIn": "Sign in with Google", "signInCTA": "Sign in or enter your live stream key from YouTube.", "signOut": "Sign out", "start": "Recording + Live Stream", "startService": "Start Service", "streamIdHelp": "What's this?", "unavailableTitle": "Live Streaming unavailable", "youtubeTerms": "YouTube terms of services"}, "lobby": {"admit": "Admit", "admitAll": "Admit all", "allow": "Allow", "backToKnockModeButton": "No password, ask to join instead", "dialogTitle": "Lobby mode", "disableDialogContent": "Lobby mode is currently enabled. This feature ensures that unwanted participants can't join your meeting. Do you want to disable it?", "disableDialogSubmit": "Disable", "emailField": "Enter your email address", "enableDialogPasswordField": "Set password (optional)", "enableDialogSubmit": "Enable", "enableDialogText": "Lobby mode lets you protect your meeting by only allowing people to enter after a formal approval by a moderator.", "enterPasswordButton": "Enter meeting password", "enterPasswordTitle": "Enter password to join meeting", "invalidPassword": "Invalid password", "joiningMessage": "You'll join the meeting as soon as someone accepts your request", "joiningTitle": "Asking to join meeting...", "joiningWithPasswordTitle": "Joining with password...", "joinRejectedMessage": "Your join request was rejected by a moderator.", "joinTitle": "Join Meeting", "joinWithPasswordMessage": "Trying to join with password, please wait...", "knockButton": "Ask to Join", "knockingParticipantList": "Knocking participant list", "knockTitle": "Someone wants to join the meeting", "nameField": "Enter your name", "notificationLobbyAccessDenied": "{{targetParticipantName}} has been rejected to join by {{originParticipantName}}", "notificationLobbyAccessGranted": "{{targetParticipantName}} has been allowed to join by {{originParticipantName}}", "notificationLobbyDisabled": "<PERSON><PERSON> has been disabled by {{originParticipantName}}", "notificationLobbyEnabled": "Lobby has been enabled by {{originParticipantName}}", "notificationTitle": "Lobby", "passwordField": "Enter meeting password", "passwordJoinButton": "Join", "reject": "Reject", "rejectAll": "Reject all", "toggleLabel": "Enable lobby"}, "localRecording": {"clientState": {"off": "Off", "on": "On", "unknown": "Unknown"}, "dialogTitle": "Local Recording Controls", "duration": "Duration", "durationNA": "N/A", "encoding": "Encoding", "label": "LOR", "labelToolTip": "Local recording is engaged", "localRecording": "Local Recording", "me": "Me", "messages": {"engaged": "Local recording engaged.", "finished": "Recording session {{token}} finished. Please send the recorded file to the moderator.", "finishedModerator": "Recording session {{token}} finished. The recording of the local track has been saved. Please ask the other participants to submit their recordings.", "notModerator": "You are not the moderator. You cannot start or stop local recording."}, "moderator": "Moderator", "no": "No", "participant": "Participant", "participantStats": "Participant Stats", "sessionToken": "Session Token", "start": "Start Recording", "stop": "Stop Recording", "yes": "Yes"}, "lockRoomPassword": "password", "lockRoomPasswordUppercase": "Password", "lonelyMeetingExperience": {"button": "Invite others", "youAreAlone": "You are the only one in the meeting"}, "me": "me", "notify": {"connectedOneMember": "{{name}} joined the meeting", "connectedThreePlusMembers": "{{name}} and {{count}} others joined the meeting", "connectedTwoMembers": "{{first}} and {{second}} joined the meeting", "disconnected": "disconnected", "focus": "Conference focus", "focusFail": "{{component}} not available - retry in {{ms}} sec", "grantedTo": "Moderator rights granted to {{to}}!", "groupTitle": "Notifications", "hostAskedUnmute": "The host would like you to unmute", "invitedOneMember": "{{name}} has been invited", "invitedThreePlusMembers": "{{name}} and {{count}} others have been invited", "invitedTwoMembers": "{{first}} and {{second}} have been invited", "kickParticipant": "{{kicked}} was removed by {{kicker}}", "me": "Me", "moderationInEffectCSDescription": "Please raise hand if you want to share your video", "moderationInEffectCSTitle": "Content sharing is disabled by moderator", "moderationInEffectDescription": "Please raise hand if you want to speak", "moderationInEffectTitle": "The microphone is muted by the moderator", "moderationInEffectVideoDescription": "Please raise your hand if you want your video to be visible", "moderationInEffectVideoTitle": "The video is muted by the moderator", "moderationRequestFromModerator": "The host would like you to unmute", "moderationRequestFromParticipant": "Wants to speak", "moderationStartedTitle": "Moderation started", "moderationStoppedTitle": "Moderation stopped", "moderationToggleDescription": "by {{participantDisplayName}}", "moderator": "Moderator rights granted!", "muted": "You have started the conversation muted.", "mutedRemotelyDescription": "You can always unmute when you're ready to speak. Mute back when you're done to keep noise away from the meeting.", "mutedRemotelyTitle": "You have been muted by {{participantDisplayName}}!", "mutedTitle": "You're muted!", "newDeviceAction": "Use", "newDeviceAudioTitle": "New audio device detected", "newDeviceCameraTitle": "New camera detected", "OldElectronAPPTitle": "Security vulnerability!", "oldElectronClientDescription1": "You appear to be using an old version of the Meet Hour client which has known security vulnerabilities. Please make sure you update to our ", "oldElectronClientDescription2": "latest build", "oldElectronClientDescription3": " now!", "passwordRemovedRemotely": "$t(lockRoomPasswordUppercase) removed by another participant", "passwordSetRemotely": "$t(lockRoomPasswordUppercase) set by another participant", "raisedHand": "{{name}} would like to speak.", "raiseHandAction": "Raise hand", "reactionSounds": "Disable sounds", "reactionSoundsForAll": "Disable sounds for all", "screenShareNoAudio": " Share audio box was not checked in the window selection screen.", "screenShareNoAudioTitle": "Couldn't share system audio!", "somebody": "Somebody", "startSilentDescription": "Rejoin the meeting to enable audio", "startSilentTitle": "You joined with no audio output!", "suboptimalBrowserWarning": "We are afraid your meeting experience isn't going to be that great here. We are looking for ways to improve this, but until then please try using one of the <a href='{{recommendedBrowserPageLink}}' target='_blank'>fully supported browsers</a>.", "suboptimalExperienceTitle": "Browser Warning", "unmute": "Unmute", "videoMutedRemotelyDescription": "You can always turn it on again.", "videoMutedRemotelyTitle": "Your camera has been disabled by {{participantDisplayName}}!"}, "participantsPane": {"actions": {"allow": "Allow attendees to:", "askUnmute": "Ask to unmute", "blockEveryoneMicCamera": "Block everyone's mic and camera", "breakoutRooms": "Breakout Rooms", "forceMute": "Force Mute Audio", "forceMuteAll": "Force Mute All", "forceMuteAllVideo": "Force Video Mute All", "forceMuteEveryoneElse": "Force Mute Everyone Else", "forceMuteEveryoneElseVideo": "Force Mute Everyone Else Video", "forceMuteVideo": "Force Mute Video", "invite": "Invite <PERSON>", "mute": "Mute", "muteAll": "Mute all", "muteEveryoneElse": "Mute everyone else", "startModeration": "Unmute themselves or start video", "stopEveryonesVideo": "Stop everyone's video", "stopVideo": "Stop video", "unblockEveryoneMicCamera": "Unblock everyone's mic and camera", "unForceMute": "undo force Mute Audio", "unForceMuteAll": "Undo Force Mute All", "unforceMuteAllVideo": "Undo Force Video Mute All", "unforceMuteEveryoneElse": "Undo force Mute Everyone Else", "unforceMuteEveryoneElseVideo": "Undo force Mute Everyone Else Video", "unForceMuteVideo": "Undo force Mute Video"}, "close": "Close", "header": "Participants", "headings": {"lobby": "Lobby ({{count}})", "participantsList": "Meeting participants ({{count}})", "waitingLobby": "Waiting in lobby ({{count}})"}, "search": "Search participants"}, "passwordDigitsOnly": "Up to {{number}} digits", "passwordSetRemotely": "set by another participant", "polls": {"answer": {"skip": "<PERSON><PERSON>", "submit": "Submit"}, "by": "By {{ name }}", "create": {"addOption": "Add Option", "answerPlaceholder": "Option {{index}}", "cancel": "Cancel", "create": "Create A Poll", "pollOption": "Poll option {{index}}", "pollQuestion": "Poll Question", "questionPlaceholder": "Ask a question", "removeOption": "Remove option", "send": "Send"}, "notification": {"description": "Open polls tab to vote", "title": "A new poll was added to this meeting"}, "results": {"changeVote": "Change vote", "empty": "There are no polls in the meeting yet. Start a poll here!", "hideDetailedResults": "Hide details", "showDetailedResults": "Show details", "vote": "Vote"}}, "poweredby": "© Meet Hour LLC", "prejoin": {"alreadyOneConferenceIsRunningInBackground": "Already one conference is running in background.", "audioAndVideoError": "Audio and video error:", "audioDeviceProblem": "There is a problem with your audio device", "audioOnlyError": "Audio error:", "audioTrackError": "Could not create audio track.", "calling": "Calling", "callMe": "Call me", "callMeAtNumber": "Call me at this number:", "configuringDevices": "Configuring devices...", "connectedWithAudioQ": "You’re connected with audio?", "connection": {"good": "Your internet connection looks good!", "nonOptimal": "Your internet connection is not optimal", "poor": "You have a poor internet connection"}, "connectionDetails": {"audioClipping": "We expect your audio to be clipped.", "audioHighQuality": "We expect your audio to have excellent quality.", "audioLowNoVideo": "We expect your audio quality to be low and no video.", "goodQuality": "Awesome! Your media quality is going to be great.", "noMediaConnectivity": "We could not find a way to establish media connectivity for this test. This is typically caused by a firewall or NAT.", "noVideo": "We expect that your video will be terrible.", "undetectable": "If you still can not make calls in browser, we recommend that you make sure your speakers, microphone and camera are properly set up, that you have granted your browser rights to use your microphone and camera, and that your browser version is up-to-date. If you still have trouble calling, you should contact the web application developer.", "veryPoorConnection": "We expect your call quality to be really terrible.", "videoFreezing": "We expect your video to freeze, turn black, and be pixelated.", "videoHighQuality": "We expect your video to have good quality.", "videoLowQuality": "We expect your video to have low quality in terms of frame rate and resolution.", "videoTearing": "We expect your video to be pixelated or have visual artefacts."}, "copyAndShare": "Copy & share meeting link", "dashboard": "Dashboard", "daysAgo": "{{daysCount}} days ago", "dialing": "Dialing", "dialInMeeting": "Dial into the meeting", "dialInPin": "Dial into the meeting and enter PIN code:", "doNotShow": "Don't show this screen again", "enterMeetingIdOrLink": "Enter meeting ID or Link", "errorDialOut": "Could not dial out", "errorDialOutDisconnected": "Could not dial out. Disconnected", "errorDialOutFailed": "Could not dial out. Call failed", "errorDialOutStatus": "Error getting dial out status", "errorMissingEmail": "Please enter your email to join the meeting", "errorMissingName": "Please enter your name to join the meeting", "errorNameLength": "Please enter atleast 3 letters in your name", "errorStatusCode": "Error dialing out, status code: {{status}}", "errorValidation": "Number validation failed", "features": "Features", "guestNotAllowedMsg": "Guest are  Not allowed to join this meeting", "initiated": "Call initiated", "invalidEmail": "<PERSON><PERSON><PERSON>", "iWantToDialIn": "I want to dial in", "joinAMeeting": "Join a Meeting", "joinAudioByPhone": "Join with phone audio", "joinMeeting": "Join meeting", "joinMeetingGuest": "Join meeting as Guest", "joinWithoutAudio": "Join without audio", "keyboardShortcuts": "Enable Keyboard shortcuts", "linkCopied": "Link copied to clipboard", "logout": "Logout", "lookGood": "It sounds like your microphone is working properly", "maximumAllowedParticipantsErr": "Maximum allowed participants have reached on this meetings. Contact Meeting Organizer.", "meetingReminder": "Meeting will commence {{time}}. Please join back on or before Scheduled time.", "multipleConferenceInitiation": "Multiple Conference Initiation", "oops": "Oops!", "oppsMaximumAllowedParticipantsErr": "Oops! Maximum participants allowed limit is reached. Please wait for the particpants to leave or contact the meeting organizer.", "or": "or", "parallelMeetingsLicencesErr": "Unable to start Meeting. Make sure you have an active licences to join Parallel meetings", "peopleInTheCall": "People in the call", "pleaseEnterEmail": "Please Enter Email", "pleaseEnterFullName": "Please Enter Full Name", "premeeting": "Pre meeting", "profile": "Profile", "readyToJoin": "Ready to Join?", "recentMeetings": "Recent Meetings", "screenSharingError": "Screen sharing error:", "showScreen": "Enable pre meeting screen", "signinsignup": "Sign In / Sign Up", "startWithPhone": "Start with phone audio", "subScriptionInactiveErr": "Your subscription is Inactive. Unable to start Meeting.", "systemUpgradedInformation": "We have upgraded our system to 2.0 version. Claim this meeting by sharing this meeting password", "userNotAllowedToJoin": "User not Allowed to Join", "videoOnlyError": "Video error:", "videoTrackError": "Could not create video track.", "viewAllNumbers": "view all numbers", "waitForModeratorMsg": "Please wait while Moderator joins the call.", "waitForModeratorMsgDynamic": "Please wait while {{Moderator}} joins the call.", "youAreNotAllowed": "You are not allowed"}, "presenceStatus": {"busy": "Busy", "calling": "Calling...", "connected": "Connected", "connecting": "Connecting...", "connecting2": "Connecting*...", "disconnected": "Disconnected", "expired": "Expired", "ignored": "Ignored", "initializingCall": "Initializing Call...", "invalidToken": "Invalid <PERSON>", "invited": "Invited", "rejected": "Rejected", "ringing": "Ringing...", "signInAsHost": "Sign In As Host"}, "profile": {"avatar": "avatar", "setDisplayNameLabel": "Your display name", "setEmailInput": "Enter e-mail", "setEmailLabel": "Your email", "title": "Profile"}, "raisedHand": "Would like to speak", "recording": {"authDropboxText": "Upload to Dropbox", "availableS3Space": "Used space: {{s3_used_space}} of {{s3_free_space}}", "availableSpace": "Available space: {{spaceLeft}} MB (approximately {{duration}} minutes of recording)", "beta": "BETA", "busy": "We're working on freeing recording resources. Please try again in a few minutes.", "busyTitle": "All recorders are currently busy", "consentDialog": {"title": "Recording Consent", "message": "This meeting is getting recorded. To continue participating, you must give your consent to get recorded.", "disclaimer": "The recording may be used for documentation, training, or other business purposes. If you do not want to give consent, kindly leave the meeting now.", "accept": "I Agree", "leaveMeeting": "Leave"}, "copyLink": "Copy Link", "error": "Recording failed. Please try again.", "errorFetchingLink": "Error fetching recording link.", "expandedOff": "Recording has stopped", "expandedOn": "The meeting is currently being recorded.", "expandedPending": "Recording is being started...", "failedToStart": "Recording failed to start", "fileSharingdescription": "Share recording with meeting participants", "limitNotificationDescriptionNative": "Due to high demand your recording will be limited to {{limit}} min. For unlimited recordings try <3>{{app}}</3>.", "limitNotificationDescriptionWeb": "Due to high demand your recording will be limited to {{limit}} min. For unlimited recordings try <a href={{url}} rel='noopener noreferrer' target='_blank'>{{app}}</a>.", "linkGenerated": "We have generated a link to your recording.", "live": "LIVE", "loggedIn": "Logged in as {{userName}}", "off": "Recording stopped", "offBy": "{{name}} stopped the recording", "on": "Recording started", "onBy": "{{name}} started the recording", "pending": "Preparing to record the meeting...", "rec": "REC", "recLive": "LIVE + REC", "serviceDescription": "Your recording will be saved by the recording service", "serviceDescriptionCloud": "Cloud recording", "serviceName": "Recording service", "signIn": "Sign in", "signOut": "Sign out", "unavailable": "Oops! The {{serviceName}} is currently unavailable. We're working on resolving the issue. Please try again later.", "unavailableTitle": "Recording unavailable", "uploadToCloud": "Upload to the cloud"}, "sectionList": {"pullToRefresh": "Pull to refresh"}, "security": {"about": "You can add a $t(lockRoomPassword) to your meeting. Participants will need to provide the $t(lockRoomPassword) before they are allowed to join the meeting.", "aboutReadOnly": "Moderator participants can add a $t(lockRoomPassword) to the meeting. Participants will need to provide the $t(lockRoomPassword) before they are allowed to join the meeting.", "insecureRoomNameWarning": "The room name is unsafe. Unwanted participants may join your conference. Consider securing your meeting using the security button.", "securityOptions": "Security options"}, "settings": {"calendar": {"about": "The {{appName}} calendar integration is used to securely access your calendar so it can read upcoming events.", "disconnect": "Disconnect", "microsoftSignIn": "Sign in with Microsoft", "signedIn": "Currently accessing calendar events for {{email}}. Click the Disconnect button below to stop accessing calendar events.", "title": "Calendar"}, "desktopShareFramerate": "Desktop sharing frame rate", "desktopShareHighFpsWarning": "A higher frame rate for desktop sharing might affect your bandwidth. You need to restart the screen share for the new settings to take effect.", "desktopShareWarning": "You need to restart the screen share for the new settings to take effect.", "devices": "Devices", "followMe": "Everyone follows me", "framesPerSecond": "frames-per-second", "incomingMessage": "Incoming message", "language": "Language", "languageSettings": "Language Settings", "loggedIn": "Logged in as {{name}}", "microphones": "Microphones", "moderator": "Moderator", "more": "More", "name": "Name", "noDevice": "None", "noLanguagesAvailable": "No languages available", "participantJoined": "Participant Joined", "participantLeft": "Participant Left", "playSounds": "Play sound on", "sameAsSystem": "Same as system ({{label}})", "selectAudioOutput": "Audio output", "selectCamera": "Camera", "selectLanguage": "Select Language", "selectMic": "Microphone", "sounds": "Sounds", "speakers": "Speakers", "startAudioMuted": "Everyone starts muted", "startVideoMuted": "Everyone starts hidden", "talkWhileMuted": "Talk while muted", "title": "Settings"}, "settingsView": {"advanced": "Advanced", "alertCancel": "Cancel", "alertOk": "OK", "alertTitle": "Warning", "alertURLText": "The entered server URL is invalid", "buildInfoSection": "Build Information", "conferenceSection": "Conference", "disableCallIntegration": "Disable native call integration", "disableCrashReporting": "Disable crash reporting", "disableCrashReportingWarning": "Are you sure you want to disable crash reporting? The setting will be applied after you restart the app.", "disableP2P": "Disable Peer-To-Peer mode", "displayName": "Display name", "email": "Email", "header": "Settings", "profileSection": "Profile", "serverURL": "Server URL", "showAdvanced": "Show advanced settings", "startWithAudioMuted": "Start with audio muted", "startWithVideoMuted": "Start with video muted", "version": "Version"}, "share": {"dialInfoText": "\n\n=====\n\nJust want to dial in on your phone?\n\n{{defaultDialInNumber}}Click this link to see the dial in phone numbers for this meeting\n{{dialInfoPageUrl}}", "mainText": "Click the following link to join the meeting:\n{{roomUrl}}"}, "speaker": "Speaker", "speakerStats": {"hours": "{{count}}h", "minutes": "{{count}}m", "name": "Name", "seconds": "{{count}}s", "speakerStats": "Speaker <PERSON><PERSON>", "speakerTime": "Speaker Time"}, "startupoverlay": {"genericTitle": "The meeting needs to use your microphone and camera.", "policyText": " ", "title": "{{app}} needs to use your microphone and camera."}, "suspendedoverlay": {"rejoinKeyTitle": "Rejoin", "text": "Press the <i>Rejoin</i> button to reconnect.", "title": "Your video call was interrupted because this computer went to sleep."}, "toolbar": {"accessibilityLabel": {"audioOnly": "Toggle audio only", "audioRoute": "Manage sound device", "boo": "<PERSON><PERSON>", "callQuality": "Manage Video Quality", "carmode": "Car Mode", "cc": "Toggle subtitles", "chat": "Open / Close chat", "clap": "<PERSON><PERSON>", "collapse": "Collapse", "document": "Toggle shared document", "donationCLP": "C&P Connect Settings", "donationLink": "DonorBox Settings", "download": "Download our apps", "embedMeeting": "Embed meeting", "expand": "Expand", "feedback": "Leave feedback", "fullScreen": "Toggle full screen", "genericIFrame": "Toggle shared application", "giphy": "Toggle GIPHY menu", "grantModerator": "Grant Moderator", "hangup": "Leave the meeting", "help": "Help", "invite": "Invite people", "kick": "Kick participant", "laugh": "<PERSON><PERSON>", "leaveConference": "Leave meeting", "like": "Thumbs Up", "lobbyButton": "Enable/disable lobby mode", "localRecording": "Toggle local recording controls", "lockRoom": "Toggle meeting password", "moreActions": "More actions", "moreActionsMenu": "More actions menu", "moreOptions": "Show more options", "mute": "Mute / Unmute", "muteEveryone": "Mute everyone", "muteEveryoneElse": "Mute everyone else", "muteEveryoneElsesVideo": "Disable everyone else's camera", "muteEveryonesVideo": "Disable everyone's camera", "participants": "Participants", "party": "Party Popper", "pip": "Toggle Picture-in-Picture mode", "privateMessage": "Send private message", "profile": "Edit your profile", "raiseHand": "Raise / Lower your hand", "reactionsMenu": "Open / Close reactions menu", "recording": "Toggle recording", "remoteMute": "Mute participant", "remoteVideoMute": "Disable camera of participant", "removeDonation": "Remove DonorBox", "rmoveCDonation": "Remove C&P", "security": "Security options", "selectBackground": "Select Background", "Settings": "Toggle settings", "shareaudio": "Share audio", "sharedvideo": "Toggle YouTube video sharing", "shareRoom": "Invite someone", "shareYourScreen": "Start / Stop sharing your screen", "shortcuts": "Toggle shortcuts", "show": "Show on stage", "speakerStats": "Toggle speaker statistics", "surprised": "Surprised", "tileView": "Toggle tile view", "toggleCamera": "Toggle camera", "toggleFilmstrip": "Toggle filmstrip", "toggleReactions": "Toggle reactions", "videoblur": "Toggle video blur", "videomute": "Start / Stop camera"}, "addPeople": "Add people to your call", "audioOnlyOff": "Disable low bandwidth mode", "audioOnlyOn": "Enable low bandwidth mode", "audioRoute": "Manage sound device", "audioSettings": "Audio settings", "authenticate": "Authenticate", "boo": "<PERSON><PERSON>", "callQuality": "Manage Video Quality", "chat": "Open / Close chat", "clap": "<PERSON><PERSON>", "closeChat": "Close chat", "closeParticipantsPane": "Close participants pane", "closeReactionsMenu": "Close reactions menu", "disableNoiseSuppression": "Disable noise suppression", "disableReactionSounds": "You can disable reaction sounds for this meeting", "documentClose": "Close LivePad", "documentOpen": "Share LivePad", "donationCLP": "C&P Connect Settings", "donationLink": "DonorBox Settings", "download": "Download our apps", "e2ee": "End-to-End Encryption", "embedMeeting": "Embed Meeting", "enterFullScreen": "View Full Screen", "enterTileView": "Enter tile view", "exitFullScreen": "Exit Full Screen", "exitTileView": "Exit tile view", "feedback": "Leave feedback", "genericIFrameClose": "Stop WhiteBoard", "genericIFrameOpen": "Share WhiteBoard", "genericIFrameWeb": "White Board", "hangup": "Leave", "hangUpforEveryOne": "Hangup for Everyone", "hangUpforMe": "Hangup only for me", "hangUpText": "Are you sure you want to Hang up?", "help": "Help", "hideReactions": "Hide Reaction", "invite": "Invite people", "inviteViaCalendar": "Invite via calendar", "iOSStopScreenShareAlertMessage": "Kindly stop the screen sharing before <PERSON><PERSON><PERSON>.", "iOSStopScreenShareAlertTitle": "Stop Screen Sharing", "laugh": "<PERSON><PERSON>", "leaveConference": "Leave meeting", "like": "Thumbs Up", "lobbyButtonDisable": "Disable lobby mode", "lobbyButtonEnable": "Enable lobby mode", "login": "<PERSON><PERSON>", "logout": "Logout", "lowerYourHand": "Lower your hand", "moreActions": "More actions", "moreOptions": "More options", "mute": "Mute / Unmute", "muteEveryone": "Mute everyone", "muteEveryonesVideo": "Disable everyone's camera", "noAudioSignalDesc": "If you did not purposely mute it from system settings or hardware, consider switching the device.", "noAudioSignalDescSuggestion": "If you did not purposely mute it from system settings or hardware, consider switching to the suggested device.", "noAudioSignalDialInDesc": "You can also dial-in using:", "noAudioSignalDialInLinkDesc": "Dial-in numbers", "noAudioSignalTitle": "There is no input coming from your mic!", "noisyAudioInputDesc": "It sounds like your microphone is making noise, please consider muting or changing the device.", "noisyAudioInputTitle": "Your microphone appears to be noisy!", "openChat": "Open chat", "openReactionsMenu": "Open reactions menu", "participants": "Participants", "party": "Celebration", "pip": "Picture in Picture mode", "privateMessage": "Send private message", "profile": "Edit your profile", "raiseHand": "Raise / Lower your hand", "raiseYourHand": "Raise your hand", "reactionBoo": "Send boo reaction", "reactionClap": "Send clap reaction", "reactionLaugh": "Send laugh reaction", "reactionLike": "Send thumbs up reaction", "reactionParty": "Send party popper reaction", "reactionSurprised": "Send surprised reaction", "removeDonation": "Remove DonorBox", "rmoveCDonation": "Remove C&P", "security": "Security options", "selectBackground": "Select Background", "Settings": "Settings", "Share": "Share", "shareaudio": "Share audio", "sharedvideo": "Share Youtube", "shareRoom": "Invite someone", "shortcuts": "View Shortcuts", "showReactions": "Show Reaction", "speakerStats": "Speaker <PERSON><PERSON>", "startScreenSharing": "Start screen sharing", "startSubtitles": "Start subtitles", "stopAudioSharing": "Stop audio sharing", "stopScreenSharing": "Stop screen sharing", "stopSharedVideo": "Stop YouTube video", "stopSubtitles": "Stop subtitles", "surprised": "Surprised", "talkWhileMutedPopup": "Trying to speak? You are muted.", "tileViewToggle": "Toggle tile view", "toggleCamera": "Toggle camera", "videomute": "Start / Stop camera", "videoSettings": "Video Settings", "voiceCommand": "Open Voice Command", "whiteBoardOpen": "Share WhiteBoard", "zoomin": "Zoom In", "zoomout": "Zoom Out"}, "transcribing": {"ccButtonTooltip": "Start / Stop subtitles", "error": "Transcribing failed. Please try again.", "expandedLabel": "Transcribing is currently on", "failedToStart": "Transcribing failed to start", "labelToolTip": "The meeting is being transcribed", "off": "Transcribing stopped", "pending": "Preparing to transcribe the meeting...", "start": "Start showing subtitles", "stop": "Stop showing subtitles", "tr": "TR", "sourceLanguageDesc": "Currently the transcription language is set to ", "subtitlesTitle": "Subtitles", "sourceLanguageHere": "You can change it from here", "subtitlesOff": "Off", "NoCaptionsAvailable": "No captions available", "Translating": "Translating", "Transcribing": "Transcribing", "LiveCaptions": "Live Captions", "Transcriptions": "Transcriptions", "TranslateTo": "Translate to", "TranslationNotAvailable": "Translation not available", "TranscribingNotAvailable": "Transcribing Not Available", "TranscriptionLanguageCantChange": "Transcription Language Can't Change", "TranscriptionLanguageCannotBeChangedOngoingCall": "Transcription Language cannot be changed on an ongoing call. Kindly rejoin to take effect.", "TranscriptionLangDefaultNote": "Note: Transcription is unavailable in the selected meeting language {{language}}, so will default to English.", "ClosedCaptions": "Closed Captions", "OpenCloseCaptions": "Open / Close captions", "transcriptionQuotaExceeded": "Transcription quota exceeded for this month", "transcriptionQuotaExceededTitle": "Transcription quota exceeded"}, "userMedia": {"androidGrantPermissions": "Select <b><i>Allow</i></b> when your browser asks for permissions.", "chromeGrantPermissions": "Select <b><i>Allow</i></b> when your browser asks for permissions.", "edgeGrantPermissions": "Select <b><i>Yes</i></b> when your browser asks for permissions.", "electronGrantPermissions": "Please grant permissions to use your camera and microphone", "firefoxGrantPermissions": "Select <b><i>Share Selected Device</i></b> when your browser asks for permissions.", "iexplorerGrantPermissions": "Select <b><i>OK</i></b> when your browser asks for permissions.", "nwjsGrantPermissions": "Please grant permissions to use your camera and microphone", "operaGrantPermissions": "Select <b><i>Allow</i></b> when your browser asks for permissions.", "react-nativeGrantPermissions": "Select <b><i>Allow</i></b> when your browser asks for permissions.", "safariGrantPermissions": "Select <b><i>OK</i></b> when your browser asks for permissions."}, "videoSIPGW": {"busy": "We're working on freeing resources. Please try again in a few minutes.", "busyTitle": "The Room service is currently busy", "errorAlreadyInvited": "{{displayName}} already invited", "errorInvite": "Conference not established yet. Please try again later.", "errorInviteFailed": "We're working on resolving the issue. Please try again later.", "errorInviteFailedTitle": "Inviting {{displayName}} failed", "errorInviteTitle": "Error inviting room", "pending": "{{displayName}} has been invited"}, "videoStatus": {"audioOnly": "AUD", "audioOnlyExpanded": "You are in low bandwidth mode. In this mode you will receive only audio and screen sharing.", "callQuality": "Video Quality", "hd": "HD", "hdTooltip": "Viewing high definition video", "highDefinition": "High definition", "labelTooiltipNoVideo": "No video", "labelTooltipAudioOnly": "Low bandwidth mode enabled", "ld": "LD", "ldTooltip": "Viewing low definition video", "lowDefinition": "Low definition", "onlyAudioAvailable": "Only audio is available", "onlyAudioSupported": "We only support audio in this browser.", "sd": "SD", "sdTooltip": "Viewing standard definition video", "standardDefinition": "Standard definition", "uhd": "UHD", "uhdTooltip": "Viewing ultra high definition video", "uhighDefinition": "Ultra High definition"}, "videothumbnail": {"connectionInfo": "Connection Info", "domute": "Mute", "domuteOthers": "Mute everyone else", "domuteVideo": "Disable camera", "domuteVideoOfOthers": "Disable camera of everyone else", "flip": "Flip", "grantModerator": "Grant Moderator", "kick": "Remove user", "moderator": "Moderator", "mute": "Participant is muted", "muted": "Muted", "remoteControl": "Start / Stop remote control", "show": "Show on stage", "videomute": "<PERSON><PERSON><PERSON><PERSON> has stopped the camera", "videoMuted": "Camera disabled"}, "virtualBackground": {"addBackground": "Add background", "appliedCustomImageTitle": "Uploaded Custom Image", "apply": "Apply", "blur": "Blur", "customImg": "Custom Image", "deleteImage": "Delete image", "desktopShare": "Desktop share", "desktopShareError": "Could not create desktop share", "enableBlur": "Enable blur", "image1": "Beach", "image2": "White neutral wall", "image3": "White empty room", "image4": "Black floor lamp", "image5": "Mountain", "image6": "Forest ", "image7": "Sunrise", "none": "None", "pleaseWait": "Please wait...", "removeBackground": "Remove background", "slightBlur": "Slight Blur", "switchBackgroundTitle": "Switch Background", "title": "Virtual Backgrounds", "uploadedImage": "Uploaded image {{index}}", "virtualImagesTitle": "Virtual Built-In Images", "webAssemblyWarning": "WebAssembly not supported"}, "voicecommand": {"activePIPLabel": "Active PIP", "clickOnMic": "Click  on the the Mic and Voice a command", "hints": {"closeLivePad": "Close LivePad", "closeWhiteboard": "Close Whiteboard", "closeYoutube": "Close Youtube", "hints": "Hints", "invitePeople": "Invite People", "lowerHand": "Lower Hand", "openChatBox": "Open chat box", "openClickAndPledge": "Open click and pledge", "openDonorbox": "Open Donorbox", "openFullScreen": "Open full screen", "openLivePad": "Open LivePad", "openLivestream": "Open Livestream", "openParticipantPane": "Open Participant pane", "openRecording": "Open Recording", "openSettings": "Open settings", "openSpeakerStats": "Open speaker stats", "openVideoQualityDialog": "Open video quality dialog", "openVirtualBackground": "Open virtual background", "openWhiteboard": "Open Whiteboard", "openYoutube": "Open Youtube", "raiseHand": "<PERSON><PERSON>", "removeClickAndPledge": "Remove click and pledge", "removeDonorbox": "Remove Donorbox", "startScreenSharing": "Start Screen sharing", "StopScreenSharing": "Stop Screen sharing"}, "inActivePIPLabel": "In Active PIP", "pleaseWaitWeAreRecording": "Please Wait we're Recording", "vcLabel": "Voice Command", "voiceCommandForMeethour": "Voice Command For Meet Hour"}, "volumeSlider": "Volume slider", "welcomepage": {"accessibilityLabel": {"join": "Ta<PERSON> to join", "roomname": "Enter Meeting ID"}, "addMeetingName": "Add Meeting name", "appDescription": "Go ahead, video chat with the whole team. In fact, invite everyone you know. {{app}} is a fully encrypted, 100% open source video conferencing solution that you can use all day, every day, for free — with no account needed.", "audioVideoSwitch": {"audio": "Voice", "video": "Video"}, "calendar": "Calendar", "connectCalendarButton": "Connect your calendar", "connectCalendarText": "Connect your calendar to view all your meetings in {{app}}. Plus, add {{provider}} meetings to your calendar and start them with one click.", "developerPlan": "Developer Plan", "enterprisePlan": "Enterprise Plan", "enterpriseSelfHostPlan": "Enterprise Self Host Plan", "enterRoomTitle": "Start a New Meeting or Enter Existing Room Name", "features": "Features", "footer": {"allRightsReserved": "All Rights Reserved", "androidAppDownload": "Android App Download", "apiDocumentation": "API Documentation", "app": "App", "blog": "Blog", "company": "Company", "contact": "Contact", "copyright": "Copyright", "copyrightText": "Copyright 2020 - 2024 Meet Hour LLC. All Rights Reserved", "developers": "Developers", "disclaimer": "Disclaimer", "download": "Download", "email": "Email", "faqs": "FAQs", "followUs": "Follow Us", "helpDesk": "Help Desk", "home": "Home", "integrations": "Integrations", "inTheNews": "In The News", "iOSAppDownload": "iOS App Download", "knowledgeBase": "Knowledge Base", "meethour": "Meet Hour", "meethourLLC": "Meet Hour LLC", "officeAddress": "8825 Stanford ,Suite 205 Columbia, MD 21045", "phone": "Phone", "privacyPolicy": "Privacy Policy", "productPresentation": "Product Presentation", "refundCancellationPolicy": "Refund & Cancellation Policy", "termsConditions": "Terms & Conditions", "testimonials": "Testimonials", "webMobileSDK": "Web & Mobile SDK", "whoAreYou": "Who Are You"}, "forHospitals": "For Hospitals", "freeBannerDescription": "Free & Unlimited HD Quality Video Conference like never before. Join meeting from anywhere.", "freePlan": "Free Plan", "getHelp": "FAQs", "go": "Create or Join a Meeting", "goSmall": "Create or Join a Meeting", "header": {"accelerateDevelopmentWithPrebuiltSDKs": "Accelerate development with pre-built SDKs.", "apiStatus": "API Status", "appointmentSchedulingVideoConference": "Appointment Scheduling & Video Conference.", "blog": "Blog", "customIntegrationDedicatedSupport": "Custom Integration & Dedicated Support", "customTailoredVideoMeetings": "Custom-Tailored Video Meetings.", "developer": "Developer", "developers": "Developers", "documentation": "Documentation", "edTech": "EdTech", "eMail": "E-Mail", "engagingOnlineLearningForEducators": "Engaging Online Learning for Educators.", "engagingVirtualEventExperiences": "Engaging Virtual Event Experiences.", "enterprise": "Enterprise", "enterpriseSelfHost": "Enterprise Self Host", "features": "Features", "fitness": "Fitness", "free": "Free", "fundraiseEffortlesslyWithinVideoConferences": "Fundraise Effortlessly Within Video Conferences.", "fundraisingDonate": "Fundraising / Donate", "fundraisingDonateOnline": "Fundraising / Donate Online", "getStarted": "Get Started", "hdQualityVideoConferenceApp": "HD Quality Video Conference App", "help": "Help", "helpDesk": "Help-Desk", "highQualityLiveEventStreaming": "High-Quality Live Event Streaming.", "hostVideoConferenceOnYourServers": "Host video conference on your servers.", "industries": "Industries", "integrateVideoCallWithinYourWebsiteApp": "Integrate Video Call within your website/app.", "interactiveVirtualLearningSolutions": "Interactive Virtual Learning Solutions.", "joinAMeeting": "Join a Meeting", "knowledgeBase": "Knowledge Base", "liveStreaming": "Live Streaming", "meethour": "Meet Hour", "mycaly": "MyCaly", "myCaly": "My Caly", "myCalyPricing": "MyCaly Pricing.", "noAdsRecordingLiveStreaming": "No Ads + Recording + Live Streaming.", "noTimeLimitGroupCalls": "No Time Limit on 1:1 & Group Calls.", "preBuiltSDKs": "Pre-Built SDKs", "pricing": "Pricing", "pro": "Pro", "products": "Products", "resources": "Resources", "scheduleADemo": "Schedule a Demo", "simplifiedAPIReferences": "Simplified API References", "smoothVideoOnboardingExperience": "Smooth Video Onboarding Experience.", "solutions": "Solutions", "stayuptodateWithOurBlog": "Stay up-to-date with our blog", "systemHealthStatusandUpdates": "System health status and updates", "tailoredSolutionsForYourHealthcareNeeds": "Tailored Solutions for Your Healthcare Needs.", "telehealth": "Telehealth", "useCases": "Use Cases", "videoConference": "Video Conference", "videoConferencePlans": "Video Conference Plans", "videoConferencePricing": "Video conference pricing.", "videoConferencing": "Video Conferencing", "videoKYC": "Video KYC", "virtualClassrooms": "Virtual Classrooms", "virtualEvents": "Virtual Events", "virtualSolutionForHomeFitness": "Virtual Solution for Home Fitness.", "webinars": "Webinars", "webinarSessionsWithIndustryLeaders": " Webinar Sessions with Industry Leaders."}, "headerSubtitle": "Secure and HD quality meetings", "headerTitle": "Meet Hour", "info": "Dial-in info", "invalidMeetingID": "Invalid Meeting ID", "jitsiOnMobile": "Meet Hour on mobile – download our apps and start a meeting from anywhere", "join": "CREATE / JOIN", "joinAMeeting": "Join a Meeting", "logo": {"calendar": "Calendar logo", "desktopPreviewThumbnail": "Desktop preview thumbnail", "googleLogo": "Google Logo", "logoDeepLinking": "Jitsi meet logo", "microsoftLogo": "Microsoft logo", "policyLogo": "Policy logo"}, "meetingDate": "Meeting Date", "meetingDetails": "Meeting Details", "meetingIsReady": "Meeting is ready", "mobileDownLoadLinkAndroid": "Download mobile app for Android", "mobileDownLoadLinkFDroid": "Download mobile app for F-Droid", "mobileDownLoadLinkIos": "Download mobile app for iOS", "moderatedMessage": "Or <a href=\"{{url}}\" rel=\"noopener noreferrer\" target=\"_blank\">book a meeting URL</a> in advance where you are the only moderator.", "oopsDeviceClockorTimezoneErr": "Oops! Something went wrong. Make sure your device clock/timezone is accurate", "oopsThereSeemsToBeProblem": "Oops there seems to be a Problem", "pleaseWaitForTheStartMeeting": "Please wait for the {{moderator}} to start this meeting.", "preRegistrationMsg": "This meeting requires Pre-registration. Register yourself in the browser and join back from the invited email link.", "pricing": "Pricing", "privacy": "Privacy", "privateMeetingErr": "You seems to be joining a private meeting. Please join by sign in using invited email address.", "proPlan": "Pro Plan", "recentList": "Recent", "recentListDelete": "Delete entry", "recentListEmpty": "Your recent list is currently empty. Chat with your team and you will find all your recent meetings here.", "reducedUIText": "Welcome to {{app}}!", "registerNow": "Register Now", "roomname": "Enter Meeting ID", "roomNameAllowedChars": "Meeting name should not contain any of these characters: ?, &, :, ', \", %, #.", "roomnameHint": "Enter the name or URL of the room you want to join. You may make a name up, just let the people you are meeting know it so that they enter the same name.", "scheduleAMeeting": "Schedule a Meeting", "sendFeedback": "Send feedback", "shiftingVirtualMeetToReality": "Shifting Virtual Meet To Reality", "solutions": "Solutions", "startMeeting": "Start meeting", "terms": "Terms", "timezone": "Timezone", "title": "100% Free Unlimited, End to End Encrypted, and HD Quality Video Conferencing Solution", "tryNowItsFree": "Try Now, It’s Free", "waitingInLobby": "Waiting in <PERSON>bby. {{moderator}} will let you in soon.", "youtubeHelpTutorial": "YouTube Help Tutorials"}}