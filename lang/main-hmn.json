{"addPeople": {"add": "Caw", "addContacts": "Caw koj cov neeg sib cuag", "contacts": "kev sib cuag", "copyInvite": "<PERSON><PERSON> daim ntawv caw lub rooj sib tham", "copyLink": "<PERSON>py kev sib tham txuas", "copyStream": "<PERSON><PERSON> theej duab nyob streaming link", "countryNotSupported": "Peb tseem tsis tau txhawb nqa lub hom phiaj no.", "countryReminder": "Hu sab nraud US? Thov nco ntsoov koj pib nrog lub teb chaws chaws!", "defaultEmail": "<PERSON><PERSON>", "disabled": "<PERSON>j tsis tuaj yeem caw tib neeg.", "doYouWantToRemoveThisPerson": "<PERSON>j puas xav tshem tus neeg no", "failedToAdd": "Ntxiv cov neeg koom tsis tau", "footerText": "Kev hu tawm yog neeg xiam.", "googleCalendar": "Google Meem", "googleEmail": "Google Email", "inviteMoreHeader": "<PERSON>j yog tib tug nyob hauv lub rooj sib tham", "inviteMoreMailSubject": "<PERSON><PERSON> nrog {{appName}} lub rooj sib tham", "inviteMorePrompt": "Caw neeg ntxiv", "linkCopied": "Txuas tau theej rau daim ntawv teev lus", "loading": "<PERSON><PERSON><PERSON> neeg thiab xov tooj", "loadingNumber": "Kev lees paub tus lej xov tooj", "loadingPeople": "<PERSON><PERSON><PERSON> neeg los caw", "loadingText": "Chaw thau khoom ...", "noResults": "Tsis muaj cov txiaj ntsig tshawb nrhiav", "noValidNumbers": "Thov sau tus xov tooj", "outlookEmail": "Outlook email", "phoneNumbers": "xov tooj", "searchNumbers": "Ntxiv cov xov tooj", "searchPeople": "<PERSON><PERSON><PERSON> neeg", "searchPeopleAndNumbers": "Tshawb nrhiav cov neeg lossis ntxiv lawv tus lej xov tooj", "searching": "Nrhiav...", "sendWhatsa[pp": "Whatsapp", "shareInvite": "Q<PERSON> kev sib ntsib caw", "shareInviteP": "Qhia tawm rooj sib tham caw nrog Password", "shareLink": "<PERSON><PERSON> qhov sib tham txuas mus caw lwm tus", "shareStream": "<PERSON>hia tawm qhov txuas nyob streaming", "sipAddresses": "sip chaw nyob", "telephone": "Xov tooj: {{number}}", "title": "Caw cov neeg tuaj koom lub rooj sib tham no", "yahooEmail": "Yahoo email"}, "audioDevices": {"bluetooth": "Bluetooth", "headphones": "<PERSON><PERSON> mloog pob ntseg", "none": "Tsis muaj cov khoom siv suab", "phone": "<PERSON><PERSON> tooj", "speaker": "<PERSON><PERSON> lus"}, "audioOnly": {"audioOnly": "Tsawg bandwidth"}, "breakoutRooms": {"actions": {"add": "Ntxiv chav sib tawm (Beta)", "autoAssign": "Tswj tau mus rau chav sib tawm", "close": "<PERSON><PERSON><PERSON>", "join": "Ntsib", "leaveBreakoutRoom": "<PERSON>so chaw sib tawm", "more": "Ntxiv", "remove": "Rho tawm", "rename": "Rhuav", "renameBreakoutRoom": "<PERSON><PERSON>v chav sib tawm", "sendToBreakoutRoom": "Xa tus neeg koom nrog mus rau:"}, "breakoutList": "Cov list sib tawm", "buttonLabel": "Chav sib tawm", "defaultName": "Chav sib tawm #{{index}}", "hideParticipantList": "<PERSON><PERSON><PERSON> cov npe ntawm cov neeg koom nrog", "mainRoom": "Chav tseem ceeb", "notifications": {"joined": "<PERSON><PERSON> nrog \"{{name}}\" chav sib tawm", "joinedMainRoom": "<PERSON><PERSON> nrog chav tseem ceeb", "joinedTitle": "<PERSON><PERSON>"}, "showParticipantList": "<PERSON><PERSON><PERSON> cov npe ntawm cov neeg koom nrog", "showParticipants": "Qhia Cov <PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON>"}, "calendarSync": {"addMeetingURL": "Ntxiv qhov txuas kev sib tham", "confirmAddLink": "Koj puas xav ntxiv qhov sib ntsib Lub Sij Hawm rau qhov kev tshwm sim no?", "error": {"appConfiguration": "Calendar integration tsis raug teeb tsa kom raug.", "generic": "Ib qho yuam kev tau tshwm sim. Thov tshawb xyuas koj daim calendar settings lossis sim refreshing daim calendar.", "notSignedIn": "Ib qho yuam kev tshwm sim thaum kuaj xyuas kom pom cov xwm txheej hauv daim ntawv teev npe. Thov xyuas koj daim ntawv teev npe thiab sim nkag mus dua."}, "join": "<PERSON><PERSON><PERSON>", "joinTooltip": "<PERSON><PERSON> nrog lub rooj sib tham", "nextMeeting": "lub rooj sib tham tom ntej", "noEvents": "Tsis muaj cov txheej xwm yuav los tom ntej no.", "ongoingMeeting": "kev sib tham tsis tu ncua", "permissionButton": "<PERSON><PERSON><PERSON> chaw", "permissionMessage": "Daim calendar tso cai yuav tsum pom koj cov rooj sib tham hauv app.", "refresh": "<PERSON>fresh daim ntawv qhia hnub", "today": "Hnub no"}, "carmode": {"actions": {"selectSoundDevice": "Xaiv lub suab ntaus ntawv"}, "labels": {"buttonLabel": "<PERSON><PERSON><PERSON> hom", "title": "<PERSON><PERSON><PERSON> hom", "videoStopped": "Koj cov yeeb yaj kiab raug tso tseg"}}, "chat": {"enter": "Nkag mus rau chav sib tham", "error": "yuam kev: koj cov lus tsis tau xa. Yog vim li cas: {{error}}", "fieldPlaceHolder": "Ntaus koj cov lus ntawm no", "message": "Xov xwm", "messageAccessibleTitle": "{{user}} hais tias:", "messageAccessibleTitleMe": "kuv hais tias:", "messageTo": "Cov lus ntiag tug rau {{recipient}}", "messagebox": "Ntaus ntawv", "nickname": {"popover": "<PERSON>aiv lub npe menyuam yaus", "title": "Sau ib lub npe menyuam yaus siv kev sib tham"}, "noMessagesMessage": "Tseem tsis tau muaj lus nyob hauv lub rooj sib tham. <PERSON><PERSON> kev sib tham ntawm no!", "privateNotice": "Cov lus ntiag tug rau {{recipient}}", "smileysPanel": "Emoji vaj huam sib luag", "tabs": {"chat": "<PERSON><PERSON>", "polls": "Kev xaiv tsa"}, "title": "<PERSON><PERSON> thiab Polls", "titleWithPolls": "<PERSON><PERSON> thiab Polls", "you": "koj"}, "chromeExtensionBanner": {"buttonText": "Nruab Chrome Extension", "close": "<PERSON>w", "dontShowAgain": "Tsis txhob qhia kuv qhov no ntxiv", "installExtensionText": "Nruab qhov txuas ntxiv rau Google Calendar thiab Office 365 kev koom ua ke"}, "clickandpledge": {"errorDesc": "Thov sau siv tau Nyem thiab Pledge Txuas GUID. Yog xav paub ntxiv mus saib - https://connect.clickandpledge.com/", "errorNotification": "Invalid <PERSON>yem thiab cog lus GUID", "title": "C&P Txuas Kev Pab Nyiaj pub dawb", "titlenative": "<PERSON><PERSON><PERSON> thiab cog lus"}, "connectingOverlay": {"joiningRoom": "Txuas koj rau koj lub rooj sib tham..."}, "connection": {"ATTACHED": "<PERSON><PERSON><PERSON><PERSON> nrog", "AUTHENTICATING": "<PERSON>v lees paub tseeb", "AUTHFAIL": "<PERSON>v lees paub tsis raug", "CONNECTED": "<PERSON><PERSON><PERSON><PERSON> nrog", "CONNECTING": "Kev sib txuas", "CONNFAIL": "Kev sib txuas ua tsis tiav", "DISCONNECTED": "Txiav tawm", "DISCONNECTING": "Txiav tawm", "ERROR": "yuam kev", "FETCH_SESSION_ID": "Tau txais session-id...", "GET_SESSION_ID_ERROR": "Tau txais session-id yuam kev: {{code}}", "GOT_SESSION_ID": "Tau txais session-id... Ua tiav", "LOW_BANDWIDTH": "<PERSON><PERSON> duab rau {{displayName}} tau raug kaw kom txuag bandwidth"}, "connectionindicator": {"address": "Chaw nyob:", "audio_ssrc": "Suab Ssrc:", "bandwidth": "Kwv yees bandwidth:", "bitrate": "Bitrate:", "bridgeCount": "Server suav:", "codecs": "Codecs (A/V):", "connectedTo": "Txuas nrog:", "e2e_rtt": "E2e RTT:", "framerate": "Ncej tus nqi:", "less": "Qhia tsawg dua", "localaddress": "Chaw nyob hauv zos:", "localaddress_plural": "Chaw nyob hauv zos:", "localport": "Local chaw nres nkoj:", "localport_plural": "Cov chaw nres nkoj hauv zos:", "maxEnabledResolution": "xa max", "more": "Qhia ntxiv", "packetloss": "Pob ntawv poob:", "participant_id": "<PERSON>s neeg koom nrog ID:", "quality": {"good": "Zoo", "inactive": "<PERSON><PERSON> muaj zog", "lost": "<PERSON><PERSON>", "nonoptimal": "Tsis zoo", "poor": "pluag"}, "remoteaddress": "Chaw nyob deb:", "remoteaddress_plural": "<PERSON>w nyob nyob deb:", "remoteport": "Chaw nres nkoj chaw taws teeb:", "remoteport_plural": "Chaw taws teeb chaw nres nkoj:", "resolution": "Kev daws teeb meem:", "savelogs": "Txuag cov cav", "status": "Kev sib txuas:", "transport": "<PERSON>v thauj mus los:", "transport_plural": "<PERSON>v thauj mus los:", "video_ssrc": "<PERSON><PERSON>:"}, "dateUtils": {"earlier": "Ua ntej", "today": "Hnub no", "yesterday": "<PERSON>g hmo"}, "deepLinking": {"appNotInstalled": "Siv peb {{app}} app mobile los koom nrog lub rooj sib tham no hauv koj lub xov tooj.", "continueWithBrowser": "Txuas ntxiv nrog Browser", "description": "Tsis muaj dab tsi tshwm sim? Peb sim tso koj lub rooj sib tham hauv {{app}} desktop app. Sim dua lossis tso nws hauv {{app}} web app.", "descriptionWithoutWeb": "Tsis muaj dab tsi tshwm sim? Peb sim tso koj lub rooj sib tham hauv {{app}} desktop app.", "downloadApp": "Download tau lub app", "ifDoNotHaveApp": "Yog tias koj tseem tsis tau muaj lub app:", "ifHaveApp": "Yog tias koj twb muaj lub app:", "ifYouDontHaveTheAppYet": "Yog tias koj tseem tsis tau muaj lub app", "joinInApp": "<PERSON><PERSON> nrog lub rooj sib tham no siv lub app", "joinMeetingWithDesktopApp": "Koom Nrog Lub Rooj <PERSON> Tham nrog Desktop App", "launchMeetingInDesktopApp": "<PERSON><PERSON> tawm <PERSON><PERSON> R<PERSON><PERSON> Tham hauv Desktop App", "launchWebButton": "Tua tawm hauv web", "title": "<PERSON>a tawm koj lub rooj sib tham hauv {{app}}...", "tryAgainButton": "Sim dua hauv desktop"}, "defaultLink": "E.G. {{url}}", "defaultNickname": "ex. <PERSON>", "deviceError": {"cameraError": "Nkag mus rau koj lub koob yees duab ua tsis tiav", "cameraPermission": "Ua yuam kev tau txais kev tso cai lub koob yees duab", "microphoneError": "Nkag mus rau koj lub <PERSON> ua tsis tiav", "microphonePermission": "yuam kev tau txais kev tso cai microphone"}, "deviceSelection": {"noPermission": "Tso cai tsis tau tso cai", "previewUnavailable": "<PERSON><PERSON> ua ntej tsis muaj", "selectADevice": "<PERSON>aiv ib lub cuab yeej", "testAudio": "Ua si lub suab xeem"}, "dialOut": {"statusMessage": "tam sim no yog {{status}}"}, "dialog": {"Back": "<PERSON>ov qab", "Cancel": "<PERSON>so tseg", "IamHost": "Kuv yog tus tswv tsev", "Ok": "Ua li cas", "Remove": "<PERSON>shem tawm", "Share": "Qhia", "Submit": "Xa", "WaitForHostMsg": "<PERSON><PERSON> rooj sab laj <b>{{room}}</b> tseem tsis tau pib. Yog tias koj yog tus tswv tsev ces thov authenticate. Txwv tsis pub, thov tos tus tswv tsev tuaj txog.", "WaitForHostMsgWOk": "<PERSON><PERSON> rooj sab laj <b>{{room}}</b> tseem tsis tau pib. Yog tias koj yog tus tswv tsev ces thov nias OK kom paub tseeb. Txwv tsis pub, thov tos tus tswv tsev tuaj txog.", "WaitforModerator": "Thov tos tus Moderator tuaj txog", "WaitforModeratorOk": "Rov qab mus", "WaitingForHost": "Tos tus tswv...", "WaitingForHostTitle": "Tos tus tswv...", "Yes": "Yog lawm", "accessibilityLabel": {"liveStreaming": "Dej ntws"}, "add": "Ntxiv", "allow": "Tso cai", "alreadySharedVideoMsg": "Lwm tus neeg koom nrog twb tau tshaj tawm cov yeeb yaj kiab. Lub rooj sib tham no tso cai tsuas yog ib qho video sib koom ib zaug.", "alreadySharedVideoTitle": "<PERSON><PERSON><PERSON> yog ib daim vis dis aus tau tso cai rau ib lub sijhawm", "applicationWindow": "Qhov rai Application", "authenticationRequired": "<PERSON><PERSON> tsum muaj kev lees paub", "cameraConstraintFailedError": "<PERSON><PERSON> lub koob yees duab tsis txaus siab rau qee qhov kev txwv uas yuav tsum tau ua.", "cameraNotFoundError": "<PERSON>b koob yees duab tsis pom.", "cameraNotSendingData": "Peb tsis tuaj yeem nkag mus rau koj lub koob yees duab. Thov xyuas yog tias lwm daim ntawv thov siv lub cuab yeej no, xaiv lwm lub cuab yeej los ntawm cov ntawv qhia zaub mov teeb tsa lossis sim rov rub daim ntawv thov.", "cameraNotSendingDataTitle": "Siv tsis tau lub koob yees duab", "cameraPermissionDeniedError": "Koj tsis tau tso cai siv koj lub koob yees duab. Koj tseem tuaj yeem koom nrog lub rooj sab laj tab sis lwm tus yuav tsis pom koj. Siv lub koob yees duab khawm hauv qhov chaw nyob bar los kho qhov no.", "cameraTimeoutError": "Tsis tuaj yeem pib qhov video. Sijhawm tshwm sim!", "cameraUnknownError": "Siv tsis tau lub koob yees duab vim tsis paub yog vim li cas.", "cameraUnsupportedResolutionError": "<PERSON><PERSON> lub koob yees duab tsis txhawb nqa video daws teeb meem.", "cannotToggleScreenSharingNotSupported": "Tsis tuaj yeem toggle screen sib koom: tsis txaus siab.", "close": "<PERSON>w", "closingAllTerminals": "Kaw tag nrho cov Terminals", "conferenceDisconnectMsg": "<PERSON><PERSON> zaum koj yuav xav tshawb xyuas koj qhov kev sib txuas network. Rov txuas dua hauv {{seconds}} sec...", "conferenceDisconnectTitle": "<PERSON>j raug rho tawm lawm.", "conferenceReloadMsg": "Peb tab tom sim kho qhov no. Rov txuas dua hauv {{seconds}} sec...", "conferenceReloadTitle": "<PERSON><PERSON><PERSON> tsis zoo, ib yam dab tsi mus tsis ncaj ncees lawm.", "confirm": "<PERSON><PERSON>", "confirmNo": "<PERSON><PERSON> muaj", "confirmYes": "Yog lawm", "connectError": "Oops! Muaj ib yam dab tsi tsis ncaj ncees lawm thiab peb txuas tsis tau rau lub rooj sab laj.", "connectErrorWithMsg": "Oops! Muaj ib yam dab tsi tsis ncaj ncees lawm thiab peb txuas tsis tau rau lub rooj sab laj: {{msg}}", "connecting": "Kev sib txuas", "contactSupport": "Hu rau kev txhawb nqa", "copied": "<PERSON><PERSON>", "copy": "<PERSON><PERSON>", "customAwsRecording": "Kev cai AWS cov ntaubntawv", "deleteCache": "Rho tawm cache", "dismiss": "<PERSON>so tseg", "displayNameRequired": "Nyob zoo! Koj lub npe yog dab tsi?", "displayUserName": "", "donationCNPLabel": "Nkag mus Txuas Daim Ntawv URL lossis Widget URL", "donationCNotificationTitle": "Pub dawb ntawm Click and Pledge", "donationLabel": "Nkag mus rau Donation Campaign url", "donationNotificationDescription": "Pub dawb los txhawb peb qhov laj thawj", "donationNotificationTitle": "Pub dawb ntawm Donorbox", "done": "Ua tiav", "e2eeDescription": "End-to-End Encryption yog tam sim no EXPERIMENTAL. Thov nco ntsoov tias kev xa mus rau qhov kawg-rau-kawg encryption yuav ua rau muaj txiaj ntsig zoo cuam tshuam rau server-sab cov kev pabcuam xws li: kaw, nyob streaming thiab kev koom tes hauv xov tooj. Kuj tseem nco ntsoov tias lub rooj sib tham tsuas yog ua haujlwm rau cov neeg tuaj koom los ntawm browsers nrog kev txhawb nqa rau cov kwj nkag.", "e2eeLabel": "Pab kom End-to-End Encryption", "e2eeWarning": "CEEB TOOM: Tsis yog txhua tus neeg koom hauv lub rooj sib tham no zoo li muaj kev txhawb nqa rau End-to-End encryption. Yog koj pab tau lawv yuav tsis pom thiab hnov ​​koj.", "embedMeeting": "Embed lub rooj sib tham", "enterCdonation": "Piv txwv: https://connect.clickandpledge.com/w/Form/283ah-0069-441a-ba0f-d42a9dec9821", "enterDisplayEmail": "Email ID", "enterDisplayName": "<PERSON><PERSON>", "enterDisplayNameToJoin": "Thov sau koj lub npe los koom", "enterDonation": "Piv txwv: https://donorbox.org/donate-an-organisation", "enterMeetingId": "Nkag mus rau <PERSON><PERSON><PERSON>", "enterMeetingPassword": "Nkag mus rau <PERSON><PERSON><PERSON><PERSON> Password", "error": "yuam kev", "errorMeetingID": "Ntxiv Kev Sib Tham ID", "errorMeetingPassword": "Ntxiv Kev Sib Tham Password", "forceMuteEveryoneDialog": "<PERSON>j puas paub tseeb tias koj xav kaw lub microphone ntawm txhua tus tsuas yog cov neeg saib xyuas? Lawv yuav tsis tuaj yeem qhib lub microphone rau lawv tus kheej, tab sis thaum koj thim rov qab quab yuam lub suab, lawv tuaj yeem qhib thiab tham.", "forceMuteEveryoneElseDialog": "Mute lawv thiab lov tes taw lawv lub microphone", "forceMuteEveryoneElseTitle": "Force Mute txhua tus tsuas yog {{whom}}?", "forceMuteEveryoneElsesVideoDialog": "<PERSON>ha<PERSON> lub koob yees duab yog neeg xiam, Lawv yuav tsis muaj peev xwm ua kom lawv lub koob yees duab", "forceMuteEveryoneElsesVideoTitle": "Force Mute txhua tus lub koob yees duab tshwj tsis yog {{whom}}?", "forceMuteEveryoneSelf": "koj tus kheej", "forceMuteEveryoneStartMuted": "Txhua tus pib quab yuam muted txij tam sim no mus", "forceMuteEveryoneTitle": "Force Mute txhua tus?", "forceMuteEveryonesVideoDialog": "Koj puas paub tseeb tias koj xav kaw cov yeeb yaj kiab ntawm tus neeg koom nrog no? Nws yuav tsis muaj peev xwm qhib video", "forceMuteEveryonesVideoTitle": "Force Mute txhua tus Video?", "forceMuteParticipantBody": "Force Mute tus neeg koom tes.", "forceMuteParticipantButton": "Ntsalty mute", "forceMuteParticipantDialog": "<PERSON>j puas paub tseeb tias koj xav kaw lub microphone ntawm tus neeg koom nrog no? Nws yuav tsis tuaj yeem qhib lub microphone, tab sis thaum koj thim rov qab yuam kev, nws tuaj yeem qhib thiab tham.", "forceMuteParticipantTitle": "Force Mute tus neeg koom nrog no?", "forceMuteParticipantsVideoBody": "Cov neeg koom ua yeeb yaj kiab yuav raug kaw thiab lawv yuav tsis tuaj yeem tig rov qab dua", "forceMuteParticipantsVideoButton": "Disable lub koob yees duab", "forceMuteParticipantsVideoTitle": "Disable koob yees duab ntawm tus neeg koom no?", "gracefulShutdown": "Peb qhov kev pabcuam tam sim no poob qis rau kev kho. Thov rov sim dua tom qab.", "grantModeratorDialog": "Koj puas paub tseeb tias koj xav ua kom tus neeg koom nrog no ua tus tswj xyuas?", "grantModeratorTitle": "<PERSON>v <PERSON><PERSON><PERSON>", "guestUser": "Qhua Neeg Siv", "hangUpLeaveReason": "<PERSON><PERSON> R<PERSON>j <PERSON> Tham no tau xaus los ntawm Tus Saib Xyuas", "hideShareAudioHelper": "Tsis txhob qhia qhov dialog no dua", "incorrectPassword": "Tsis yog username lossis password", "incorrectRoomLockPassword": "<PERSON><PERSON> password tsis raug", "internalError": "Oops! Ib yam dab tsi mus tsis ncaj ncees lawm. Qhov yuam kev hauv qab no tshwm sim: {{error}}", "internalErrorTitle": "Internal yuam kev", "kickMessage": "Aws! Koj raug tshem tawm ntawm kev sib ntsib!", "kickParticipantButton": "<PERSON><PERSON>m cov neeg siv", "kickParticipantDialog": "Koj puas paub tseeb tias koj xav tshem tus neeg koom nrog no?", "kickParticipantTitle": "Tshem tawm tus neeg koom nrog no?", "kickTitle": "Aws! Koj raug tshem tawm ntawm lub rooj sib tham", "liveStreaming": "Nyob streaming", "liveStreamingDisabledBecauseOfActiveRecordingTooltip": "Tsis muaj peev xwm ua tau thaum cov ntaubntawv povthawj siv", "liveStreamingDisabledForGuestTooltip": "Cov qhua tsis tuaj yeem pib streaming nyob.", "liveStreamingDisabledTooltip": "Pib streaming nyob tsis siv neeg.", "localUserControls": "Cov neeg siv hauv zos tswj hwm", "lockMessage": "<PERSON>auv lub rooj sib tham ua tsis tiav.", "lockRoom": "Ntxiv lub rooj sib tham $t(lockRoomPasswordUppercase)", "lockTitle": "Xauv ua tsis tiav", "login": "Nkag mus", "logoutQuestion": "Koj puas paub tseeb tias koj xav tawm thiab tso tseg lub rooj sab laj?", "logoutTitle": "Thwj tas", "maxUsersLimitReached": "Qhov txwv rau cov neeg koom siab tshaj plaws tau mus txog. <PERSON>b rooj sab laj puv. Thov hu rau tus tswv lub rooj sib tham lossis sim dua tom qab!", "maxUsersLimitReachedTitle": "Cov neeg koom siab tshaj plaws tau mus txog", "meetHourRecording": "<PERSON><PERSON><PERSON>", "meetingID": "<PERSON><PERSON>b <PERSON>", "meetingIDandPassword": "Nkag mus rau <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>ham <PERSON> thiab Password", "meetingPassword": "Sib ntsib Password", "messageErrorApi": "oops ua! Tej yam ua yuam kev", "messageErrorInvalid": "Cov ntawv pov thawj tsis raug", "messageErrorNotModerator": "Oops! koj tsis yog tus neeg saib xyuas ntawm lub rooj sib tham no", "messageErrorNull": "<PERSON>s neeg siv lub npe lossis tus password yog qhov khoob", "micConstraintFailedError": "<PERSON><PERSON> lub <PERSON> tsis txaus siab rau qee qhov kev txwv uas yuav tsum tau ua.", "micNotFoundError": "Microphone tsis pom.", "micNotSendingData": "<PERSON>s rau koj lub computer cov chaw kom unmute koj lub mic thiab kho nws qib", "micNotSendingDataTitle": "<PERSON>j lub mic tau kaw los ntawm koj qhov kev teeb tsa", "micPermissionDeniedError": "Koj tsis tau tso cai siv koj lub microphone. Koj tseem tuaj yeem koom nrog lub rooj sab laj tab sis lwm tus yuav tsis hnov ​​koj. Siv lub koob yees duab khawm hauv qhov chaw nyob bar los kho qhov no.", "micTimeoutError": "Tsis tuaj yeem pib lub suab. Sijhawm tshwm sim!", "micUnknownError": "Tsis tuaj yeem siv lub microphone rau qhov tsis paub yog vim li cas.", "muteEveryoneDialog": "Koj puas paub tseeb tias koj xav kaw txhua tus? Koj yuav tsis muaj peev xwm unmute lawv, tab sis lawv tuaj yeem unmute lawv tus kheej txhua lub sijhawm.", "muteEveryoneElseDialog": "<PERSON><PERSON><PERSON> muted, koj yuav tsis tuaj yeem unmute lawv, tab sis lawv tuaj yeem unmute lawv tus kheej txhua lub sijhawm.", "muteEveryoneElseTitle": "<PERSON>aw sawv daws tsuas yog {{whom}}?", "muteEveryoneElsesVideoDialog": "<PERSON>haum lub koob yees duab tsis ua haujlwm, koj yuav tsis tuaj yeem tig rov qab, tab sis lawv tuaj yeem tig rov qab rau txhua lub sijhawm.", "muteEveryoneElsesVideoTitle": "Tshem tawm txhua lub koob yees duab tshwj tsis yog {{whom}}?", "muteEveryoneSelf": "koj tus kheej", "muteEveryoneStartMuted": "Txhua tus pib muted txij tam sim no mus", "muteEveryoneTitle": "Caw sawv daws?", "muteEveryonesVideoDialog": "Koj puas paub tseeb tias koj xav lov tes taw txhua tus lub koob yees duab? Koj yuav tsis tuaj yeem tig rov qab, tab sis lawv tuaj yeem tig rov qab rau txhua lub sijhawm.", "muteEveryonesVideoDialogOk": "<PERSON>", "muteEveryonesVideoTitle": "Disable sawv daws lub koob yees duab?", "muteParticipantBody": "Koj yuav tsis muaj peev xwm unmute lawv, tab sis lawv tuaj yeem unmute lawv tus kheej txhua lub sijhawm.", "muteParticipantButton": "Ntshai", "muteParticipantDialog": "Koj puas paub tseeb tias koj xav kaw tus neeg koom nrog no? Koj yuav tsis muaj peev xwm unmute lawv, tab sis lawv tuaj yeem unmute lawv tus kheej txhua lub sijhawm.", "muteParticipantTitle": "Mute tus neeg koom nrog no?", "muteParticipantsVideoBody": "Koj yuav tsis tuaj yeem tig lub koob yees duab rov qab, tab sis lawv tuaj yeem tig rov qab rau txhua lub sijhawm.", "muteParticipantsVideoButton": "Disable lub koob yees duab", "muteParticipantsVideoDialog": "Koj puas paub tseeb tias koj xav tua tus neeg koom nrog lub koob yees duab no? Koj yuav tsis tuaj yeem tig lub koob yees duab rov qab, tab sis lawv tuaj yeem tig rov qab rau txhua lub sijhawm.", "muteParticipantsVideoTitle": "Disable koob yees duab ntawm tus neeg koom no?", "noDropboxToken": "Tsis siv tau Dropbox token", "noScreensharingInAudioOnlyMode": "Tsis muaj kev sib tham hauv lub suab nkaus xwb", "password": "Tus password", "passwordLabel": "<PERSON>b rooj sib tham tau kaw los ntawm Tus Neeg Saib Xyuas. Thov nkag mus rau $t(lockRoomPassword) los koom.", "passwordNotSupported": "Kev teeb tsa lub rooj sib tham $t(lockRoomPassword) tsis txaus siab.", "passwordNotSupportedTitle": "$t(lockRoomPasswordUppercase) tsis txaus siab", "passwordRequired": "$t(lockRoomPasswordUppercase) xav tau", "permissionCameraRequiredError": "<PERSON><PERSON> koob yees duab tso cai yuav tsum tau koom nrog hauv cov rooj sib tham nrog video. Thov tso cai rau hauv Chaw", "permissionErrorTitle": "<PERSON><PERSON> tsum tso cai", "permissionMicRequiredError": "Kev tso cai microphone yuav tsum tau koom nrog hauv cov rooj sib tham nrog suab. Thov tso cai rau hauv Chaw", "popupError": "<PERSON><PERSON> tus browser tab tom thaiv qhov rais pop-up los ntawm qhov chaw no. Thov qhib cov pop-ups hauv koj qhov browser qhov chaw ruaj ntseg thiab sim dua.", "popupErrorTitle": "Pop-up thaiv", "readMore": "ntau", "recording": "<PERSON>v kaw cia", "recordingDisabledBecauseOfActiveLiveStreamingTooltip": "Tsis muaj peev xwm ua tau thaum muaj kwj dej nyob", "recordingDisabledForGuestTooltip": "Cov qhua tuaj yeem pib kaw tsis tau.", "recordingDisabledTooltip": "<PERSON><PERSON> kaw kaw.", "rejoinNow": "<PERSON>ov koom nrog tam sim no", "remoteControlAllowedMessage": "{{user}} lees txais koj qhov kev thov tswj chaw taws teeb!", "remoteControlDeniedMessage": "{{user}} tsis lees paub koj qhov kev thov tswj chaw taws teeb!", "remoteControlErrorMessage": "Ib qho yuam kev tshwm sim thaum sim thov kev tso cai tswj chaw taws teeb los ntawm {{user}}!", "remoteControlRequestMessage": "Koj puas yuav tso cai rau {{user}} los tswj koj lub desktop?", "remoteControlShareScreenWarning": "Nco ntsoov tias yog koj nias \"Cia\" koj yuav qhia koj lub vijtsam!", "remoteControlStopMessage": "Kev tswj chaw taws teeb tau xaus!", "remoteControlTitle": "<PERSON>j thaj chaw deb desktop tswj", "remoteUserControls": "Cov neeg siv tej thaj chaw deb tswj ntawm {{username}}", "removeCDonation": "<PERSON><PERSON><PERSON> thiab cog lus qhia tshem tawm", "removeCDonationD": "Qhov txuas pub dawb tau raug tshem tawm tiav", "removeDonation": "Donorbox donation link tshem tawm", "removeDonationD": "Qhov txuas pub dawb tau raug tshem tawm tiav", "removePassword": "Tshem tawm $t(lockRoomPassword)", "removeSharedVideoMsg": "Koj puas paub tseeb tias koj xav tshem tawm koj cov yeeb yaj kiab uas tau qhia tawm?", "removeSharedVideoTitle": "<PERSON>shem tawm cov yees duab sib koom", "reservationError": "Reservation system yuam kev", "reservationErrorMsg": "yuam kev code: {{code}}, lus: {{msg}}", "restartInitiatedBecauseOfBridgeFailure": "Restart pib vim yog tus choj tsis ua haujlwm", "retry": "Rov sim dua", "revokeModeration": "Tshem tawm tus neeg siv los ua Tus Saib Xyuas?", "revokeModerationTitle": "Txhawb Nqa Cov Qauv", "screenSharingAudio": "Qhia tawm suab", "screenSharingFailed": "Oops! <PERSON>aj ib yam dab tsi tsis ncaj ncees lawm, peb tsis tuaj yeem pib sib qhia qhov screen!", "screenSharingFailedTitle": "Kev sib koom npo ua tsis tiav!", "screenSharingPermissionDeniedError": "Oops! Muaj ib yam dab tsi tsis ncaj ncees lawm nrog koj qhov kev tso cai sib koom ua ke. Thov reload thiab sim dua.", "screenSharingUser": "{{displayName}} yog tam sim no sib qhia lub vijtsam", "sendPrivateMessage": "Tsis ntev los no koj tau txais xov xwm ntiag tug. Koj puas tau npaj siab yuav teb rau qhov ntawd ntiag tug, lossis koj xav xa koj cov lus rau pawg?", "sendPrivateMessageCancel": "Xa mus rau pawg", "sendPrivateMessageOk": "<PERSON>a tus kheej", "sendPrivateMessageTitle": "Xa tus kheej?", "serviceUnavailable": "<PERSON>v pabcuam tsis muaj", "sessTerminated": "Kev hu raug kaw lawm", "sessionRestarted": "Hu rov pib dua los ntawm tus choj", "shareAudio": "Txuas ntxiv", "shareAudioTitle": "Qhia tawm suab li cas", "shareAudioWarningD1": "koj yuav tsum tso tseg kev tshuaj ntsuam sib qhia ua ntej sib qhia koj lub suab.", "shareAudioWarningD2": "koj yuav tsum rov pib dua koj qhov kev tshuaj ntsuam sib qhia thiab kos cov \"sib qhia suab\" kev xaiv.", "shareAudioWarningH1": "Yog koj xav share xwb audio:", "shareAudioWarningTitle": "<PERSON><PERSON> yuav tsum tau tso tseg kev tshuaj ntsuam sib qhia ua ntej sib qhia suab", "shareMediaWarningGenericH2": "Yog tias koj xav qhia koj lub vijtsam thiab lub suab", "shareScreenWarningD1": "koj yuav tsum nres lub suab sib koom ua ntej sib koom koj lub vijtsam.", "shareScreenWarningD2": "koj yuav tsum tau tso tseg lub suab sib koom, pib kev tshuaj ntsuam sib koom thiab kos lub \"sib qhia suab\" kev xaiv.", "shareScreenWarningH1": "Yog tias koj xav qhia koj qhov screen xwb:", "shareScreenWarningTitle": "<PERSON><PERSON> yuav tsum tso tseg lub suab sib koom ua ntej sib qhia koj lub vijtsam", "shareVideoLinkError": "Thov muab qhov link youtube kom raug.", "shareVideoTitle": "Share YouTube", "shareYourScreen": "<PERSON><PERSON> koj qhov screen", "shareYourScreenDisabled": "Kev sib koom ntawm lub vijtsam raug kaw.", "shareYourScreenDisabledForGuest": "Cov qhua tsis tuaj yeem tshuaj ntsuam qhia.", "startLiveStreaming": "Live stream + kaw", "startRecording": "<PERSON><PERSON>", "startRemoteControlErrorMessage": "Ib qho yuam kev tau tshwm sim thaum sim pib qhov chaw taws teeb tswj kev sib ntsib!", "stopLiveStreaming": "Nres Streaming", "stopRecording": "<PERSON>so tseg", "stopRecordingWarning": "Koj puas paub tseeb tias koj xav tso tseg qhov kev kaw cia?", "stopStreamingWarning": "Koj puas paub tseeb tias koj xav tso tseg cov streaming nyob?", "streamKey": "Nyob Kwj Ke Key", "switchInProgress": "Hloov ua haujlwm.", "thankYou": "Ua tsaug rau koj siv {{appName}}!", "token": "token", "tokenAuthFailed": "<PERSON>hov txim, koj tsis raug tso cai koom nrog qhov kev hu no.", "tokenAuthFailedTitle": "<PERSON>v lees paub tsis raug", "transcribing": "Kev sau ntawv", "unforceMuteEveryoneDialog": "<PERSON>j puas paub tseeb tias koj xav qhib lub microphone ntawm txhua tus? lawv tuaj yeem tsis mloog & tham.", "unforceMuteEveryoneElseDialog": "Undo Mute lawv thiab cia lawv qhib lawv lub microphone", "unforceMuteEveryoneElseTitle": "Undo Force Mute txhua tus tsuas yog {{whom}}?", "unforceMuteEveryoneElsesVideoDialog": "<PERSON><PERSON><PERSON> lub koob yees duab qhib, Lawv yuav muaj peev xwm ua kom lawv lub koob yees duab", "unforceMuteEveryoneElsesVideoTitle": "Pab kom txhua tus lub koob yees duab tsuas yog {{whom}}?", "unforceMuteEveryoneSelf": "koj tus kheej", "unforceMuteEveryoneTitle": "Undo Force Mute txhua tus lub microphone?", "unforceMuteEveryonesVideoDialog": "Koj puas paub tseeb tias koj xav qhib video ntawm txhua tus?", "unforceMuteEveryonesVideoTitle": "Pab kom sawv daws lub koob yees duab?", "unforceMuteParticipantBody": "Undo Mute tus neeg koom tes.", "unforceMuteParticipantButton": "Undo force mute", "unforceMuteParticipantDialog": "<PERSON>j puas paub tseeb tias koj xav qhib video ntawm tus neeg koom nrog no?.", "unforceMuteParticipantTitle": "Undo Force Mute tus neeg koom nrog no?", "unforceMuteParticipantsVideoBody": "Cov neeg koom ua yeeb yaj kiab yuav raug qhib thiab lawv yuav tuaj yeem tig rov qab dua", "unforceMuteParticipantsVideoButton": "<PERSON><PERSON><PERSON> lub koob yees duab", "unforceMuteParticipantsVideoTitle": "Xauv video ntawm tus neeg koom nrog no?", "unlockRoom": "Tshem tawm lub rooj sib tham $t(lockRoomPassword)", "user": "Cov neeg siv", "userIdentifier": "<PERSON>s neeg siv ID", "userPassword": "<PERSON>s neeg siv tus password", "videoLink": "Kev Txuas Txuas Ntawv", "viewUpgradeOptions": "Saib cov kev xaiv hloov tshiab", "viewUpgradeOptionsContent": "Txhawm rau kom tau txais kev txwv tsis pub nkag mus rau cov yam ntxwv zoo xws li kaw, sau ntawv, RTMP Streaming & ntau dua, koj yuav tsum tau hloov kho koj txoj kev npaj.", "viewUpgradeOptionsTitle": "Koj nrhiav tau tus hwm tshwj xeeb!", "yourEntireScreen": "<PERSON><PERSON> qhov screen tag nrho"}, "documentSharing": {"title": "<PERSON><PERSON> nyob"}, "donorbox": {"errorDesc": "Thov sau qhov siv tau Donorbox phiaj xwm URL. Yog xav paub ntxiv mus saib - www.donorbox.org", "errorNotification": "Donorbox Tsis Raug URL", "title": "Ntxiv DonorBox Campaign URL", "titlenative": "<PERSON><PERSON> kem"}, "e2ee": {"labelToolTip": "<PERSON><PERSON> thiab Video Kev sib txuas lus ntawm qhov kev hu no yog qhov kawg-rau-kawg encrypted"}, "embedMeeting": {"title": "Embed lub rooj sib tham no"}, "feedback": {"average": "<PERSON><PERSON>b nrab", "bad": "Phem", "detailsLabel": "Qhia rau peb paub ntxiv txog nws.", "good": "Zoo", "rateExperience": "<PERSON><PERSON><PERSON> koj qhov kev sib ntsib", "star": "<PERSON><PERSON> hnub qub", "veryBad": "<PERSON><PERSON> heev", "veryGood": "Zoo heev"}, "giphy": {"giphy": "<PERSON><PERSON><PERSON>", "noResults": "Tsis muaj qhov tshwm sim pom :(", "search": "Nrhiav GIPHY"}, "helpView": {"header": "<PERSON><PERSON>"}, "incomingCall": {"answer": "<PERSON><PERSON>", "audioCallTitle": "<PERSON>j", "decline": "<PERSON>so tseg", "productLabel": "los ntawm Raws li teev", "videoCallTitle": "Incoming video hu"}, "info": {"accessibilityLabel": "Qhia cov ntaub ntawv", "addPassword": "Ntxiv $t(lockRoomPassword)", "cancelPassword": "Ncua tseg $t(lockRoomPassword)", "conferenceURL": "Txuas:", "copyNumber": "<PERSON><PERSON> tus lej", "country": "<PERSON>b teb chaws", "dialANumber": "<PERSON><PERSON><PERSON><PERSON> rau koom nrog koj lub rooj sib tham, ntaus ib qho ntawm cov lej no thiab tom qab ntawd nkag mus rau tus pin.", "dialInConferenceID": "Tus PIN:", "dialInNotSupported": "Thov txim, hu rau hauv tam sim no tsis txaus siab.", "dialInNumber": "Dial-hauv:", "dialInSummaryError": "yuam kev mus nqa cov ntaub ntawv dial-in tam sim no. Thov rov sim dua tom qab.", "dialInTollFree": "<PERSON><PERSON>", "genericError": "<PERSON><PERSON>, muaj ib yam dab tsi tsis ncaj ncees lawm.", "inviteLiveStream": "Tx<PERSON>m rau saib cov kwj nyob ntawm lub rooj sib tham no, nyem qhov txuas no: {{url}}", "invitePhone": "<PERSON><PERSON><PERSON><PERSON> rau koom nrog hauv xov tooj, coj mus rhaub qhov no: {{number}},,{{conferenceID}}#", "invitePhoneAlternatives": "<PERSON>hiav rau tus lej xov tooj sib txawv?\nSaib lub rooj sib tham hu xov tooj: {{url}}\n\n\nYog tias tseem hu xovtooj-hauv chav xov tooj, koom nrog yam tsis txuas rau lub suab: {{silentUrl}}", "inviteSipEndpoint": "T<PERSON><PERSON>m rau koom nrog SIP chaw nyob, sau qhov no: {{sipUri}}", "inviteTextiOSInviteUrl": "<PERSON><PERSON><PERSON> qhov txuas hauv qab no mus koom: {{inviteUrl}}.", "inviteTextiOSJoinSilent": "Yog tias koj hu xovtooj-hauv chav xov tooj, siv qhov txuas no los koom nrog yam tsis tau txuas rau lub suab: {{silentUrl}}.", "inviteTextiOSPersonal": "{{name}} tab tom caw koj tuaj koom lub rooj sib tham.", "inviteTextiOSPhone": "Txhawm rau koom nrog hauv xov tooj, siv tus lej no: {{number}},,{{conferenceID}}#. Yog tias koj tab tom nrhiav tus lej sib txawv, qhov no yog daim ntawv teev npe tag nrho: {{didUrl}}.", "inviteURLFirstPartGeneral": "<PERSON><PERSON> raug caw tuaj koom lub rooj sib tham.", "inviteURLFirstPartPersonal": "{{name}} tab tom caw koj tuaj koom lub rooj sib tham.", "inviteURLSecondPart": "<PERSON><PERSON> nrog lub rooj sib tham:\n{{url}}", "label": "Dial-in Info", "liveStreamURL": "Kwj nyob:", "moreNumbers": "<PERSON><PERSON>u tus lej", "noNumbers": "Tsis muaj tus lej xov tooj.", "noPassword": "<PERSON><PERSON> muaj", "noRoom": "Tsis muaj chav tau teev tseg rau kev hu-hauv.", "numbers": "Dial-in NUMBERS", "password": "$ t (LockrooDasswordcaseuppercase):", "sip": "SIP chaw nyob", "title": "Qhia", "tooltip": "<PERSON>hia tawm qhov txuas thiab hu xov tooj rau lub rooj sib tham no"}, "inlineDialogFailure": {"msg": "Peb dawm me ntsis.", "retry": "Sim dua", "support": "<PERSON><PERSON><PERSON>b nqa", "supportMsg": "Yog tias qhov no tseem tshwm sim, hu rau"}, "inviteDialog": {"alertText": "Tsis tau caw ib co neeg koom.", "header": "Caw", "searchCallOnlyPlaceholder": "Sau tus xov tooj", "searchPeopleOnlyPlaceholder": "Tshawb nrhiav cov neeg koom", "searchPlaceholder": "<PERSON><PERSON> neeg koom lossis tus xov tooj", "send": "Xa"}, "jitsiHome": "{{logo}} Logo, txuas rau Homepage", "keyboardShortcuts": {"focusLocal": "Tsom ntsoov rau koj cov yeeb yaj kiab", "focusRemote": "Tsom ntsoov rau lwm tus neeg cov yeeb yaj kiab", "fullScreen": "<PERSON>b lossis tawm puv npo", "keyboardShortcuts": "Keyboard shortcuts", "localRecording": "Qhia lossis zais cov kev tswj hwm hauv zos", "mute": "Mute lossis unmute koj lub microphone", "pushToTalk": "Nias mus tham", "raiseHand": "Nce lossis txo koj txhais tes", "showSpeakerStats": "Qhia tus neeg hais lus stats", "toggleChat": "<PERSON><PERSON><PERSON> <PERSON>is kaw qhov kev sib tham", "toggleFilmstrip": "Qhia lossis zais video thumbnails", "toggleParticipantsPane": "Qhia lossis zais cov neeg koom pane", "toggleScreensharing": "<PERSON><PERSON><PERSON> ntawm lub koob yees duab thiab kev tshuaj ntsuam sib koom", "toggleShortcuts": "Qhia lossis zais cov keyboard shortcuts", "videoMute": "<PERSON>b lossis nres koj lub koob yees duab", "videoQuality": "Tswj hu tau zoo"}, "liveChatView": {"header": "24x7 Kev them nyiaj yug nyob"}, "liveStreaming": {"addStream": "Ntxiv qhov chaw", "busy": "Peb tab tom ua haujlwm ntawm kev tso cov peev txheej streaming. Thov rov sim dua hauv ob peb feeb.", "busyTitle": "Tag nrho cov streamers tam sim no tsis khoom", "changeSignIn": "<PERSON><PERSON>ov cov nyiaj.", "choose": "<PERSON><PERSON><PERSON> qhov kwj nyob", "chooseCTA": "Xaiv qhov kev xaiv streaming. Tam sim no koj tau nkag rau hauv ua {{email}}.", "enterLinkedInUrlWithTheKey": "Nkag mus rau LinkedIn url nrog tus yuam sij", "enterStreamKey": "Nkag mus rau koj tus lej hauv YouTube nyob ntawm no.", "enterStreamKeyFacebook": "Nkag mus rau koj tus Facebook live stream key ntawm no.", "enterStreamKeyInstagram": "Nkag mus rau koj tus lej Instagram nyob ntawm no.", "enterStreamKeyYouTube": "Nkag mus rau koj tus lej {{youtube}} nyob ntawm no.", "error": "Live Streaming ua tsis tau. Thov rov sim dua.", "errorAPI": "<PERSON>b qho yuam kev tshwm sim thaum nkag mus rau koj qhov kev tshaj tawm hauv YouTube. Thov sim nkag mus ntxiv.", "errorLiveStreamNotEnabled": "Live Streaming tsis tau qhib rau ntawm {{email}}. Thov pab kom nyob streaming los yog nkag mus rau hauv ib tug account nrog nyob streaming enabled.", "expandedOff": "Live streaming tau tso tseg", "expandedOn": "<PERSON><PERSON> rooj sib tham tam sim no tau tshaj tawm rau YouTube.", "expandedPending": "Live streaming tab tom pib ...", "failToStartAutoLiveStreaming": "Tsis Pib Pib Live Streaming", "failToStartAutoRecording": "Ua tsis tiav pib Auto Recording", "failedToStart": "Live Streaming ua tsis tau tejyam pib", "getStreamKeyManually": "Peb tsis tuaj yeem nqa cov kwj nyob. Sim tau txais koj tus yuam sij streaming nyob ntawm YouTube.", "googlePrivacyPolicy": "Google Ntiag Tug Txoj Cai", "invalidStreamKey": "Live stream key tej zaum yuav tsis raug.", "limitNotificationDescriptionNative": "<PERSON><PERSON> qhov streaming yuav raug txwv rau {{limit}} min. <PERSON><PERSON> kev streaming tsis txwv sim {{app}}.", "limitNotificationDescriptionWeb": "<PERSON><PERSON> qhov kev thov siab koj streaming yuav raug txwv rau {{limit}} min. <PERSON>u kev streaming tsis txwv sim <a href={{url}} rel='noopener noreferrer' target='_blank'>{{app}}</a>.", "makeSureYouHaveEnoughStorageAvailableOnYourAccount": "<PERSON><PERSON><PERSON> kom tseeb tias koj muaj txaus cia muaj nyob rau hauv koj tus account.", "note": "Nco tseg", "off": "Live Streaming nres", "offBy": "{{name}} nres qhov kev tshaj tawm nyob", "on": "Live Streaming pib", "onBy": "{{name}} tau pib streaming nyob", "pending": "Pib Live Stream...", "pleaseContactSupportForAssistance": "Thov hu rau kev pab txhawb nqa.", "serviceName": "Live Streaming Service", "signIn": "Nkag mus nrog Google", "signInCTA": "Nkag mus lossis nkag rau koj tus yuam sij streaming nyob ntawm YouTube.", "signOut": "Sau npe tawm", "signedInAs": "Tam sim no koj tau kos npe rau hauv:", "start": "Cov ntaubntawv povthawj siv + Live Stream", "startService": "<PERSON><PERSON>", "streamIdHelp": "Qhov no yog dab tsi?", "unavailableTitle": "Live Streaming tsis muaj", "youtubeTerms": "YouTube cov kev pab cuam"}, "lobby": {"admit": "Txais", "admitAll": "lees tag nrho", "allow": "Tso cai", "backToKnockModeButton": "Tsis muaj tus password, thov kom koom nrog", "dialogTitle": "Lobby hom", "disableDialogContent": "Lobby hom yog tam sim no qhib. Qhov no ua kom ntseeg tau tias cov neeg tsis xav tau tuaj yeem koom tsis tau koj lub rooj sib tham. Koj puas xav lov tes taw nws?", "disableDialogSubmit": "<PERSON>", "emailField": "Sau koj email chaw nyob", "enableDialogPasswordField": "<PERSON><PERSON> tus password (yeem)", "enableDialogSubmit": "<PERSON><PERSON>", "enableDialogText": "Lobby hom cia koj tiv thaiv koj lub rooj sib tham los ntawm tsuas yog tso cai rau tib neeg nkag tom qab kev pom zoo los ntawm tus neeg saib xyuas.", "enterPasswordButton": "Sau tus password lub rooj sib tham", "enterPasswordTitle": "Sau tus password los koom lub rooj sib tham", "invalidPassword": "<PERSON><PERSON> password tsis raug", "joinRejectedMessage": "<PERSON><PERSON> qhov kev thov tuaj koom raug tsis lees paub los ntawm tus neeg saib xyuas.", "joinTitle": "<PERSON><PERSON> lub rooj sib tham", "joinWithPasswordMessage": "<PERSON>m koom nrog tus password, thov tos ...", "joiningMessage": "<PERSON>j yuav tuaj koom lub rooj sib tham sai li sai tau thaum ib tug neeg lees txais koj qhov kev thov", "joiningTitle": "Thov tuaj koom rooj sib tham...", "joiningWithPasswordTitle": "<PERSON><PERSON> nrog tus password ...", "knockButton": "<PERSON>ug koom", "knockTitle": "<PERSON>b tug neeg xav tuaj koom lub rooj sib tham", "knockingParticipantList": "Knocking cov neeg koom nrog", "nameField": "<PERSON>u koj lub npe", "notificationLobbyAccessDenied": "{{targetParticipantName}} tau raug tsis kam koom los ntawm {{originParticipantName}}", "notificationLobbyAccessGranted": "{{targetParticipantName}} tau tso cai koom los ntawm {{originParticipantName}}", "notificationLobbyDisabled": "Lobby tau raug kaw los ntawm {{originParticipantName}}", "notificationLobbyEnabled": "Lobby tau qhib los ntawm {{originParticipantName}}", "notificationTitle": "Chaw tos txais", "passwordField": "Sau tus password lub rooj sib tham", "passwordJoinButton": "<PERSON><PERSON><PERSON>", "reject": "<PERSON><PERSON> lees paub", "rejectAll": "<PERSON><PERSON> lees paub tag nrho", "toggleLabel": "<PERSON><PERSON><PERSON> chaw tos txais"}, "localRecording": {"clientState": {"off": "Tawm", "on": "Ntawm", "unknown": "<PERSON><PERSON> paub"}, "dialogTitle": "Kev tswj cov ntaub ntawv hauv zos", "duration": "Txog kawg", "durationNA": "N / a", "encoding": "Chaw kawm", "label": "<PERSON>r", "labelToolTip": "Kev kaw cia hauv zos yog koom nrog", "localRecording": "Cov Ntaub Ntawv Hauv Zos", "me": "<PERSON><PERSON>", "messages": {"engaged": "Cov ntaub ntawv hauv zos tau koom nrog.", "finished": "Cov kev kaw tseg {{token}} tiav lawm. Thov xa cov ntaub ntawv kaw tseg rau tus neeg saib xyuas.", "finishedModerator": "Cov kev kaw tseg {{token}} tiav lawm. Cov ntaubntawv povthawj siv hauv zos tau txais kev cawmdim. Thov nug lwm tus neeg koom nrog xa lawv cov ntaub ntawv kaw tseg.", "notModerator": "Koj tsis yog tus neeg saib xyuas. Koj tsis tuaj yeem pib lossis tso tseg kev kaw hauv zos."}, "moderator": "<PERSON><PERSON> neeg saib x<PERSON>as", "no": "<PERSON><PERSON> muaj", "participant": "<PERSON><PERSON><PERSON>", "participantStats": "Koom nrog Stats", "sessionToken": "<PERSON><PERSON>", "start": "<PERSON><PERSON>", "stop": "<PERSON>so tseg", "yes": "Yog lawm"}, "lockRoomPassword": "tus password", "lockRoomPasswordUppercase": "Tus password", "lonelyMeetingExperience": {"button": "Caw lwm tus", "youAreAlone": "<PERSON>j yog tib tug nyob hauv lub rooj sib tham"}, "me": "kuv", "notify": {"OldElectronAPPTitle": "Kev ruaj ntseg vulnerability!", "connectedOneMember": "{{name}} tau koom lub rooj sib tham", "connectedThreePlusMembers": "{{name}} thiab {{count}} lwm tus tau koom nrog lub rooj sib tham", "connectedTwoMembers": "{{first}} thiab {{second}} tau koom nrog lub rooj sib tham", "disconnected": "txiav tawm", "focus": "<PERSON><PERSON> rooj sib tham tsom", "focusFail": "{{component}} tsis muaj - rov ua dua hauv {{ms}} sec", "grantedTo": "Moderator txoj cai tso cai rau {{to}}!", "groupTitle": "<PERSON>v ceeb toom", "hostAskedUnmute": "Tus tswv tsev xav kom koj unmute", "invitedOneMember": "{{name}} tau raug caw", "invitedThreePlusMembers": "{{name}} thiab {{count}} lwm tus tau raug caw", "invitedTwoMembers": "{{first}} thiab {{second}} tau raug caw", "kickParticipant": "{{kicked}} raug tshem tawm los ntawm {{kicker}}", "me": "<PERSON><PERSON>", "moderationInEffectCSDescription": "Thov tsa tes yog koj xav muab koj cov video", "moderationInEffectCSTitle": "Kev sib qhia cov ntsiab lus raug cuam tshuam los ntawm tus neeg saib xyuas", "moderationInEffectDescription": "Thov tsa tes yog koj xav hais lus", "moderationInEffectTitle": "Lub microphone yog muted los ntawm tus neeg saib xyuas", "moderationInEffectVideoDescription": "Thov tsa koj txhais tes yog koj xav kom koj cov yeeb yaj duab pom", "moderationInEffectVideoTitle": "Cov yeeb yaj kiab tau kaw los ntawm tus neeg saib xyuas", "moderationRequestFromModerator": "Tus tswv tsev xav kom koj unmute", "moderationRequestFromParticipant": "<PERSON>av hais lus", "moderationStartedTitle": "Kev tswj hwm pib", "moderationStoppedTitle": "Kev tswj hwm tau nres", "moderationToggleDescription": "los ntawm {{participantDisplayName}}", "moderator": "Moderator txoj cai tau tso cai!", "muted": "Koj tau pib qhov kev sib tham muted.", "mutedRemotelyDescription": "<PERSON>j muaj peev xwm unmute thaum koj npaj hais lus. Mute rov qab thaum koj ua tiav kom tsis txhob muaj suab nrov ntawm lub rooj sib tham.", "mutedRemotelyTitle": "Koj tau raug kaw los ntawm {{participantDisplayName}}!", "mutedTitle": "Koj muted!", "newDeviceAction": "Siv", "newDeviceAudioTitle": "Cov cuab yeej suab tshiab tau kuaj pom", "newDeviceCameraTitle": "T<PERSON>b pom lub koob yees duab tshiab", "oldElectronClientDescription1": "Koj zoo li tau siv ib qho qub version ntawm Meet Teev cov neeg siv khoom uas tau paub txog kev nyab xeeb tsis zoo. Thov nco ntsoov koj hloov tshiab rau peb", "oldElectronClientDescription2": "tsim tawm tshiab", "oldElectronClientDescription3": "tam sim no!", "passwordRemovedRemotely": "$t(lockRoomPasswordUppercase) tshem tawm los ntawm lwm tus neeg koom nrog", "passwordSetRemotely": "$t(lockRoomPasswordUppercase) teeb tsa los ntawm lwm tus neeg koom", "raiseHandAction": "Tsa tes", "raisedHand": "{{name}} xav hais.", "reactionSounds": "Disable suab", "reactionSoundsForAll": "Disable suab rau txhua tus", "screenShareNoAudio": "Qhia tawm lub thawv tsis tau txheeb xyuas nyob rau hauv lub qhov rais xaiv qhov screen.", "screenShareNoAudioTitle": "Qhia tsis tau qhov system audio!", "somebody": "Ib tug", "startSilentDescription": "Rov mus koom lub rooj sib tham kom qhib suab", "startSilentTitle": "<PERSON>j koom nrog tsis muaj suab tawm!", "suboptimalBrowserWarning": "Peb ntshai koj qhov kev sib ntsib yuav tsis zoo li no. Peb tab tom nrhiav txoj hauv kev los txhim kho qhov no, tab sis txog thaum ntawd thov sim siv ib qho ntawm <a href='{{recommendedBrowserPageLink}}' target='_blank'> txhawb nqa tag nrho browsers</a>.", "suboptimalExperienceTitle": "Browser ceeb toom", "unmute": "Unmining", "videoMutedRemotelyDescription": "<PERSON>j tuaj yeem tig nws ib zaug ntxiv.", "videoMutedRemotelyTitle": "<PERSON>j lub koob yees duab tau raug kaw los ntawm {{participantDisplayName}}!"}, "participantsPane": {"actions": {"allow": "Tso cai rau cov neeg tuaj koom:", "askUnmute": "Hais kom unmute", "blockEveryoneMicCamera": "Thaiv txhua tus mic thiab lub koob yees duab", "breakoutRooms": "Cov chav breakout", "forceMute": "<PERSON><PERSON>", "forceMuteAll": "Force Mute Txhua", "forceMuteAllVideo": "Force Video Mute Txhua", "forceMuteEveryoneElse": "Force Mute Lwm Tus", "forceMuteEveryoneElseVideo": "Force Mute Lwm Tus Video", "forceMuteVideo": "<PERSON><PERSON> kom mute video", "invite": "Caw ib tug", "mute": "Ntshai", "muteAll": "Muas tag nrho", "muteEveryoneElse": "Muas lwm tus", "startModeration": "Unmute lawv tus kheej lossis pib video", "stopEveryonesVideo": "Tso tseg sawv daws cov video", "stopVideo": "Nres video", "unForceMute": "Undo Force Lus Mute Audio", "unForceMuteAll": "Undo Force Mute Txhua", "unForceMuteVideo": "Undo quab yuam Mu<PERSON> Video", "unblockEveryoneMicCamera": "Tshem tawm txhua tus mic thiab lub koob yees duab", "unforceMuteAllVideo": "Undo Force Video Mute Txhua", "unforceMuteEveryoneElse": "Undo quab yuam Mute Txhua leej txhua tus", "unforceMuteEveryoneElseVideo": "Undo yuam Mute Txhua Tus Lwm Tus Video"}, "close": "<PERSON>w", "header": "Cov neeg koom nrog", "headings": {"lobby": "Lobby ({{count}})", "participantsList": "Cov neeg koom nrog lub rooj sib tham ({{count}})", "waitingLobby": "Tos hauv chaw tos txais ({{count}})"}, "search": "<PERSON><PERSON><PERSON> cov neeg koom"}, "passwordDigitsOnly": "Txog rau {{number}} tus lej", "passwordSetRemotely": "teem los ntawm lwm tus neeg koom", "polls": {"answer": {"skip": "Hla", "submit": "Xa"}, "by": "Los ntawm {{ name }}", "create": {"addOption": "Ntxiv kev xaiv", "answerPlaceholder": "Kev xaiv {{index}}", "cancel": "<PERSON>so tseg", "create": "Tsim Kev Xaiv Tsa", "pollOption": "Kev xaiv xaiv tsa {{index}}", "pollQuestion": "Lus nug", "questionPlaceholder": "Nug ib lo lus nug", "removeOption": "<PERSON><PERSON>m tawm kev xaiv", "send": "Xa"}, "notification": {"description": "<PERSON><PERSON><PERSON> chaw xaiv tsa los pov npav", "title": "Ib qho kev xaiv tsa tshiab tau ntxiv rau lub rooj sib tham no"}, "results": {"changeVote": "Hloov pov npav", "empty": "Tseem tsis tau muaj kev xaiv tsa hauv lub rooj sib tham. Pib kev xaiv tsa ntawm no!", "hideDetailedResults": "Nkaum cov ntsiab lus", "showDetailedResults": "Qhia kom meej", "vote": "Pov npav"}}, "poweredby": "© Ntsib Hour LLC", "prejoin": {"alreadyOneConferenceIsRunningInBackground": "Twb tau ib lub rooj sab laj tab tom ua tom qab.", "audioAndVideoError": "<PERSON><PERSON> thiab video yuam kev:", "audioDeviceProblem": "<PERSON><PERSON> teeb meem nrog koj lub cuab yeej", "audioOnlyError": "<PERSON><PERSON> yuam kev:", "audioTrackError": "Tsim tsis tau suab khiav.", "callMe": "Hu rau kuv", "callMeAtNumber": "Hu rau kuv ntawm tus xov tooj no:", "calling": "Hu", "configuringDevices": "Cov khoom siv teeb tsa ...", "connectedWithAudioQ": "Koj puas txuas nrog lub suab?", "connection": {"good": "<PERSON><PERSON> qhov kev sib txuas hauv internet zoo li zoo!", "nonOptimal": "<PERSON><PERSON> qhov kev sib txuas hauv internet tsis zoo", "poor": "<PERSON><PERSON> muaj kev sib txuas hauv internet tsis zoo"}, "connectionDetails": {"audioClipping": "Peb cia siab tias koj lub suab yuav raug txiav.", "audioHighQuality": "Peb cia siab tias koj lub suab yuav zoo heev.", "audioLowNoVideo": "Peb cia siab tias koj lub suab zoo yuav qis thiab tsis muaj yees duab.", "goodQuality": "Zoo heev! Koj cov xov xwm zoo yuav zoo heev.", "noMediaConnectivity": "Peb nrhiav tsis tau ib txoj hauv kev los tsim kev sib txuas xov xwm rau qhov kev sim no. Qhov no feem ntau tshwm sim los ntawm firewall lossis NAT.", "noVideo": "Peb cia siab tias koj cov yeeb yaj kiab yuav txaus ntshai.", "undetectable": "Yog tias koj tseem tsis tuaj yeem hu xov tooj hauv browser, peb xav kom koj ua kom koj cov neeg hais lus, microphone thiab lub koob yees duab tau teeb tsa kom raug, uas koj tau tso cai rau koj tus browser txoj cai siv koj lub microphone thiab lub koob yees duab, thiab koj qhov browser version yog up-to - hnub tim. Yog tias koj tseem muaj teeb meem hu xov tooj, koj yuav tsum hu rau lub vev xaib thov tsim tawm.", "veryPoorConnection": "Peb cia siab tias koj qhov kev hu xovtooj yuav yog qhov txaus ntshai heev.", "videoFreezing": "Peb cia siab tias koj cov yeeb yaj kiab yuav khov, tig dub, thiab ua pixelated.", "videoHighQuality": "Peb cia siab tias koj cov yeeb yaj kiab yuav muaj qhov zoo.", "videoLowQuality": "Peb cia siab tias koj cov vis dis aus yuav muaj qhov tsis zoo nyob rau hauv cov nqe lus ntawm tus ncej tus nqi thiab kev daws teeb meem.", "videoTearing": "Peb cia siab tias koj cov vis dis aus yuav pixelated lossis muaj cov duab kos duab."}, "copyAndShare": "Copy & share lub rooj sib tham txuas", "dashboard": "Lub dashboard", "daysAgo": "{{daysCount}} hnub dhau los", "dialInMeeting": "Hu rau lub rooj sib tham", "dialInPin": "Hu rau lub rooj sib tham thiab sau tus lej PIN:", "dialing": "<PERSON> xov tooj", "doNotShow": "T<PERSON> txhob pom qhov screen dua", "enterMeetingIdOrLink": "Nkag mus rau lub rooj sib tham <PERSON> lossis Txuas", "errorDialOut": "Hu tsis tau", "errorDialOutDisconnected": "Hu tsis tau. Txiav tawm", "errorDialOutFailed": "Hu tsis tau. Hu tsis tau", "errorDialOutStatus": "<PERSON><PERSON> kev tau txais kev hu xovtooj tawm", "errorMissingEmail": "Thov sau koj email tuaj koom lub rooj sib tham", "errorMissingName": "Thov sau koj lub npe los koom lub rooj sib tham", "errorNameLength": "Thov sau yam tsawg kawg 3 tsab ntawv hauv koj lub npe", "errorStatusCode": "yuam kev dialing out, status code: {{status}}", "errorValidation": "Tus lej validation ua tsis tau", "features": "Nta", "guestNotAllowedMsg": "Tsis pub qhua tuaj koom lub rooj sib tham no", "iWantToDialIn": "Kuv xav hu rau hauv", "initiated": "pib hu", "invalidEmail": "<PERSON><PERSON> tsis raug", "joinAMeeting": "<PERSON><PERSON> lub rooj sib tham", "joinAudioByPhone": "<PERSON><PERSON> nrog xov tooj suab", "joinMeeting": "<PERSON><PERSON> lub rooj sib tham", "joinMeetingGuest": "<PERSON><PERSON> lub rooj sib tham ua Guest", "joinWithoutAudio": "<PERSON><PERSON> nrog tsis muaj suab", "keyboardShortcuts": "Qhib cov keyboard shortcuts", "linkCopied": "Txuas tau theej rau daim ntawv teev lus", "logout": "Thwj tas", "lookGood": "Nws zoo li koj lub microphone ua haujlwm zoo", "maximumAllowedParticipantsErr": "Cov neeg tuaj koom siab tshaj plaws tau mus txog ntawm cov rooj sib tham no. Hu rau Lub Rooj Sib Tham Organizer.", "meetingReminder": "<PERSON>b rooj sib tham yuav pib {{time}}. Thov koom rov qab los yog ua ntej lub sijhawm teem tseg.", "multipleConferenceInitiation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "oops": "Oops!", "oppsMaximumAllowedParticipantsErr": "Oops! Cov neeg tuaj koom siab tshaj qhov txwv tau mus txog. Thov tos kom cov neeg koom nrog tawm mus lossis tiv tauj lub rooj sib tham.", "or": "los yog", "parallelMeetingsLicencesErr": "Tsis tuaj yeem pib Lub Rooj Sib Tham. Nco ntsoov tias koj muaj daim ntawv tso cai nquag koom nrog cov rooj sib tham <PERSON>l", "peopleInTheCall": "Cov neeg hauv kev hu", "pleaseEnterEmail": "<PERSON>hov sau Email", "pleaseEnterFullName": "<PERSON><PERSON> Sau Npe Lub Npe", "premeeting": "Kev sib ntsib ua ntej", "profile": "Sab ntawm", "readyToJoin": "Npaj los koom?", "recentMeetings": "Cov rooj sib tham tsis ntev los no", "screenSharingError": "Kev sib koom screen yuam kev:", "showScreen": "<PERSON><PERSON><PERSON> lub vijtsam ua ntej lub rooj sib tham", "signinsignup": "Sau npe / Sau npe", "startWithPhone": "<PERSON><PERSON> nrog xov tooj suab", "subScriptionInactiveErr": "<PERSON><PERSON> qhov kev tso npe yog Inactive. Tsis tuaj yeem pib <PERSON><PERSON><PERSON>j <PERSON><PERSON>.", "systemUpgradedInformation": "Peb tau hloov kho peb qhov system mus rau 2.0 version. Thov lub rooj sib tham no los ntawm kev sib qhia tus password lub rooj sib tham no", "userNotAllowedToJoin": "<PERSON><PERSON> neeg siv tsis tso cai koom", "videoOnlyError": "Video yuam kev:", "videoTrackError": "Tsim tsis tau video track.", "viewAllNumbers": "saib txhua tus lej", "waitForModeratorMsg": "Thov tos thaum Moderator koom nrog hu.", "waitForModeratorMsgDynamic": "Thov tos thaum {{Moderator}} koom nrog hu.", "youAreNotAllowed": "<PERSON><PERSON> tsis raug tso cai"}, "presenceStatus": {"busy": "<PERSON><PERSON> khoom", "calling": "Hu rau...", "connected": "<PERSON><PERSON><PERSON><PERSON> nrog", "connecting": "Kev sib txuas...", "connecting2": "Kev sib txuas *...", "disconnected": "Txiav tawm", "expired": "tas sij hawm", "ignored": "Tsis quav ntsej", "initializingCall": "<PERSON>b <PERSON>...", "invalidToken": "Tsis yog token", "invited": "Caw", "rejected": "<PERSON><PERSON> lees paub", "ringing": "Ringing ...", "signInAsHost": "Kos Npe Ua Tus Tswv Cuab"}, "profile": {"avatar": "tus avatar", "setDisplayNameLabel": "<PERSON><PERSON> lub npe tso saib", "setEmailInput": "Sau e-mail", "setEmailLabel": "<PERSON>j tus email", "title": "Sab ntawm"}, "raisedHand": "<PERSON>av hais lus", "recording": {"authDropboxText": "Upload rau Dropbox", "availableS3Space": "Siv qhov chaw: {{s3_used_space}} ntawm {{s3_free_space}}", "availableSpace": "<PERSON><PERSON> chaw muaj: {{spaceLeft}} MB (kwv yees {{duration}} feeb ntawm kev kaw)", "beta": "<PERSON><PERSON> n<PERSON>us", "busy": "Peb tab tom ua haujlwm ntawm kev tso cov ntaub ntawv kaw tseg. Thov rov sim dua hauv ob peb feeb.", "busyTitle": "Tag nrho cov recorders tam sim no tsis khoom", "copyLink": "<PERSON><PERSON>", "consentDialog": {"accept": "Kuv pom zoo", "disclaimer": "Cov ntaubntawv yuav siv rau cov ntaub ntawv, kev qhia, lossis lwm yam kev lag luam. Yog tias koj tsis pom zoo, thov tawm lub rooj sib tham tam sim no.", "leaveMeeting": "KHO SIB sib tham", "message": "<PERSON>b rooj sib tham no yog yuav tsum tau sau tseg. Los ntawm kev txuas ntxiv mus koom, koj pom zoo kom raug kaw tseg.", "title": "Cov Ntaub Ntawv Tso Cai"}, "error": "Kev kaw cia ua tsis tiav. Thov rov sim dua.", "errorFetchingLink": "<PERSON><PERSON> kev mus khaws cov ntawv txuas.", "expandedOff": "<PERSON>v kaw tseg lawm", "expandedOn": "<PERSON>b rooj sib tham tam sim no raug kaw.", "expandedPending": "Kev kaw cia tau pib ...", "failedToStart": "<PERSON>v kaw cia tsis pib", "fileSharingdescription": "<PERSON>ab cov ntaub ntawv kaw tseg nrog cov neeg koom nrog lub rooj sib tham", "limitNotificationDescriptionNative": "<PERSON>im muaj kev thov siab koj cov ntaubntawv povthawj siv yuav raug txwv rau {{limit}} min. <PERSON>u kev kaw tsis txwv sim <3>{{app}}</3>.", "limitNotificationDescriptionWeb": "<PERSON>im muaj kev thov siab koj cov ntaubntawv povthawj siv yuav raug txwv rau {{limit}} min. Rau kev kaw tsis txwv sim <a href={{url}} rel='noopener noreferrer' target='_blank'>{{app}}</a>.", "linkGenerated": "Peb tau tsim ib qhov txuas rau koj cov ntaubntawv povthawj siv.", "live": "<PERSON><PERSON> neeg nyob", "loggedIn": "Nkag mus ua {{userName}}", "off": "Kev kaw tseg nres", "offBy": "{{name}} nres qhov kaw", "on": "<PERSON>v kaw cia pib", "onBy": "{{name}} pib qhov kaw", "pending": "<PERSON><PERSON>j sau lub rooj sib tham...", "rec": "Rov sau", "recLive": "Nyob + rec", "serviceDescription": "Koj cov ntaubntawv povthawj siv yuav raug cawm dim los ntawm qhov kev pabcuam kaw", "serviceDescriptionCloud": "<PERSON><PERSON> kaw", "serviceName": "<PERSON>v pab<PERSON>am", "signIn": "<PERSON>s npe rau hauv", "signOut": "Sau npe tawm", "unavailable": "Oops! Lub {{serviceName}} tam sim no tsis muaj. Peb tab tom ua haujlwm los daws qhov teeb meem. Thov rov sim dua tom qab.", "unavailableTitle": "<PERSON>v kaw tsis muaj", "uploadToCloud": "Upload rau huab"}, "sectionList": {"pullToRefresh": "Rub kom refresh"}, "security": {"about": "Koj tuaj yeem ntxiv $t (lockRoomPassword) rau koj lub rooj sib tham. Cov neeg koom yuav tsum tau muab $t (lockRoomPassword) ua ntej lawv raug tso cai koom nrog lub rooj sib tham.", "aboutReadOnly": "Cov neeg koom nrog tuaj yeem ntxiv $t (lockRoomPassword) rau lub rooj sib tham. Cov neeg koom yuav tsum tau muab $t (lockRoomPassword) ua ntej lawv raug tso cai koom nrog lub rooj sib tham.", "insecureRoomNameWarning": "<PERSON>b npe chav tsis muaj kev nyab xeeb. Cov neeg tsis xav tau tuaj yeem koom nrog koj lub rooj sib tham. Xav txog kev ruaj ntseg koj lub rooj sib tham siv lub khawm ruaj ntseg.", "securityOptions": "Kev xaiv kev ruaj ntseg"}, "settings": {"calendar": {"about": "Kev sib koom ua ke ntawm {{appName}} daim ntawv qhia hnub yog siv txhawm rau nkag mus rau koj daim ntawv qhia hnub kom nws tuaj yeem nyeem cov xwm txheej yav tom ntej.", "disconnect": "Txiav tawm", "microsoftSignIn": "Sau npe nrog Microsoft", "signedIn": "Tam sim no nkag mus rau cov xwm txheej hauv daim ntawv teev npe rau {{email}}. Nyem lub Disconnect khawm hauv qab no kom tsis txhob nkag mus rau cov xwm txheej hauv daim ntawv teev npe.", "title": "Daim ntawv qhia hnub"}, "desktopShareFramerate": "Desktop sib koom ncej tus nqi", "desktopShareHighFpsWarning": "<PERSON><PERSON> ncej siab dua rau kev sib koom desktop yuav cuam tshuam rau koj lub bandwidth. Koj yuav tsum rov pib dua qhov kev tshuaj ntsuam qhia rau cov chaw tshiab kom muaj txiaj ntsig.", "desktopShareWarning": "<PERSON>j yuav tsum rov pib dua qhov kev tshuaj ntsuam qhia rau cov chaw tshiab kom muaj txiaj ntsig.", "devices": "Cov khoom siv", "followMe": "<PERSON>v daws raws kuv qab", "framesPerSecond": "frames-ib-second", "incomingMessage": "Cov lus tuaj", "language": "<PERSON><PERSON>", "languageSettings": "Hom lus", "loggedIn": "Nkag mus ua {{name}}", "microphones": "Microphones", "moderator": "<PERSON><PERSON> neeg saib x<PERSON>as", "more": "Ntau", "name": "Lub npe", "noDevice": "<PERSON><PERSON> muaj", "noLanguagesAvailable": "<PERSON><PERSON> muaj hom lus muaj", "participantJoined": "<PERSON><PERSON><PERSON>", "participantLeft": "<PERSON><PERSON> nrog sab laug", "playSounds": "<PERSON>a suab rau", "sameAsSystem": "<PERSON>b yam li qhov system ({{label}})", "selectAudioOutput": "<PERSON><PERSON> tso zis", "selectCamera": "<PERSON>b koob yees duab", "selectLanguage": "Xaiv hom lus", "selectMic": "Microphone", "sounds": "<PERSON><PERSON>", "speakers": "Cov neeg hais lus", "startAudioMuted": "Txhua tus pib muted", "startVideoMuted": "Txhua tus pib zais", "talkWhileMuted": "Tham thaum tsis mloog", "title": "<PERSON>w"}, "settingsView": {"advanced": "Tau siab kawg", "alertCancel": "<PERSON>so tseg", "alertOk": "Ua li cas", "alertTitle": "<PERSON><PERSON>", "alertURLText": "Lub server URL nkag mus tsis raug", "buildInfoSection": "Tsim cov ntaub ntawv", "conferenceSection": "<PERSON><PERSON> sablaj", "disableCallIntegration": "Disable kev sib koom hu ib txwm", "disableCrashReporting": "Lov Taw Qhia Sib Tw", "disableCrashReportingWarning": "Koj puas paub tseeb tias koj xav lov tes taw qhia kev sib tsoo? Qhov teeb tsa yuav raug siv tom qab koj rov pib lub app.", "disableP2P": "Disable Peer-To-Peer hom", "displayName": "Tso npe", "email": "Tus email", "header": "<PERSON>w", "profileSection": "Sab ntawm", "serverURL": "<PERSON><PERSON>g rau zaub mov URL", "showAdvanced": "Qhia cov kev teeb tsa siab heev", "startWithAudioMuted": "<PERSON><PERSON> nrog lub suab kaw", "startWithVideoMuted": "<PERSON>b nrog video muted", "version": "<PERSON><PERSON>"}, "share": {"dialInfoText": "=====\n\nTsuas yog xav hu rau hauv koj lub xov tooj?\n\n{{defaultDialInNumber}}Nyem qhov txuas no kom pom cov xov tooj hu rau lub rooj sib tham no\n{{dialInfoPageUrl}}", "mainText": "<PERSON><PERSON><PERSON> qhov txuas hauv qab no mus koom lub rooj sib tham:\n{{roomUrl}}"}, "speaker": "<PERSON><PERSON> lus", "speakerStats": {"hours": "{{count}}h", "minutes": "{{count}}m", "name": "Lub npe", "seconds": "{{count}}s", "speakerStats": "Hais lus Stats", "speakerTime": "<PERSON><PERSON> sij hawm hais lus"}, "startupoverlay": {"genericTitle": "<PERSON><PERSON> rooj sib tham yuav tsum siv koj lub microphone thiab lub koob yees duab.", "policyText": "", "title": "{{app}} yuav tsum siv koj lub microphone thiab lub koob yees duab."}, "suspendedoverlay": {"rejoinKeyTitle": "<PERSON><PERSON> koom nrog", "text": "<PERSON><PERSON> lub <i>Rejoin</i> khawm kom rov txuas dua.", "title": "<PERSON><PERSON> qhov kev hu video raug cuam tshuam vim lub computer no mus pw."}, "toolbar": {"Settings": "<PERSON>w", "Share": "Qhia", "accessibilityLabel": {"Settings": "Toggle chaw", "audioOnly": "Toggle audio nkaus xwb", "audioRoute": "Tswj lub suab ntaus ntawv", "boo": "<PERSON><PERSON>", "callQuality": "Tswj Video Zoo", "carmode": "<PERSON><PERSON><PERSON> hom", "cc": "Toggle subtitles", "chat": "<PERSON><PERSON><PERSON> / Kaw kev sib tham", "clap": "Npog", "collapse": "<PERSON>w", "document": "Toggle qhia cov ntaub ntawv", "donationCLP": "C&P Txuas Chaw", "donationLink": "DonorBox Chaw", "download": "Download tau peb cov apps", "embedMeeting": "Embed lub rooj sib tham", "expand": "Nthuav", "feedback": "Tawm tswv yim", "fullScreen": "Toggle puv npo", "genericIFrame": "Toggle daim ntawv thov sib koom", "giphy": "Toggle GIPHY zaub mov", "grantModerator": "<PERSON>v <PERSON><PERSON><PERSON>", "hangup": "Tawm ntawm lub rooj sib tham", "help": "<PERSON><PERSON>", "invite": "<PERSON><PERSON> neeg", "kick": "ncaws neeg koom", "laugh": "Luag", "leaveConference": "Tawm ntawm lub rooj sib tham", "like": "Ntiv tes", "lobbyButton": "Pab tau / Lovable Lobby hom", "localRecording": "Toggle hauv zos kaw cia tswj", "lockRoom": "Toggle lub rooj sib tham tus password", "moreActions": "<PERSON><PERSON>u yam kev ua", "moreActionsMenu": "Ntau cov ntawv qhia ua haujlwm", "moreOptions": "Qhia ntau txoj kev xaiv", "mute": "Mute / unmute", "muteEveryone": "<PERSON><PERSON> sawv daws", "muteEveryoneElse": "Muas lwm tus", "muteEveryoneElsesVideo": "Disable lwm tus lub koob yees duab", "muteEveryonesVideo": "Disable sawv daws lub koob yees duab", "participants": "Cov neeg koom nrog", "party": "<PERSON><PERSON>", "pip": "Toggle Duab-hauv-Daim duab hom", "privateMessage": "Xa cov lus ntiag tug", "profile": "<PERSON><PERSON> koj qhov profile", "raiseHand": "Nce / txo koj txhais tes", "reactionsMenu": "<PERSON><PERSON>b / Kaw cov ntawv qhia zaub mov", "recording": "Toggle kaw", "remoteMute": "<PERSON>te neeg koom", "remoteVideoMute": "Disable lub koob yees duab ntawm cov neeg koom", "removeDonation": "Tshem tawm DonorBox", "rmoveCDonation": "Tshem tawm C&P", "security": "Kev xaiv kev ruaj ntseg", "selectBackground": "Xaiv Background", "shareRoom": "Caw ib tug", "shareYourScreen": "Pib / nres sib koom koj lub vijtsam", "shareaudio": "Qhia tawm suab", "sharedvideo": "Toggle YouTube video sib koom", "shortcuts": "Toggle shortcuts", "show": "Qhia rau theem", "speakerStats": "Toggle hais lus txheeb cais", "surprised": "<PERSON>av tsis thoob", "tileView": "Toggle pobzeb saib", "toggleCamera": "Toggle lub koob yees duab", "toggleFilmstrip": "Toggle filmstrip", "toggleReactions": "Toggle cov tshuaj tiv thaiv", "videoblur": "Toggle video plooj", "videomute": "Pib / nres lub koob yees duab"}, "addPeople": "Ntxiv cov neeg rau koj hu", "audioOnlyOff": "Lov tes taw tsis muaj bandwidth tsawg", "audioOnlyOn": "Qhib hom bandwidth tsawg", "audioRoute": "Tswj lub suab ntaus ntawv", "audioSettings": "<PERSON>v teeb tsa suab", "authenticate": "Ua pov thawj", "boo": "<PERSON><PERSON>", "callQuality": "Tswj Video Zoo", "chat": "<PERSON><PERSON><PERSON> / Kaw kev sib tham", "clap": "Npog", "closeChat": "<PERSON>w kev sib tham", "closeParticipantsPane": "<PERSON>w cov neeg koom nrog pane", "closeReactionsMenu": "Kaw cov ntawv qhia zaub mov", "disableNoiseSuppression": "<PERSON><PERSON><PERSON> tawm suab nrov", "disableReactionSounds": "Koj tuaj yeem kaw cov suab nrov rau lub rooj sib tham no", "documentClose": "Kaw <PERSON>", "documentOpen": "Qhia LivePad", "donationCLP": "C&P Txuas Chaw", "donationLink": "DonorBox Chaw", "download": "Download tau peb cov apps", "e2ee": "Xaus-rau-kawg encryption", "embedMeeting": "<PERSON>bed <PERSON><PERSON>", "enterFullScreen": "Saib puv npo", "enterTileView": "Nkag mus saib cov nplais", "exitFullScreen": "Tawm Full Screen", "exitTileView": "Tawm ntawm pobzeb saib", "feedback": "Tawm tswv yim", "genericIFrameClose": "<PERSON><PERSON> WhiteBoard", "genericIFrameOpen": "Qhia WhiteBoard", "genericIFrameWeb": "Dawb Board", "hangUpText": "Koj puas paub tseeb tias koj xav Hang up?", "hangUpforEveryOne": "<PERSON> rau sawv daws", "hangUpforMe": "Hangup tsuas yog rau kuv", "hangup": "Tawm mus", "help": "<PERSON><PERSON>", "hideReactions": "Nkaum Reaction", "iOSStopScreenShareAlertMessage": "Ua siab zoo tso tseg qhov kev tshuaj ntsuam sib qhia ua ntej HangUp.", "iOSStopScreenShareAlertTitle": "<PERSON><PERSON> <PERSON>v <PERSON>b Tham Sib Tham", "invite": "<PERSON><PERSON> neeg", "inviteViaCalendar": "Caw ntawm daim ntawv qhia hnub", "laugh": "Luag", "leaveConference": "Tawm ntawm lub rooj sib tham", "like": "Ntiv tes", "lobbyButtonDisable": "Disable qhov chaw tos txais hom", "lobbyButtonEnable": "<PERSON><PERSON><PERSON> hom chaw tos txais", "login": "Nkag mus", "logout": "Thwj tas", "lowerYourHand": "Txo koj txhais tes", "moreActions": "<PERSON><PERSON>u yam kev ua", "moreOptions": "Ntau txoj kev xaiv", "mute": "Mute / unmute", "muteEveryone": "<PERSON><PERSON> sawv daws", "muteEveryonesVideo": "Disable sawv daws lub koob yees duab", "noAudioSignalDesc": "Yog tias koj tsis txhob txwm kaw nws los ntawm qhov chaw teeb tsa lossis kho vajtse, xav txog kev hloov lub cuab yeej.", "noAudioSignalDescSuggestion": "Yog tias koj tsis txhob txwm kaw nws los ntawm qhov chaw teeb tsa lossis kho vajtse, xav hloov mus rau qhov pom zoo.", "noAudioSignalDialInDesc": "<PERSON>j tseem tuaj yeem hu xov tooj rau siv:", "noAudioSignalDialInLinkDesc": "Dial-in cov lej", "noAudioSignalTitle": "Tsis muaj kev tawm tswv yim los ntawm koj lub mic!", "noisyAudioInputDesc": "Nws zoo li koj lub microphone ua suab nrov, thov xav txog kev kaw lossis hloov lub cuab yeej.", "noisyAudioInputTitle": "<PERSON>j lub microphone zoo li tsis nrov!", "openChat": "<PERSON><PERSON><PERSON> kev sib tham", "openReactionsMenu": "Qhib cov ntawv qhia zaub mov", "participants": "Cov neeg koom nrog", "party": "<PERSON>v ua koob tsheej", "pip": "<PERSON><PERSON> nyob rau hauv daim duab hom", "privateMessage": "Xa cov lus ntiag tug", "profile": "<PERSON><PERSON> koj qhov profile", "raiseHand": "Nce / txo koj txhais tes", "raiseYourHand": "Tsa koj txhais tes", "reactionBoo": "Xa boo cov tshuaj tiv thaiv", "reactionClap": "Xa clap cov tshuaj tiv thaiv", "reactionLaugh": "Xa luag cov tshuaj tiv thaiv", "reactionLike": "Xa tus ntiv tes xoo rau cov tshuaj tiv thaiv", "reactionParty": "Xa tog popper tshuaj tiv thaiv", "reactionSurprised": "Xa cov kev xav tsis thoob", "removeDonation": "Tshem tawm DonorBox", "rmoveCDonation": "Tshem tawm C&P", "security": "Kev xaiv kev ruaj ntseg", "selectBackground": "Xaiv Background", "shareRoom": "Caw ib tug", "shareaudio": "Qhia tawm suab", "sharedvideo": "Share YouTube", "shortcuts": "Saib Shortcuts", "showReactions": "Qhia Tawm", "speakerStats": "Hais lus Stats", "startScreenSharing": "<PERSON><PERSON> qhov kev tshuaj ntsuam sib qhia", "startSubtitles": "Pib subtitles", "stopAudioSharing": "Tso tseg lub suab sib koom", "stopScreenSharing": "<PERSON><PERSON> kev tshuaj n<PERSON>am sib qhia", "stopSharedVideo": "Nres YouTube video", "stopSubtitles": "Nres subtitles", "surprised": "<PERSON>av tsis thoob", "talkWhileMutedPopup": "Sim hais lus? <PERSON><PERSON> muted.", "tileViewToggle": "Toggle pobzeb saib", "toggleCamera": "Toggle lub koob yees duab", "videoSettings": "Video Chaw", "videomute": "Pib / nres lub koob yees duab", "voiceCommand": "Qhib Voice Command", "whiteBoardOpen": "Qhia WhiteBoard", "zoomin": "Zoom hauv", "zoomout": "Zoom tawm"}, "transcribing": {"ClosedCaptions": "<PERSON>w lus", "LiveCaptions": "<PERSON>aj ntawv teev nyob", "NoCaptionsAvailable": "Tsis muaj cov ntawv teev", "OpenCloseCaptions": "Qhib / kaw cov lus sau", "Transcribing": "<PERSON><PERSON><PERSON>", "TranscribingNotAvailable": "<PERSON><PERSON><PERSON><PERSON> tsis muaj", "TranscriptionLangDefaultNote": "Nco tseg: Kev sib sau ntawv tsis muaj nyob hauv cov lus xaiv sib tham {{language}}, yog li yuav ua rau lus Askiv.", "TranscriptionLanguageCannotBeChangedOngoingCall": "Cov lus sib txuas tsis tuaj yeem hloov pauv ntawm kev hu ua ntej. <PERSON><PERSON><PERSON> rejoin kom tau txais cov nyhuv.", "TranscriptionLanguageCantChange": "Cov lus sib txuas tuaj yeem '\\ tsis hloov", "Transcriptions": "Kev sau ntawv", "Translate": "<PERSON><PERSON><PERSON><PERSON>", "TranslateTo": "<PERSON><PERSON><PERSON><PERSON> rau", "Translating": "Kev txhais lus", "TranslationNotAvailable": "Kev txhais lus tsis muaj", "ccButtonTooltip": "Pib / nres subtitles", "error": "Kev sau ntawv ua tsis tiav. Thov rov sim dua.", "expandedLabel": "Kev sau ntawv yog nyob rau tam sim no", "failedToStart": "Kev sau ntawv tsis tau pib", "labelToolTip": "<PERSON><PERSON> rooj sib tham tau muab luam tawm", "off": "Kev sau ntawv nres", "pending": "Npaj los sau cov rooj sib tham...", "sourceLanguageDesc": "Hais tam sim no, lub hom lus zaub mov tau teem {{sourceLanguage}}", "sourceLanguageHere": "Koj tuaj yeem hloov tau ntawm no", "start": "Pib qhia subtitles", "stop": "Tso tseg tsis qhia subtitles", "subtitlesOff": "Tawm", "subtitlesTitle": "Qhia lo lus", "tr": "Tus TR", "transcriptionQuotaExceeded": "<PERSON><PERSON>j tawm quota rau lub hlis no", "transcriptionQuotaExceededTitle": "Tshaj tawm quota"}, "userMedia": {"androidGrantPermissions": "Xaiv <b><i>Allow</i></b> thaum koj tus browser nug txog kev tso cai.", "chromeGrantPermissions": "Xaiv <b><i>Allow</i></b> thaum koj tus browser nug txog kev tso cai.", "edgeGrantPermissions": "Xaiv <b><i>Yog</i></b> thaum koj tus browser nug txog kev tso cai.", "electronGrantPermissions": "Thov tso cai siv koj lub koob yees duab thiab lub microphone", "firefoxGrantPermissions": "Xaiv <b><i>Qhia Xaiv Ntaus Ntaus</i></b> thaum koj tus browser thov kev tso cai.", "iexplorerGrantPermissions": "Xaiv <b><i>OK</i></b> thaum koj tus browser nug txog kev tso cai.", "nwjsGrantPermissions": "Thov tso cai siv koj lub koob yees duab thiab lub microphone", "operaGrantPermissions": "Xaiv <b><i>Allow</i></b> thaum koj tus browser nug txog kev tso cai.", "react-nativeGrantPermissions": "Xaiv <b><i>Allow</i></b> thaum koj tus browser nug txog kev tso cai.", "safariGrantPermissions": "Xaiv <b><i>OK</i></b> thaum koj tus browser nug txog kev tso cai."}, "videoSIPGW": {"busy": "Peb tab tom ua haujlwm ntawm kev tso cov peev txheej. Thov rov sim dua hauv ob peb feeb.", "busyTitle": "<PERSON><PERSON> kev pabcuam chav tsev tam sim no tsis khoom", "errorAlreadyInvited": "{{displayName}} twb caw lawm", "errorInvite": "<PERSON><PERSON> rooj sab laj tseem tsis tau tsim. Thov rov sim dua tom qab.", "errorInviteFailed": "Peb tab tom ua haujlwm los daws qhov teeb meem. Thov rov sim dua tom qab.", "errorInviteFailedTitle": "Caw {{displayName}} ua tsis tau", "errorInviteTitle": "yuam kev caw chav", "pending": "{{displayName}} tau raug caw"}, "videoStatus": {"audioOnly": "Ud twm", "audioOnlyExpanded": "Koj nyob hauv hom qis bandwidth. Hauv hom no koj yuav tau txais cov suab thiab kev tshuaj ntsuam nkaus xwb.", "callQuality": "Video Zoo", "hd": "Hi", "hdTooltip": "Saib high definition video", "highDefinition": "<PERSON><PERSON> txhais", "labelTooiltipNoVideo": "<PERSON><PERSON> muaj video", "labelTooltipAudioOnly": "Tsawg bandwidth hom enabled", "ld": "Ld", "ldTooltip": "<PERSON>b cov yeeb yaj kiab uas tsis tshua muaj siab", "lowDefinition": "Tsawg txhais", "onlyAudioAvailable": "<PERSON><PERSON><PERSON> muaj suab xwb", "onlyAudioSupported": "<PERSON><PERSON> tsuas txhawb lub suab hauv qhov browser no xwb.", "sd": "Sd", "sdTooltip": "<PERSON><PERSON> tus qauv txhais video", "standardDefinition": "Txuj kev txhais", "uhd": "Uhd", "uhdTooltip": "Saib ultra siab txhais video", "uhighDefinition": "Ultra siab txhais"}, "videothumbnail": {"connectionInfo": "Cov ntaub ntawv txuas", "domute": "Ntshai", "domuteOthers": "Muas lwm tus", "domuteVideo": "Disable lub koob yees duab", "domuteVideoOfOthers": "Disable lub koob yees duab ntawm txhua leej txhua tus", "flip": "Tig", "grantModerator": "<PERSON>v <PERSON><PERSON><PERSON>", "kick": "<PERSON>shem tawm cov neeg siv", "moderator": "<PERSON><PERSON> neeg saib x<PERSON>as", "mute": "<PERSON>s neeg koom tau kaw", "muted": "Ntshai", "remoteControl": "<PERSON><PERSON> / <PERSON>es cov chaw taws teeb tswj", "show": "Qhia rau theem", "videoMuted": "<PERSON>b koob yees duab kaw", "videomute": "<PERSON>s neeg koom tau nres lub koob yees duab"}, "virtualBackground": {"addBackground": "Ntxiv keeb kwm yav dhau", "appliedCustomImageTitle": "Upload Custom Image", "apply": "<PERSON><PERSON>", "blur": "Plooj", "customImg": "<PERSON><PERSON>", "deleteImage": "Rho tawm duab", "desktopShare": "Qhia Tawm Duab", "desktopShareError": "Tsis tuaj yeem tsim kev sib koom <PERSON>", "enableBlur": "<PERSON><PERSON> kom qhov muag plooj", "image1": "<PERSON><PERSON><PERSON>", "image2": "<PERSON><PERSON><PERSON> nruab nrab phab ntsa", "image3": "<PERSON>v dawb dawb", "image4": "Dub pem teb teeb", "image5": "<PERSON><PERSON><PERSON>", "image6": "Hav zoov", "image7": "<PERSON><PERSON><PERSON> tuaj", "none": "<PERSON><PERSON> muaj", "pleaseWait": "Thov tos...", "removeBackground": "<PERSON>shem tawm keeb kwm yav dhau", "slightBlur": "<PERSON><PERSON> muag plooj", "switchBackgroundTitle": "<PERSON><PERSON><PERSON> keeb kwm yav dhau", "title": "Virtual Background", "uploadedImage": "Upload duab {{index}}", "virtualImagesTitle": "Virtual Built-In duab", "webAssemblyWarning": "WebAssembly tsis txaus siab"}, "voicecommand": {"activePIPLabel": "<PERSON><PERSON>", "clickOnMic": "<PERSON>yem rau ntawm Mic thiab Suab ib qho lus txib", "hints": {"StopScreenSharing": "Nres Screen sib koom", "closeLivePad": "Kaw <PERSON>", "closeWhiteboard": "<PERSON><PERSON> Whiteboard", "closeYoutube": "Kaw <PERSON>e", "hints": "Cov lus qhia", "invitePeople": "Caw Neeg", "lowerHand": "<PERSON><PERSON> qis", "openChatBox": "Qhib chat box", "openClickAndPledge": "<PERSON><PERSON><PERSON> nyem thiab cog lus", "openDonorbox": "Qhib Donorbox", "openFullScreen": "Qhib puv npo", "openLivePad": "Qhib LivePad", "openLivestream": "<PERSON><PERSON><PERSON> Livestream", "openParticipantPane": "<PERSON><PERSON>b tus neeg koom nrog pane", "openRecording": "Qhib Cov Ntaub Ntawv", "openSettings": "<PERSON><PERSON><PERSON> chaw", "openSpeakerStats": "<PERSON>hib tus neeg hais lus stats", "openVideoQualityDialog": "Qhib video zoo dialog", "openVirtualBackground": "Qhib virtual keeb kwm yav dhau", "openWhiteboard": "Q<PERSON>b Whiteboard", "openYoutube": "Qhib Youtube", "raiseHand": "<PERSON><PERSON>", "removeClickAndPledge": "<PERSON>shem tawm nyem thiab cog lus", "removeDonorbox": "Tshem tawm Donorbox", "startScreenSharing": "<PERSON><PERSON> sib qhia <PERSON>"}, "inActivePIPLabel": "Hauv Active PIP", "pleaseWaitWeAreRecording": "<PERSON><PERSON>", "vcLabel": "Lub Suab Command", "voiceCommandForMeethour": "Lub Suab Command Rau <PERSON> Si<PERSON> Ntsib"}, "volumeSlider": "XOV XWM NROOG", "welcomepage": {"accessibilityLabel": {"join": "Coj mus rhaub kom koom", "roomname": "Nkag mus rau <PERSON><PERSON><PERSON>"}, "addMeetingName": "Ntxiv lub npe <PERSON>b R<PERSON>j <PERSON>", "appDescription": "<PERSON>a ntej, video tham nrog tag nrho pab neeg. Qhov tseeb, caw txhua tus uas koj paub. {{app}} yog encrypted tag nrho, 100% qhib qhov kev sib tham video daws teeb meem uas koj tuaj yeem siv txhua hnub, txhua hnub, dawb - tsis muaj tus lej xav tau.", "audioVideoSwitch": {"audio": "<PERSON><PERSON>", "video": "<PERSON>m duab"}, "calendar": "Daim ntawv qhia hnub", "connectCalendarButton": "Txuas koj daim ntawv qhia hnub", "connectCalendarText": "Txuas koj daim ntawv qhia hnub saib tag nrho koj cov rooj sib tham hauv {{app}}. Ntxiv rau, ntxiv {{provider}} cov rooj sib tham rau koj daim ntawv qhia hnub thiab pib lawv nrog ib nias.", "developerPlan": "<PERSON>s tsim tawm Plan", "enterRoomTitle": "<PERSON><PERSON> lub rooj sib tham tshiab lossis sau npe chav tsev uas twb muaj lawm", "enterprisePlan": "Kev Npaj Ua Lag Luam", "enterpriseSelfHostPlan": "Enterprise tus kheej txoj kev npaj", "features": "Nta", "footer": {"allRightsReserved": "<PERSON><PERSON><PERSON> loo", "androidAppDownload": "Hauv app download", "apiDocumentation": "API Cov ntaub ntawv", "app": "<PERSON>ho tu", "blog": "Blog", "company": "<PERSON><PERSON> txhab", "contact": "<PERSON> rau", "copyright": "Txwv", "copyrightText": "Copyright 2020 - 2024 Ntsib teev LLC. Qoob loo", "developers": "Cov neeg tsim tawm", "disclaimer": "<PERSON><PERSON> lees paub", "download": "Download tau", "email": "Tus email", "faqs": "Cov <PERSON><PERSON>", "followUs": "Ua raws li peb", "helpDesk": "<PERSON><PERSON>", "home": "Tsev", "iOSAppDownload": "iOS app download", "inTheNews": "Hauv Xov Xwm", "integrations": "Kev koom ua ke", "knowledgeBase": "<PERSON><PERSON> hauv paus kev paub", "meethour": "<PERSON><PERSON><PERSON>", "meethourLLC": "Ntsib Hour LLC", "officeAddress": "8825 Stanford, Suite 205 Columbia, MD 21045", "phone": "<PERSON><PERSON> tooj", "privacyPolicy": "Txoj Cai Tswjfwm Ntiag Tug", "productPresentation": "<PERSON><PERSON><PERSON> nthuav qhia", "refundCancellationPolicy": "Txoj cai thim rov qab & thim rov qab", "termsConditions": "Cov Cai & Cov Cai", "testimonials": "Cov ntawv pov thawj", "webMobileSDK": "Lub Vev Xaib & Mobile SDK", "whoAreYou": "Koj yog leej twg"}, "forHospitals": "<PERSON><PERSON> T<PERSON><PERSON>", "freeBannerDescription": "Free & Unlimited HD Quality Video Conference zoo li yeej tsis tau ua ntej. <PERSON><PERSON> lub rooj sib tham los ntawm txhua qhov chaw.", "freePlan": "<PERSON><PERSON>", "getHelp": "Cov <PERSON><PERSON>", "go": "Tsim lossis koom nrog Kev Sib Tham", "goSmall": "Tsim lossis koom nrog Kev Sib Tham", "header": {"accelerateDevelopmentWithPrebuiltSDKs": "Ua kom nrawm nrawm nrog SDKs ua ntej.", "apiStatus": "APK li cas", "appointmentSchedulingVideoConference": "Teem caij teem caij & Video Conference.", "blog": "Blog", "customIntegrationDedicatedSupport": "Kev cai kev cai thiab kev txhawb nqa", "customTailoredVideoMeetings": "Custom-Tailored Video Rooj Sib <PERSON>.", "developer": "<PERSON>s tsim tawm", "developers": "Cov neeg tsim tawm", "documentation": "Cov ntaub ntawv", "eMail": "E-mail", "edTech": "Tus kws kho mob", "engagingOnlineLearningForEducators": "Koom Kev Kawm Online rau Cov Kws Qhia Ntawv.", "engagingVirtualEventExperiences": "Koom nrog Virtual Event Experiences.", "enterprise": "Kev tsim mus", "enterpriseSelfHost": "Enterprise Tus K<PERSON>j", "features": "Nta", "fitness": "Kev qoj ib ce", "free": "<PERSON><PERSON><PERSON>", "fundraiseEffortlesslyWithinVideoConferences": "Kev nrhiav nyiaj txiag tsis muaj zog nyob rau hauv Video Conferences.", "fundraisingDonate": "Kev nrhiav nyiaj txiag / pub dawb", "fundraisingDonateOnline": "Kev nrhiav nyiaj txiag / pub dawb online", "getStarted": "<PERSON>b pib", "hdQualityVideoConferenceApp": "HD Zoo Nkauj Video", "help": "<PERSON><PERSON>", "helpDesk": "Pab-Desk", "highQualityLiveEventStreaming": "Zoo siab nyob cov kev tshwm sim streaming.", "hostVideoConferenceOnYourServers": "<PERSON>b rooj sib tham video ntawm koj cov servers.", "industries": "Kev lag luam", "integrateVideoCallWithinYourWebsiteApp": "Integrate Video Hu rau hauv koj lub vev xaib / app.", "interactiveVirtualLearningSolutions": "Sib tham sib Virtual Learning Solutions.", "joinAMeeting": "<PERSON><PERSON> lub rooj sib tham", "knowledgeBase": "<PERSON><PERSON> hauv paus kev paub", "liveStreaming": "Nyob streaming", "meethour": "<PERSON><PERSON><PERSON>", "myCaly": "<PERSON><PERSON>", "myCalyPricing": "MyCaly Nqe.", "mycaly": "Mercaly", "noAdsRecordingLiveStreaming": "Tsis muaj tshaj tawm + Sau + Live Streaming.", "noTimeLimitGroupCalls": "<PERSON><PERSON> <PERSON> Txwv 1: 1 & <PERSON><PERSON> Pawg Hu.", "preBuiltSDKs": "Ua ntej-ua SDKs", "pricing": "Nqe", "pro": "Tus pro", "products": "<PERSON>v khoom", "resources": "Cov peev txheej", "scheduleADemo": "<PERSON><PERSON>", "simplifiedAPIReferences": "Yo<PERSON>j Yim API cov ntaub ntawv", "smoothVideoOnboardingExperience": "<PERSON> video onboarding kev paub.", "solutions": "Kev daws teeb meem", "stayuptodateWithOurBlog": "Nyob twj ywm tshiab nrog peb blog", "systemHealthStatusandUpdates": "Cov txheej txheem kev noj qab haus huv thiab hloov tshiab", "tailoredSolutionsForYourHealthcareNeeds": "Kev daws teeb meem rau koj cov kev xav tau kho mob.", "telehealth": "Phabderhealth", "useCases": "Siv Cases", "videoConference": "Daim ntawv sib tham", "videoConferencePlans": "Video Sib Tham Cov Tswv Yim", "videoConferencePricing": "Daim video sib tham.", "videoConferencing": "Kev Sib Tham Kev Sib Tham", "videoKYC": "<PERSON><PERSON>", "virtualClassrooms": "Cov chav kawm virtual", "virtualEvents": "Cov xwm txheej Virtual", "virtualSolutionForHomeFitness": "<PERSON>v daws teeb meem Virtual rau Tsev Qiv.", "webinarSessionsWithIndustryLeaders": "Webinar Sessions nrog Cov Thawj <PERSON>j <PERSON>v <PERSON>.", "webinars": "Webinars"}, "headerSubtitle": "Kev ruaj ntseg thiab HD cov rooj sib tham zoo", "headerTitle": "<PERSON><PERSON><PERSON>", "info": "Dial-in Info", "invalidMeetingID": "<PERSON><PERSON><PERSON><PERSON> tsis raug", "jitsiOnMobile": "Ntsib <PERSON>hawm ntawm lub xov tooj ntawm tes - rub tawm peb cov apps thiab pib lub rooj sib tham los ntawm txhua qhov chaw", "join": "Tsim / Koom Nrog", "joinAMeeting": "<PERSON><PERSON> lub rooj sib tham", "logo": {"calendar": "Meem lub logo", "desktopPreviewThumbnail": "Desktop saib ua ntej thumbnail", "googleLogo": "Google logo", "logoDeepLinking": "Jitsi ntsib logo", "microsoftLogo": "Microsoft logo", "policyLogo": "Txoj cai logo"}, "meetingDate": "<PERSON>nub sib ntsib", "meetingDetails": "<PERSON><PERSON> rooj sib tham <PERSON><PERSON> meej", "meetingIsReady": "<PERSON><PERSON> rooj sib tham npaj txhij", "mobileDownLoadLinkAndroid": "Download tau mobile app rau Android", "mobileDownLoadLinkFDroid": "Download tau mobile app rau F-Droid", "mobileDownLoadLinkIos": "Download tau mobile app rau iOS no", "moderatedMessage": "Los yog <a href=\"{{url}}\" rel=\"noopener noreferrer\" target=\"_blank\">book ib lub rooj sib tham URL</a> ua ntej qhov twg koj yog tus saib xyuas xwb.", "oopsDeviceClockorTimezoneErr": "Oops! Ib yam dab tsi mus tsis ncaj ncees lawm. <PERSON><PERSON><PERSON> kom tseeb tias koj lub moos/timezone yog qhov tseeb", "oopsThereSeemsToBeProblem": "Oops zoo li muaj teeb meem", "pleaseWaitForTheStartMeeting": "Thov tos tus {{moderator}} pib lub rooj sib tham no.", "preRegistrationMsg": "<PERSON>b rooj sib tham no yuav tsum tau sau npe ua ntej. Sau npe koj tus kheej hauv qhov browser thiab koom nrog rov qab los ntawm qhov txuas email caw.", "pricing": "Nqe", "privacy": "Tsis pub twg paub", "privateMeetingErr": "Zoo li koj tab tom koom nrog kev sib tham ntiag tug. Thov koom nrog kev kos npe siv email chaw nyob caw.", "proPlan": "proPlan", "recentList": "Tsis ntev los no", "recentListDelete": "Rho tawm nkag", "recentListEmpty": "<PERSON>j daim ntawv teev npe tam sim no tsis muaj dab tsi. Tham nrog koj pab neeg thiab koj yuav pom tag nrho koj cov rooj sib tham tsis ntev los no ntawm no.", "reducedUIText": "Txais tos rau {{app}}!", "registerNow": "Sau npe tam sim no", "roomNameAllowedChars": "<PERSON>b rooj sib tham lub npe yuav tsum tsis muaj ib qho ntawm cov cim no: ?, &, :, ', \", %, #.", "roomname": "Nkag mus rau <PERSON><PERSON><PERSON>", "roomnameHint": "Sau lub npe lossis URL ntawm chav koj xav koom. Koj tuaj yeem tsim lub npe, tsuas yog cia cov neeg koj ntsib paub nws kom lawv sau tib lub npe.", "scheduleAMeeting": "<PERSON><PERSON> lub rooj sib tham", "sendFeedback": "Xa tawm tswv yim", "shiftingVirtualMeetToReality": "H<PERSON>ov Kev Sib Ntsib Virtual <PERSON>seeb", "solutions": "Kev daws teeb meem", "startMeeting": "<PERSON>b lub rooj sib tham", "terms": "Cov ntsiab lus", "timezone": "<PERSON><PERSON><PERSON><PERSON>", "title": "100% Dawb Unlimited, Xaus rau Xaus Encrypted, thiab HD Zoo Video Conferencing Solution", "tryNowItsFree": "Sim Tam sim no, <PERSON><PERSON> yog Dawb", "waitingInLobby": "Tos hauv Lobby. {{moderator}} yuav cia koj sai sai no.", "youtubeHelpTutorial": "YouTube Pab <PERSON>ls"}}