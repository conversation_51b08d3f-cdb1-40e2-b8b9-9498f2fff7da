{"addPeople": {"add": "Meg<PERSON>ív", "addContacts": "Hívja meg kap<PERSON>t", "contacts": "kapcsolatokat", "copyInvite": "Találkozói meghívó másolása", "copyLink": "Megbeszé<PERSON><PERSON> linkj<PERSON> m<PERSON>", "copyStream": "<PERSON><PERSON>ő közvetítés linkjének másolása", "countryNotSupported": "Ezt a célt még nem támogatjuk.", "countryReminder": "Az Egyesült Államokon kívül hív? Ügyeljen arra, hogy az országkóddal kezdje!", "defaultEmail": "<PERSON><PERSON> <PERSON><PERSON>lmezett e-mail címe", "disabled": "<PERSON>em hívhat meg embereket.", "doYouWantToRemoveThisPerson": "Eltávolítja ezt a személyt?", "failedToAdd": "<PERSON>em si<PERSON> a résztvevők hozzáadása", "footerText": "A kitárcsázás le <PERSON> tilt<PERSON>.", "googleCalendar": "Google Naptár", "googleEmail": "Google e-mail", "inviteMoreHeader": "Te vagy az egyetlen a találkozón", "inviteMoreMailSubject": "Csatlakozzon a {{appName}} megbeszéléséhez", "inviteMorePrompt": "<PERSON><PERSON><PERSON><PERSON> meg több embert", "linkCopied": "Link a vágólapra másolva", "loading": "Személyek és telefonszámok keresése", "loadingNumber": "Telefonszám érvényesítése", "loadingPeople": "Meg<PERSON><PERSON><PERSON><PERSON><PERSON> sze<PERSON>lyek k<PERSON>sése", "loadingText": "Terhelés...", "noResults": "Nincsenek megfelelő keresési eredmények", "noValidNumbers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adjon meg egy telefonszámot", "outlookEmail": "Outlook e-mail", "phoneNumbers": "telefonszámokat", "searchNumbers": "Telefonszámok hozzáadása", "searchPeople": "Emberek keresése", "searchPeopleAndNumbers": "<PERSON><PERSON><PERSON> em<PERSON>, vagy adja hozzá telefonszámukat", "searching": "Keresés...", "sendWhatsa[pp": "WhatsApp", "shareInvite": "Meghívó megosztása", "shareInviteP": "Ossza meg a találkozó meghívóját j<PERSON>val", "shareLink": "Mások meghívásához ossza meg a megbeszélés linkjét", "shareStream": "Oszd meg az élő közvetítés linkjét", "sipAddresses": "k<PERSON><PERSON> c<PERSON>", "telephone": "Telefonszám}}", "title": "Hívjon meg embereket erre a találkozóra", "yahooEmail": "Yahoo e-mail"}, "audioDevices": {"bluetooth": "Bluetooth", "headphones": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "none": "Nem <PERSON>k rendelkezésre audioeszközök", "phone": "Telefon", "speaker": "Hangszóró"}, "audioOnly": {"audioOnly": "Alacsony s<PERSON>sz<PERSON>less<PERSON>g"}, "breakoutRooms": {"actions": {"add": "<PERSON><PERSON>b<PERSON><PERSON> (Beta)", "autoAssign": "Automatikus hozzárendelés a szobákhoz", "close": "Bezárás", "join": "Csatlakozás", "leaveBreakoutRoom": "Kilépés a szobából", "more": "<PERSON><PERSON><PERSON>", "remove": "Eltávolítás", "rename": "Átnevezés", "renameBreakoutRoom": "Szoba átnevezése", "sendToBreakoutRoom": "Résztvevő küldése ide:"}, "breakoutList": "Szobák listája", "buttonLabel": "<PERSON><PERSON><PERSON><PERSON>", "defaultName": "Szoba #{{index}}", "hideParticipantList": "Résztvevők listájának elrejtése", "mainRoom": "Fő szoba", "notifications": {"joined": "Csatlakozás a \"{{name}}\" szobához", "joinedMainRoom": "Csatlakozás a fő szobához", "joinedTitle": "<PERSON><PERSON><PERSON><PERSON>"}, "showParticipantList": "Résztvevők listájának megjelenítése", "showParticipants": "Mutasd meg a résztvevőket", "title": "<PERSON><PERSON><PERSON><PERSON>"}, "calendarSync": {"addMeetingURL": "Adjon hozzá egy érte<PERSON>zle<PERSON> link<PERSON>", "confirmAddLink": "Hozzá szeretne adni egy Találkozni órával linket ehhez az eseményhez?", "error": {"appConfiguration": "A naptárintegráció nincs megfelelően konfigurálva.", "generic": "Hiba történt. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> a naptár <PERSON>, vagy pr<PERSON>b<PERSON><PERSON>ja meg frissíteni a naptárt.", "notSignedIn": "Hiba történt a naptáresemények megtekintéséhez való hitelesítés során. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ellenőrizze a naptár be<PERSON>, és próbáljon meg újra bejelentkezni."}, "join": "Csatlakozik", "joinTooltip": "Csatlakozzon a találkozóhoz", "nextMeeting": "következő találkozó", "noEvents": "Nincsenek betervezve közelgő események.", "ongoingMeeting": "folyamatban lévő találkozó", "permissionButton": "Nyissa meg a beállításokat", "permissionMessage": "A naptári engedély szükséges ahhoz, hogy megtekinthesse értekezleteit az alkalmazásban.", "refresh": "<PERSON><PERSON><PERSON><PERSON>", "today": "Ma"}, "carmode": {"actions": {"selectSoundDevice": "Válassza ki a hangeszközt"}, "labels": {"buttonLabel": "<PERSON><PERSON><PERSON> mó<PERSON>", "title": "<PERSON><PERSON><PERSON> mó<PERSON>", "videoStopped": "A videód le<PERSON>t"}}, "chat": {"enter": "Lépjen be a csevegőszobába", "error": "Hiba: az üzenetet nem küldték el. Ok: {{error}}", "fieldPlaceHolder": "<PERSON><PERSON><PERSON> be üzenetét ide", "message": "Üzenet", "messageAccessibleTitle": "{{user}} azt mondja:", "messageAccessibleTitleMe": "én azt mondja:", "messageTo": "Privát üzenet a következőnek: {{recipient}}", "messagebox": "<PERSON><PERSON><PERSON> be egy üzenetet", "nickname": {"popover": "<PERSON><PERSON><PERSON><PERSON>", "title": "Adjon meg egy becenevet a csevegés használatához"}, "noMessagesMessage": "Még nincsenek üzenetek az értekezleten. Kezdj beszélgetést itt!", "privateNotice": "Privát üzenet a következőnek: {{recipient}}", "smileysPanel": "Hangulatjelek panel", "tabs": {"chat": "Csevegés", "polls": "Szavazások"}, "title": "Csevegés és szavazások", "titleWithPolls": "Csevegés és szavazások", "you": "te"}, "chromeExtensionBanner": {"buttonText": "Telepítse a Chrome-bővítményt", "close": "<PERSON><PERSON><PERSON><PERSON>", "dontShowAgain": "Ne mutasd ezt még egyszer", "installExtensionText": "Telepítse a bővítményt a Google Naptár és az Office 365 integrációjához"}, "clickandpledge": {"errorDesc": "Adjon meg érvényes Click and Pledge Connect GUID-t. További részletekért látogasson el a következő oldalra: https://connect.clickandpledge.com/", "errorNotification": "Érvénytelen kattintás és fogadás GUID", "title": "A C&P Connect adományozási beállításai", "titlenative": "Kattintson és fogadja el"}, "connectingOverlay": {"joiningRoom": "Csatlakoztatjuk a találkozóhoz..."}, "connection": {"ATTACHED": "Csatolt", "AUTHENTICATING": "Hitelesítés", "AUTHFAIL": "A hitelesítés nem sikerült", "CONNECTED": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CONNECTING": "Csatlakozás", "CONNFAIL": "A csatlakozás si<PERSON>telen", "DISCONNECTED": "Szétkapcsolt", "DISCONNECTING": "Leválasztás", "ERROR": "Hiba", "FETCH_SESSION_ID": "Munkamenet-azonosító <PERSON>...", "GET_SESSION_ID_ERROR": "Munkamenet-azonosító hiba lekérése: {{code}}", "GOT_SESSION_ID": "Munkamenet-azon<PERSON><PERSON><PERSON><PERSON> be<PERSON>... Kész", "LOW_BANDWIDTH": "A {{displayName}} videóját a sávszélesség megtakarítása érdekében kikapcsoltuk"}, "connectionindicator": {"address": "Cím:", "audio_ssrc": "Audio SSRC:", "bandwidth": "Be<PERSON><PERSON>lt sávszélesség:", "bitrate": "Bitráta:", "bridgeCount": "Szerverek száma:", "codecs": "<PERSON><PERSON><PERSON> (A/V):", "connectedTo": "Csatlakozva:", "e2e_rtt": "E2E RTT:", "framerate": "Képkockasebesség:", "less": "<PERSON><PERSON><PERSON>", "localaddress": "<PERSON><PERSON><PERSON> c<PERSON>:", "localaddress_plural": "<PERSON><PERSON><PERSON>:", "localport": "<PERSON><PERSON><PERSON>:", "localport_plural": "<PERSON><PERSON><PERSON> portok:", "maxEnabledResolution": "k<PERSON><PERSON><PERSON> max", "more": "<PERSON><PERSON><PERSON>", "packetloss": "Csomagvesztés:", "participant_id": "Résztvevő azonosítója:", "quality": {"good": "<PERSON><PERSON>", "inactive": "Inaktív", "lost": "Elveszett", "nonoptimal": "<PERSON><PERSON>", "poor": "Szegény"}, "remoteaddress": "Távoli cím:", "remoteaddress_plural": "Távoli címek:", "remoteport": "Távoli port:", "remoteport_plural": "Távoli portok:", "resolution": "Felbontás:", "savelogs": "Mentse el a naplókat", "status": "Kapcsolat:", "transport": "Szállítás:", "transport_plural": "Szállítások:", "video_ssrc": "Videó SSRC:"}, "dateUtils": {"earlier": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "today": "Ma", "yesterday": "Tegnap"}, "deepLinking": {"appNotInstalled": "Használja {{app}} mobilalkalmazásunkat, hogy telefonján csatlakozzon ehhez a megbeszéléshez.", "continueWithBrowser": "Folytassa a Böngészővel", "description": "Nem tö<PERSON> semmi? Megpróbáltuk elindítani a megbeszélést az {{app}} asztali alkalmazásban. Próbálja ú<PERSON>, vagy indí<PERSON> el az {{app}} internetes alkalmazásban.", "descriptionWithoutWeb": "Nem tö<PERSON> semmi? Megpróbáltuk elindítani a megbeszélést az {{app}} asztali alkalmazásban.", "downloadApp": "Töltse le az alkalmazást", "ifDoNotHaveApp": "Ha még nem rendelkezik az alkalmazással:", "ifHaveApp": "Ha már rendelkezik az alkalmazással:", "ifYouDontHaveTheAppYet": "Ha még nem rendelkezik az alkalmazással", "joinInApp": "Csatlakozzon ehhez az értekezlethez az alkalmazás segítségével", "joinMeetingWithDesktopApp": "Csatlakozzon a Meetinghez az asztali alkalmazással", "launchMeetingInDesktopApp": "Indítsa el a Meeting programot az asztali alkalmazásban", "launchWebButton": "Indítás a weben", "title": "Megbeszélés indítása az {{app}} alkalmazásban...", "tryAgainButton": "Próbálja újra az asztalon"}, "defaultLink": "p<PERSON><PERSON><PERSON><PERSON> {{url}}", "defaultNickname": "volt. <PERSON>", "deviceError": {"cameraError": "<PERSON><PERSON> a kamerá<PERSON>", "cameraPermission": "Hiba történt a kameraengedély megszerzése során", "microphoneError": "<PERSON><PERSON>érni a mikrofont", "microphonePermission": "Hiba történt a mikrofonengedély megszerzése során"}, "deviceSelection": {"noPermission": "<PERSON><PERSON> enged<PERSON><PERSON> nem adott", "previewUnavailable": "Az előnézet nem érhető el", "selectADevice": "Válasszon ki egy eszközt", "testAudio": "<PERSON><PERSON><PERSON><PERSON> le egy teszthangot"}, "dialOut": {"statusMessage": "most {{status}}"}, "dialog": {"Back": "<PERSON><PERSON><PERSON>", "Cancel": "M<PERSON>gs<PERSON>", "IamHost": "Én vagyok a házigazda", "Ok": "RENDBEN", "Remove": "Távolítsa el", "Share": "Részesedés", "Submit": "<PERSON><PERSON><PERSON><PERSON>", "WaitForHostMsg": "A <b>{{room}}</b> konferencia még nem kezd<PERSON>tt el. Ha <PERSON> a házigazda, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hitelesítse magát. Ellenkező esetben várja meg, amíg a házigazda megérkezik.", "WaitForHostMsgWOk": "A <b>{{room}}</b> konferencia még nem kezd<PERSON>tt el. Ha <PERSON> a házigazda, nyomja meg az OK gombot a hitelesítéshez. Ellenkező esetben várja meg, amíg a házigazda megérkezik.", "WaitforModerator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, v<PERSON><PERSON><PERSON> meg a moderátor <PERSON>", "WaitforModeratorOk": "<PERSON><PERSON>", "WaitingForHost": "Várjuk a házigazdát...", "WaitingForHostTitle": "Várjuk a házigazdát...", "Yes": "Igen", "accessibilityLabel": {"liveStreaming": "<PERSON><PERSON><PERSON>vet<PERSON>"}, "add": "Hozzáadás", "allow": "En<PERSON><PERSON><PERSON><PERSON>ze", "alreadySharedVideoMsg": "Egy másik résztvevő már megoszt egy videót. Ez a konferencia egyszerre csak egy megosztott videót engedélyez.", "alreadySharedVideoTitle": "Egyszerre csak egy megosztott videó engedélyezett", "applicationWindow": "Alkalmazás ablak", "authenticationRequired": "Hitelesítés szükséges", "cameraConstraintFailedError": "Fényképezőgépe nem tesz eleget a szükséges megkötések egy részének.", "cameraNotFoundError": "A kamera nem található.", "cameraNotSendingData": "Nem férünk hozzá a kamerájához. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, hogy más alkalmazás használja-e ezt az eszközt, válasszon másik eszközt a beállítások menüből, vagy próbálja meg újra betölteni az alkalmazást.", "cameraNotSendingDataTitle": "<PERSON><PERSON> le<PERSON> ho<PERSON>érni a kamerához", "cameraPermissionDeniedError": "<PERSON><PERSON> adott engedé<PERSON>t a kamera használatára. <PERSON><PERSON><PERSON><PERSON><PERSON> is csatlakozhat a konferenciához, de mások nem látnak. A probléma megoldásához használja a kamera gombot a címsorban.", "cameraTimeoutError": "<PERSON><PERSON> el<PERSON> a videoforrást. Időtúllépés történt!", "cameraUnknownError": "A fényképezőgép ismeretlen okból nem használható.", "cameraUnsupportedResolutionError": "A fényképezőgép nem támogatja a szükséges videofelbontást.", "cannotToggleScreenSharingNotSupported": "A képernyőmegosztás nem váltható át: nem tá<PERSON>.", "close": "<PERSON><PERSON><PERSON><PERSON>", "closingAllTerminals": "Az összes terminál bezá<PERSON>", "conferenceDisconnectMsg": "Érdemes ellenőrizni a hálózati kapcsolatot. Újracsatlakozás {{seconds}} másodperc múlva...", "conferenceDisconnectTitle": "<PERSON><PERSON>.", "conferenceReloadMsg": "Ezt próbáljuk megjavítani. Újracsatlakozás {{seconds}} másodperc múlva...", "conferenceReloadTitle": "<PERSON>jn<PERSON> v<PERSON>.", "confirm": "Erősítse meg", "confirmNo": "Nem", "confirmYes": "Igen", "connectError": "Hoppá! <PERSON><PERSON>, és nem tudtunk csatlakozni a konferenciához.", "connectErrorWithMsg": "Hoppá! <PERSON><PERSON>, és nem tudtunk csatlakozni a konferenciához: {{msg}}", "connecting": "Csatlakozás", "contactSupport": "Lépjen ka<PERSON>csolatba az ügyfélszolgálattal", "copied": "M<PERSON>ol<PERSON>", "copy": "Másolat", "customAwsRecording": "Egyéni Aws felvétel", "deleteCache": "Törölje a gyorsítótárat", "dismiss": "Elvetés", "displayNameRequired": "Szia! mi a neved?", "displayUserName": "", "donationCNPLabel": "Adja meg a Csatlakozási űrlap URL-jét vagy a Widget URL-címét", "donationCNotificationTitle": "Adomán<PERSON><PERSON><PERSON> a <PERSON>lick and <PERSON><PERSON> se<PERSON><PERSON><PERSON><PERSON>", "donationLabel": "Adja meg az adományozási kampány URL-jét", "donationNotificationDescription": "Adományozzon minket ügyünk támogatására", "donationNotificationTitle": "Adományozz a Donorboxon keresztül", "done": "<PERSON><PERSON><PERSON>", "e2eeDescription": "A végpontok közötti titkosítás jelenleg KÍSÉRLETES. Ne feledje, hogy a végpontok közötti titkosítás bekapcsolása hatékonyan letiltja a szerveroldali szolgáltatásokat, mint például a rögzítést, az élő közvetítést és a telefonos részvételt. Ne feledje azt is, hogy az értekezlet csak olyan felhasználók számára működik, akik olyan böngészőkből csatlakoznak, amelyek támogatják a beilleszthető adatfolyamokat.", "e2eeLabel": "Engedélyezze a végpontok közötti titkosítást", "e2eeWarning": "FIGYELMEZTETÉS: <PERSON><PERSON>, nem minden résztvevő támogatja a végpontok közötti titkosítást. Ha engedélyezi, nem fognak látni vagy hall<PERSON>.", "embedMeeting": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enterCdonation": "Példa: https://connect.clickandpledge.com/w/Form/283ah-0069-441a-ba0f-d42a9dec9821", "enterDisplayEmail": "E-mail azonosító", "enterDisplayName": "Teljes név", "enterDisplayNameToJoin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adja meg a nevét a csatlakozáshoz", "enterDonation": "<PERSON><PERSON><PERSON>: https://donorbox.org/donate-an-organisation", "enterMeetingId": "<PERSON><PERSON><PERSON> be a megbeszélés azonosítóját", "enterMeetingPassword": "<PERSON><PERSON><PERSON> be a megbeszélés jelszavát", "error": "Hiba", "errorMeetingID": "Találkozóazonosító hozzáadása", "errorMeetingPassword": "Adja ho<PERSON> az értekezlet jelszavát", "forceMuteEveryoneDialog": "Biztosan le szeretné zárni a moderátorok kivételével mindenki mikrofonját? Nem tudják maguk feloldani a mikrofon zárolását, de a kényszerített némítás visszavonása után feloldhatják a némítást és beszélhetnek.", "forceMuteEveryoneElseDialog": "Némítsa <PERSON>, valamint tiltsa le a mikrofonjukat", "forceMuteEveryoneElseTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> a <PERSON>, kiv<PERSON><PERSON> {{whom}}?", "forceMuteEveryoneElsesVideoDialog": "<PERSON> a kamera le <PERSON>, ak<PERSON> nem tudj<PERSON> engedélyezni a kamerát", "forceMuteEveryoneElsesVideoTitle": "Kényszerítette mindenki ka<PERSON>, kiv<PERSON><PERSON> {{whom}}?", "forceMuteEveryoneSelf": "magad", "forceMuteEveryoneStartMuted": "Ezentúl mindenki némítva kezdi az erőt", "forceMuteEveryoneTitle": "Kényszerített némítás?", "forceMuteEveryonesVideoDialog": "Biztosan zárolni akarja a résztvevő videóját? Nem tudja feloldani a videót", "forceMuteEveryonesVideoTitle": "Kényszerítette mindenki videójának némítását?", "forceMuteParticipantBody": "Kényszerített némítás résztvevő.", "forceMuteParticipantButton": "Némítás kényszerítése", "forceMuteParticipantDialog": "Biztosan zárolni szeretné ennek a résztvevőnek a mikrofonját? Nem fogja tudni feloldani a mikrofont, de ha visszavonja az erőszakos némítást, feloldhatja a némítást és beszélhet.", "forceMuteParticipantTitle": "Kényszerítette ennek a résztvevőnek a némítását?", "forceMuteParticipantsVideoBody": "A résztvevők videója kikapcsol, és nem tudják új<PERSON> bekapcsolni", "forceMuteParticipantsVideoButton": "<PERSON><PERSON><PERSON>", "forceMuteParticipantsVideoTitle": "Letiltja ennek a résztvevőnek a kameráját?", "gracefulShutdown": "Szervizünk jelenleg karbantartás miatt nem üzemel. Kérjük, próbá<PERSON>ja ú<PERSON> k<PERSON>.", "grantModeratorDialog": "Biztosan moderátorrá szeretnéd tenni ezt a résztvevőt?", "grantModeratorTitle": "<PERSON><PERSON><PERSON><PERSON>", "guestUser": "Vendégfelhas<PERSON>ó", "hangUpLeaveReason": "Ezt a találkozót a moderátor befejezte", "hideShareAudioHelper": "Ne jelenítse meg új<PERSON> ezt a párbeszédpanelt", "incorrectPassword": "Helytelen f<PERSON>ználónév vagy j<PERSON>", "incorrectRoomLockPassword": "<PERSON><PERSON><PERSON><PERSON>", "internalError": "Hoppá! Valami elromlott. A következő hiba történt: {{error}}", "internalErrorTitle": "Belső hiba", "kickMessage": "Jaj! Ki lettél távolítva a találkozóról!", "kickParticipantButton": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> eltávolítása", "kickParticipantDialog": "Biztosan eltávolítja ezt a résztvevőt?", "kickParticipantTitle": "Eltávolítja ezt a résztvevőt?", "kickTitle": "Jaj! Önt eltávolították a megbeszélésről", "liveStreaming": "<PERSON><PERSON><PERSON>vet<PERSON>", "liveStreamingDisabledBecauseOfActiveRecordingTooltip": "<PERSON><PERSON>, ha a felvétel aktív", "liveStreamingDisabledForGuestTooltip": "A vendégek nem indíthatják el az élő közvetítést.", "liveStreamingDisabledTooltip": "<PERSON><PERSON>ő közvetítés indítása letiltva.", "localUserControls": "<PERSON><PERSON><PERSON> f<PERSON>ználói vezérlők", "lockMessage": "<PERSON><PERSON> le<PERSON> a konferenciát.", "lockRoom": "$t érte<PERSON><PERSON><PERSON> ho<PERSON>(lockRoomPasswordNagybetű)", "lockTitle": "A zárolás nem sikerült", "login": "Bejelentkezés", "logoutQuestion": "<PERSON><PERSON><PERSON>, hogy ki s<PERSON>et<PERSON>, <PERSON>s leállítja a konferenciát?", "logoutTitle": "Kijelentkezés", "maxUsersLimitReached": "Elérte a résztvevők maximális számának korlátját. A konferencia megtelt. Kérjük, lépjen ka<PERSON> a megbeszélés tula<PERSON>, vagy próbálja újra később!", "maxUsersLimitReachedTitle": "Elérte a maximális résztvevői korlátot", "meetHourRecording": "Találkozzon órás felvétel", "meetingID": "Ta<PERSON><PERSON>lkozó azonosítója", "meetingIDandPassword": "Ír<PERSON> be a találkozó azonosítóját és jelszavát", "meetingPassword": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "messageErrorApi": "hoppá! Valami elromlott", "messageErrorInvalid": "Érvénytelen hitelesítő adatok", "messageErrorNotModerator": "Hoppá! Ön nem moderátora ennek a találkozónak", "messageErrorNull": "A felhasználónév vagy a jelszó üres", "micConstraintFailedError": "A mikrofonja nem tesz eleget néhány követelménynek.", "micNotFoundError": "A mikrofon nem található.", "micNotSendingData": "Nyissa meg a számítógép beállításait a mikrofon némításának feloldásához és a hangerő beállításához", "micNotSendingDataTitle": "A mikrofont elnémították a rendszerbeállítások", "micPermissionDeniedError": "<PERSON><PERSON> adott engedé<PERSON>t a mikrofon használatára. Tov<PERSON><PERSON>ra is csatlakozhat a konferenciához, de mások nem hallanak. A probléma megoldásához használja a kamera gombot a címsorban.", "micTimeoutError": "<PERSON><PERSON> elind<PERSON> a hangforrást. Időtúllépés történt!", "micUnknownError": "A mikrofon ismeretlen okból nem használható.", "muteEveryoneDialog": "<PERSON><PERSON><PERSON>, hogy mindenkit le akar némítani? A némításukat nem tudod felolda<PERSON>, de <PERSON>k bárm<PERSON>r feloldhatják saját magukat.", "muteEveryoneElseDialog": "A némítást követően nem tudja feloldani a némításukat, de ők bármikor feloldhatják saját magukat.", "muteEveryoneElseTitle": "<PERSON>en<PERSON><PERSON>, k<PERSON><PERSON><PERSON> {{whom}}?", "muteEveryoneElsesVideoDialog": "A kamera letiltása után nem tudja újra bekapcsolni, de <PERSON><PERSON> bármikor újra bekapcsolhatják.", "muteEveryoneElsesVideoTitle": "<PERSON><PERSON><PERSON> ka<PERSON>, k<PERSON><PERSON><PERSON> {{whom}}?", "muteEveryoneSelf": "magad", "muteEveryoneStartMuted": "Ezentúl mindenki némítva indul", "muteEveryoneTitle": "Mindenkit elnémít?", "muteEveryonesVideoDialog": "Biztosan letiltja mindenki ka<PERSON>ájá<PERSON>? Ön nem tudja majd ú<PERSON>, de <PERSON>k b<PERSON><PERSON><PERSON>r visszakapcsolhatják.", "muteEveryonesVideoDialogOk": "<PERSON><PERSON><PERSON>", "muteEveryonesVideoTitle": "<PERSON>iltja mindenki kameráját?", "muteParticipantBody": "A némításukat nem tudod felo<PERSON>, de <PERSON><PERSON> bá<PERSON><PERSON>r feloldhatják saját magukat.", "muteParticipantButton": "Néma", "muteParticipantDialog": "Biztosan elnémítja ezt a résztvevőt? A némításukat nem tudod felolda<PERSON>, de ők bármikor feloldhatják saját magu<PERSON>.", "muteParticipantTitle": "Lenémítja ezt a résztvevőt?", "muteParticipantsVideoBody": "<PERSON>n nem tudja újra bekapcsolni a ka<PERSON>, de <PERSON>k bármikor újra bekapcsolhatják.", "muteParticipantsVideoButton": "<PERSON><PERSON><PERSON>", "muteParticipantsVideoDialog": "Biztosan kikapcsolja ennek a résztvevőnek a kameráját? Ön nem tudja újra bekapcsolni a kamerát, de ők bármikor újra bekapcsolhatják.", "muteParticipantsVideoTitle": "Letiltja ennek a résztvevőnek a kameráját?", "noDropboxToken": "<PERSON><PERSON><PERSON> Dropbox token", "noScreensharingInAudioOnlyMode": "Csak hang módban nincs képernyőmegosztás", "password": "Je<PERSON><PERSON><PERSON>", "passwordLabel": "A találkozót egy moderátor <PERSON>. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adja meg a $t(lockRoomPassword) kódot a csatlakozáshoz.", "passwordNotSupported": "A $t(lockRoomPassword) értekezlet beállítása nem támogatott.", "passwordNotSupportedTitle": "A $t(lockRoomPasswordUppercase) nem támo<PERSON>ott", "passwordRequired": "$t(lockRoomPasswordUppercase) szükséges", "permissionCameraRequiredError": "A videós konferenciákon való részvételhez kameraengedély szükséges. K<PERSON><PERSON><PERSON><PERSON><PERSON>, adja meg a Beállításokban", "permissionErrorTitle": "<PERSON><PERSON><PERSON><PERSON>", "permissionMicRequiredError": "A hangos konferenciákon való részvételhez mikrofonengedély szükséges. K<PERSON><PERSON><PERSON><PERSON><PERSON>, adja meg a Beállításokban", "popupError": "A böngészője letiltja az előugró ablakokat ezen a webhelyen. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, engedélyezze az előugró ablakokat a böngésző biztonsági beállításainál, és próbálja újra.", "popupErrorTitle": "<PERSON>z előugró ablak blokkolva", "readMore": "t<PERSON>bb", "recording": "Felvétel", "recordingDisabledBecauseOfActiveLiveStreamingTooltip": "<PERSON><PERSON>, amíg az élő közvetítés aktív", "recordingDisabledForGuestTooltip": "A vendégek nem indíthatják el a felvételeket.", "recordingDisabledTooltip": "Felvétel indítása letiltva.", "rejoinNow": "Csatlakozzon most újra", "remoteControlAllowedMessage": "{{user}} elfogadta a távirányító kérését!", "remoteControlDeniedMessage": "{{user}} elutasította a távirányító kérelmét!", "remoteControlErrorMessage": "<PERSON><PERSON>, mik<PERSON><PERSON>ben megpróbált távirányító engedélyeket kérni a(z) {{user}} felhasználótól!", "remoteControlRequestMessage": "Engedélyezi a(z) {{user}} sz<PERSON><PERSON><PERSON>ra, hogy távolról vezérelje az asztalát?", "remoteControlShareScreenWarning": "<PERSON>egye figyelembe, hogy ha megnyomja az \"Engedélyezés\" gombot, megosztja a képernyőjét!", "remoteControlStopMessage": "A távirányítós munkamenet véget ért!", "remoteControlTitle": "Távoli as<PERSON><PERSON>i <PERSON>", "remoteUserControls": "A(z) {{username}} t<PERSON><PERSON>li felhasználói vezérlői", "removeCDonation": "Kattintás és zálogcím eltávolítva", "removeCDonationD": "Az adományozási link sikeresen eltávolítva", "removeDonation": "A Donorbox adományozási linkje eltávolítva", "removeDonationD": "Az adományozási link sikeresen eltávolítva", "removePassword": "$t(lockRoomPassword) eltávolítása", "removeSharedVideoMsg": "Biztos vagy ben<PERSON>, hogy el szeretnéd távolítani a megosztott videódat?", "removeSharedVideoTitle": "Megosztott videó eltávolítása", "reservationError": "Foglalási rendszer hiba", "reservationErrorMsg": "Hibakód: {{code}}, üzenet: {{msg}}", "restartInitiatedBecauseOfBridgeFailure": "Az újraindítás hídhiba miatt indult el", "retry": "Prób<PERSON><PERSON><PERSON>", "revokeModeration": "Visszavonja a felhasználót moderátorként?", "revokeModerationTitle": "Moderá<PERSON>ás v<PERSON>", "screenSharingAudio": "Hang megosztása", "screenSharingFailed": "Hoppá! <PERSON><PERSON>, nem tudtuk elindítani a képernyőmegosztást!", "screenSharingFailedTitle": "A képernyőmegosztás nem si<PERSON>ült!", "screenSharingPermissionDeniedError": "Hoppá! Hiba történt a képernyőmegosztási engedélyekkel. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, töltse be újra, és próbálja új<PERSON>.", "screenSharingUser": "{{displayName}} jelenleg megosztja a képernyőt", "sendPrivateMessage": "Nemrég kapott egy privát üzenetet. Privátban szándékozott válaszolni, vagy el szeretné küldeni az üzenetet a csoportnak?", "sendPrivateMessageCancel": "Küldje el a csoportnak", "sendPrivateMessageOk": "Privátban küldje el", "sendPrivateMessageTitle": "Privátban küldöm?", "serviceUnavailable": "A szolgáltatás nem elérhető", "sessTerminated": "A hívás megszakadt", "sessionRestarted": "A hívás újraindult a hídon", "shareAudio": "Folytatás", "shareAudioTitle": "A hang megosztása", "shareAudioWarningD1": "le kell állítania a képernyőmegosztást, miel<PERSON>tt megosztaná hang<PERSON>át.", "shareAudioWarningD2": "újra kell indítania a képernyőmegosztást, és be kell jelölnie a „hang megosztása” opciót.", "shareAudioWarningH1": "Ha csak hangot szeretne megosztani:", "shareAudioWarningTitle": "A hang megosztása előtt le kell állítania a képernyőmegosztást", "shareMediaWarningGenericH2": "Ha meg szeretné o<PERSON>tani a képernyőt és a hangot", "shareScreenWarningD1": "le kell állítania a hangmegosztást a képernyő megosztása előtt.", "shareScreenWarningD2": "le kell állítania a hangmegosztást, el kell indítania a képernyőmegosztást, és be kell jelölnie a „hang megosztása” opciót.", "shareScreenWarningH1": "Ha csak a képernyőjét szeretné megosztani:", "shareScreenWarningTitle": "A képernyő megosztása előtt le kell állítania a hangmegosztást", "shareVideoLinkError": "<PERSON><PERSON><PERSON><PERSON> adj meg egy helyes youtube linket.", "shareVideoTitle": "Oszd meg a Youtube-ot", "shareYourScreen": "Ossza meg képer<PERSON>", "shareYourScreenDisabled": "Képernyőmegosztás letiltva.", "shareYourScreenDisabledForGuest": "A vendégek nem oszthatnak meg képernyőt.", "startLiveStreaming": "Élő közvetítés + felvétel", "startRecording": "Indítsa el a Felvételt", "startRemoteControlErrorMessage": "Hiba történt a távvezérlő munkamenetének elindítása közben!", "stopLiveStreaming": "Streaming leállítása", "stopRecording": "Állítsa le a felvételt", "stopRecordingWarning": "<PERSON><PERSON><PERSON> ben<PERSON>, hogy le s<PERSON>et<PERSON>é <PERSON>llí<PERSON> a felvételt?", "stopStreamingWarning": "Biztos vagy ben<PERSON>, hogy le szeretnéd állítani az élő közvetítést?", "streamKey": "Élő közvetítés kulcsa", "switchInProgress": "A váltás folyamatban van.", "thankYou": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy az {{appName}} alkalmazást használja!", "token": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tokenAuthFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, nem c<PERSON>hat ehhez a híváshoz.", "tokenAuthFailedTitle": "A hitelesítés nem sikerült", "transcribing": "Átírás", "unforceMuteEveryoneDialog": "Biztosan fel akarja oldani mindenki mikrofonjának zárolását? feloldhatják a némítást és beszélhetnek.", "unforceMuteEveryoneElseDialog": "A némítás v<PERSON>zavon<PERSON>a, és engedélyezze a mikrofonjukat", "unforceMuteEveryoneElseTitle": "Visszavonja a Kényszerített némítást, kiv<PERSON><PERSON> {{whom}}?", "unforceMuteEveryoneElsesVideoDialog": "Ha a kamera enged<PERSON><PERSON><PERSON><PERSON> van, enged<PERSON><PERSON><PERSON><PERSON> tud<PERSON> a kamerájukat", "unforceMuteEveryoneElsesVideoTitle": "Mindenkinek enged<PERSON><PERSON><PERSON><PERSON> a <PERSON>, kiv<PERSON><PERSON> {{whom}}?", "unforceMuteEveryoneSelf": "magad", "unforceMuteEveryoneTitle": "Visszavonja a mindenki mikrofonjának kényszerített némítását?", "unforceMuteEveryonesVideoDialog": "Biztosan fel szeretnéd oldani a videót mindenkiről?", "unforceMuteEveryonesVideoTitle": "Engedélyezi mindenki ka<PERSON>?", "unforceMuteParticipantBody": "Résztvevő némításának visszavonása.", "unforceMuteParticipantButton": "Kényszerített némítás visszavonása", "unforceMuteParticipantDialog": "Biztosan feloldja a résztvevő videóját?", "unforceMuteParticipantTitle": "Visszavonja a résztvevő kényszerített elnémítását?", "unforceMuteParticipantsVideoBody": "A résztvevők videója be lesz ka<PERSON>olva, és újra bekapcsolhatják", "unforceMuteParticipantsVideoButton": "Kamera engedélyez<PERSON>e", "unforceMuteParticipantsVideoTitle": "Feloldja a résztvevő videójának zárolását?", "unlockRoom": "$t(lockRoomPassword) értekezlet eltávolítása", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "userIdentifier": "Felhasználói azonosító", "userPassword": "Felhasználói jelszó", "videoLink": "Videó link", "viewUpgradeOptions": "Frissítési lehetőségek megtekintése", "viewUpgradeOptionsContent": "Ha korlátlanul hozzáférhet a prémium funkciókhoz, például a felvételhez, az átírásokhoz, az RTMP streameléshez és egyebekhez, frissítenie kell a csomagot.", "viewUpgradeOptionsTitle": "Felfedezett egy prémium funkciót!", "yourEntireScreen": "A teljes k<PERSON>őd"}, "documentSharing": {"title": "LivePad"}, "donorbox": {"errorDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adjon meg érvényes Donorbox kampány URL-t. További részletekért látogasson el a www.donorbox.org oldalra", "errorNotification": "Érvénytelen Donorbox URL", "title": "Adja hozzá a DonorBox kampány URL-jét", "titlenative": "Donordoboz"}, "e2ee": {"labelToolTip": "Az audio- és videokommunikáció ebben a hívásban végponttól végpontig titkosított"}, "embedMeeting": {"title": "A találkozó beágyazása"}, "feedback": {"average": "Átlagos", "bad": "<PERSON><PERSON>", "detailsLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "good": "<PERSON><PERSON>", "rateExperience": "Értékelje a találkozó tapasztalatait", "star": "Csillag", "veryBad": "Nagyon rossz", "veryGood": "Nagyon jó"}, "giphy": {"giphy": "Csípős", "noResults": "<PERSON><PERSON><PERSON> er<PERSON> :(", "search": "Keresés a GIPHY-ban"}, "helpView": {"header": "S<PERSON>gó"}, "incomingCall": {"answer": "<PERSON><PERSON><PERSON><PERSON>", "audioCallTitle": "Bejövő hívás", "decline": "Elvetés", "productLabel": "a Találkozás órájából", "videoCallTitle": "Bejövő videohívás"}, "info": {"accessibilityLabel": "Információk megjelenítése", "addPassword": "$t(lockRoomPassword) hozzáadása", "cancelPassword": "Mégse $t(lockRoomPassword)", "conferenceURL": "Link:", "copyNumber": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>", "country": "<PERSON><PERSON><PERSON><PERSON>", "dialANumber": "Az értekezlethez való csatlakozáshoz tárcsázza a számok egyikét, majd írja be a PIN-kódot.", "dialInConferenceID": "PIN:", "dialInNotSupported": "Sajnos a betárcsázás jelenleg nem támogatott.", "dialInNumber": "Betárcsázás:", "dialInSummaryError": "Hiba történt a betárcsázási adatok lekérésekor. Kérjük, prób<PERSON><PERSON><PERSON> k<PERSON>.", "dialInTollFree": "<PERSON><PERSON><PERSON><PERSON> hí<PERSON>", "genericError": "<PERSON><PERSON><PERSON>, valami hiba t<PERSON>.", "inviteLiveStream": "A megbeszélés élő közvetítésének megtekintéséhez kattintson erre a linkre: {{url}}", "invitePhone": "Ha inkább telefonon szeretne csatlakozni, kop<PERSON><PERSON>on erre: {{number}},,{{conferenceID}}#", "invitePhoneAlternatives": "Másik betárcsázósz<PERSON><PERSON> keres?\nTekintse meg a megbeszélések betárcs<PERSON><PERSON><PERSON> s<PERSON>: {{url}}\n\n\nHa szobai telefonról is t<PERSON><PERSON><PERSON><PERSON><PERSON>, csat<PERSON><PERSON><PERSON> an<PERSON>, hogy csatlak<PERSON>na a hanghoz: {{silentUrl}}", "inviteSipEndpoint": "Ha SIP-<PERSON><PERSON><PERSON><PERSON> s<PERSON>, <PERSON><PERSON><PERSON> be ezt: {{sipUri}}", "inviteTextiOSInviteUrl": "A csatlakozáshoz kattintson a következő linkre: {{inviteUrl}}.", "inviteTextiOSJoinSilent": "Ha szobai telefonon keresztül tárc<PERSON>z, hasz<PERSON><PERSON><PERSON> ezt a linket a hangcsatlakozás nélküli csatlakozáshoz: {{silentUrl}}.", "inviteTextiOSPersonal": "{{name}} megbeszélésre hívja <PERSON>.", "inviteTextiOSPhone": "Telefonon keresztüli csatlakozáshoz használja ezt a számot: {{number}},,{{conferenceID}}#. Ha más számot keres, ez a teljes lista: {{didUrl}}.", "inviteURLFirstPartGeneral": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy csatlakozzon egy találkozóhoz.", "inviteURLFirstPartPersonal": "{{name}} megbeszélésre hívja <PERSON>.", "inviteURLSecondPart": "Csatlakozz a találkozóhoz:\n{{url}}", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "liveStreamURL": "<PERSON><PERSON><PERSON>zvetíté<PERSON>:", "moreNumbers": "<PERSON><PERSON><PERSON>", "noNumbers": "<PERSON><PERSON><PERSON><PERSON> bet<PERSON>rc<PERSON><PERSON><PERSON><PERSON>.", "noPassword": "<PERSON><PERSON><PERSON> sem", "noRoom": "<PERSON>ncs megadva helyiség a betárcsázáshoz.", "numbers": "Bet<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "password": "$t(lockRoomPasswordUppercase):", "sip": "SIP-cím", "title": "Részesedés", "tooltip": "Ossza meg a megbeszélés linkjét és betárcsázási adatait"}, "inlineDialogFailure": {"msg": "<PERSON><PERSON>it megbotlottunk.", "retry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "support": "Támogatás", "supportMsg": "Ha ez továbbra is megtörténik, forduljon hozz<PERSON>"}, "inviteDialog": {"alertText": "<PERSON><PERSON> meghívni néhány résztvevőt.", "header": "Meg<PERSON>ív", "searchCallOnlyPlaceholder": "Adja meg a telefonszámot", "searchPeopleOnlyPlaceholder": "Résztvevők keresése", "searchPlaceholder": "Résztvevő vagy telefonszám", "send": "<PERSON><PERSON><PERSON><PERSON>"}, "jitsiHome": "{{logo}} <PERSON><PERSON><PERSON>, linkek a kezdőlapra", "keyboardShortcuts": {"focusLocal": "Koncentrálj a videódra", "focusRemote": "Koncentrálj egy másik személy videójára", "fullScreen": "Teljes képer<PERSON>ő megtekintése vagy kilépés", "keyboardShortcuts": "Billentyűpar<PERSON>ok", "localRecording": "<PERSON><PERSON><PERSON> felv<PERSON>telvezérlők megjelenítése vagy el<PERSON>", "mute": "Mikrofon némítása vagy némításának feloldása", "pushToTalk": "<PERSON><PERSON><PERSON> a beszélgetést", "raiseHand": "<PERSON><PERSON><PERSON> fel vagy engedje le a kez<PERSON>t", "showSpeakerStats": "Hangszóró statisztikák megjelenítése", "toggleChat": "<PERSON><PERSON><PERSON> meg vagy <PERSON> be a csevegést", "toggleFilmstrip": "Videó indexképeinek megjelenítése vagy elrej<PERSON>se", "toggleParticipantsPane": "A résztvevők panel megjelenítése vagy elrejtése", "toggleScreensharing": "Váltás a kamera és a képernyőmegosztás között", "toggleShortcuts": "Billentyűparancsok megjelenítése vagy elrej<PERSON>se", "videoMute": "Indítsa el vagy állítsa le a kamerát", "videoQuality": "A hívásminőség kezelése"}, "liveChatView": {"header": "24x7 élő támogatás"}, "liveStreaming": {"addStream": "<PERSON><PERSON><PERSON>", "busy": "Dolgozunk a streamelési erőforrások felszabadításán. Kérjük, próbálja újra néhány perc múlva.", "busyTitle": "<PERSON><PERSON><PERSON> minden streamer foglalt", "changeSignIn": "Fiókváltás.", "choose": "Válassz élő közvetítést", "chooseCTA": "Válasszon egy streamelési lehetőséget. Jelenleg mint {{email}} vagy be<PERSON>zve.", "enterLinkedInUrlWithTheKey": "<PERSON><PERSON><PERSON> be a LinkedIn URL-címét a kulccsal", "enterStreamKey": "Itt add meg a YouTube élő közvetítés kulcsát.", "enterStreamKeyFacebook": "Ide írja be a Facebook élő közvetítés kulcsát.", "enterStreamKeyInstagram": "Adja meg itt az Instagram élő közvetítés kulcsát.", "enterStreamKeyYouTube": "Ide add meg a {{youtube}} <PERSON><PERSON><PERSON> közvetítés k<PERSON>.", "error": "Az élő közvetítés nem sikerült. Kérjük, próbá<PERSON>ja <PERSON>.", "errorAPI": "Hiba történt a YouTube-közvetítések elérése közben. Kérjük, prób<PERSON><PERSON>jon meg újra bejelentkezni.", "errorLiveStreamNotEnabled": "Az élő közvetítés nincs engedélyezve itt: {{email}}. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, engedélyezze az élő közvetítést, vagy j<PERSON> be olya<PERSON>, ahol engedélyez<PERSON> van az élő közvetítés.", "expandedOff": "Az élő közvetítés le<PERSON>llt", "expandedOn": "A találkozót jelenleg streameljük a YouTube-on.", "expandedPending": "Indul az élő közvetítés...", "failToStartAutoLiveStreaming": "<PERSON><PERSON> elindítani az automatikus élő közvetítést", "failToStartAutoRecording": "<PERSON><PERSON> elindítani az automatikus felvételt", "failedToStart": "Az élő közvetítést nem sikerült elindítani", "getStreamKeyManually": "Nem tudtunk élő közvetítést lekérni. Próbáld megszerezni az élő közvetítés kulcsát a YouTube-ról.", "googlePrivacyPolicy": "Google adatvédelmi irányelvek", "invalidStreamKey": "<PERSON><PERSON><PERSON>, hogy az élő közvetítés kulcsa hibás.", "limitNotificationDescriptionNative": "Streamelésed {{limit}} percre korlátozódik. A korlátlan streameléshez próbálja ki az {{app}} alkalmazást.", "limitNotificationDescriptionWeb": "A nagy kereslet miatt streamelésed {{limit}} percre korlátozódik. A korlátlan adatfolyamhoz próbálja ki a <a href={{url}} rel='noopener noreferrer' target='_blank'>{{app}}</a> alkalmazást.", "makeSureYouHaveEnoughStorageAvailableOnYourAccount": "Győződjön meg a<PERSON>, hogy elegend<PERSON> tá<PERSON><PERSON><PERSON>ll rendelkezésre a fiókjában.", "note": "Jegyzet", "off": "Az élő közvetítés le<PERSON>llt", "offBy": "{{name}} leállította az élő közvetítést", "on": "Elindult az élő közvetítés", "onBy": "{{name}} elindította az élő közvetítést", "pending": "<PERSON><PERSON>ő közvetítés indítása...", "pleaseContactSupportForAssistance": "Segítségért forduljon az ügyfélszolgálathoz.", "serviceName": "Élő közvetítés szolgáltatás", "signIn": "Jelentkezzen be a Google-lal", "signInCTA": "Jelentkezz be, vagy add meg az élő közvetítés kulcsát a YouTube-ról.", "signOut": "Jelentkezzen ki", "signedInAs": "Jelenleg a következő néven vagy bejelentkezve:", "start": "Felvétel + élő közvetítés", "startService": "Indítsa el a szolgáltatást", "streamIdHelp": "Mi ez?", "unavailableTitle": "Az élő közvetítés nem érhető el", "youtubeTerms": "YouTube szolgáltatási feltételek"}, "lobby": {"admit": "<PERSON>ld be", "admitAll": "Valld be az összeset", "allow": "En<PERSON><PERSON><PERSON><PERSON>ze", "backToKnockModeButton": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> k<PERSON><PERSON><PERSON><PERSON>", "dialogTitle": "<PERSON><PERSON> mód", "disableDialogContent": "A lobby mód jelenleg engedélyezve van. Ez a funkció biztosítja, hogy nem kívánt résztvevők ne csatlakozhassanak az értekezlethez. Le szeretné tiltani?", "disableDialogSubmit": "<PERSON><PERSON><PERSON>", "emailField": "Adja meg e-mail címét", "enableDialogPasswordField": "<PERSON><PERSON><PERSON><PERSON> (opcionális)", "enableDialogSubmit": "Engedélyezés", "enableDialogText": "A Lobby mód lehetővé teszi a megbeszélés védelmét a<PERSON>l, hogy csak a moderátor hivatalos jóváhagyása után engedi be az embereket.", "enterPasswordButton": "Adja meg a megbeszélés jelszavát", "enterPasswordTitle": "Adja meg a jelszót az értekezlethez való csatlakozáshoz", "invalidPassword": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "joinRejectedMessage": "Csatlakozási kérelmét egy moderátor el<PERSON>.", "joinTitle": "Csatlakozás a találkozóhoz", "joinWithPasswordMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, várjon...", "joiningMessage": "Amint valaki elfogad<PERSON>, csatlakozik a megbeszéléshez", "joiningTitle": "Megbeszéléshez csatlakozás kérése...", "joiningWithPasswordTitle": "Csatlakozás jelszóval...", "knockButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "knockTitle": "Valaki csatlakozni szeretne a találkozóhoz", "knockingParticipantList": "Kopogtató résztvevőlista", "nameField": "<PERSON><PERSON><PERSON> be a nevét", "notificationLobbyAccessDenied": "{{originParticipantName}} elutasította {{originParticipantName}} csatlakozását", "notificationLobbyAccessGranted": "{{originParticipantName}} engedélyezte a csatlakozást {{originParticipantName}} számára", "notificationLobbyDisabled": "<PERSON>z előcsarnokot letiltotta {{originParticipantName}}", "notificationLobbyEnabled": "<PERSON>z előcsarnokot engedélyezte: {{originParticipantName}}", "notificationTitle": "Előcsarnok", "passwordField": "Adja meg a megbeszélés jelszavát", "passwordJoinButton": "Csatlakozik", "reject": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rejectAll": "Az összes elutasítása", "toggleLabel": "<PERSON><PERSON> engedélyez<PERSON>"}, "localRecording": {"clientState": {"off": "Le", "on": "On", "unknown": "Ismeretlen"}, "dialogTitle": "<PERSON><PERSON><PERSON>vezérl<PERSON>", "duration": "Időtar<PERSON>", "durationNA": "N/A", "encoding": "Kódolás", "label": "<PERSON>r", "labelToolTip": "<PERSON><PERSON><PERSON> fel<PERSON> be van <PERSON>", "localRecording": "<PERSON><PERSON><PERSON>", "me": "<PERSON><PERSON><PERSON>", "messages": {"engaged": "<PERSON>lyi felvétel bekapcsolva.", "finished": "A {{token}} felvételi munkamenet befejeződött. Kérjük, küldje el a rögzített fájlt a moderátornak.", "finishedModerator": "A {{token}} felvételi munkamenet befejeződött. A helyi szám felvétele elmentve. Kérem a többi résztvevőt, hogy küldjék be felvételeiket.", "notModerator": "Nem te vagy a moderátor. A helyi felvételt nem lehet elind<PERSON> vagy le<PERSON>."}, "moderator": "<PERSON><PERSON><PERSON><PERSON>", "no": "Nem", "participant": "Résztvevő", "participantStats": "Résztvevői statisztika", "sessionToken": "Munkamenet -token", "start": "Indítsa el a Felvételt", "stop": "Felvétel leállítása", "yes": "Igen"}, "lockRoomPassword": "<PERSON><PERSON><PERSON><PERSON>", "lockRoomPasswordUppercase": "Je<PERSON><PERSON><PERSON>", "lonelyMeetingExperience": {"button": "Hí<PERSON>j meg m<PERSON>t is", "youAreAlone": "Te vagy az egyetlen a találkozón"}, "me": "nekem", "notify": {"OldElectronAPPTitle": "Biztonsági sebezhetőség!", "connectedOneMember": "{{name}} c<PERSON><PERSON><PERSON>ott a megbeszéléshez", "connectedThreePlusMembers": "{{name}} <PERSON><PERSON> to<PERSON><PERSON><PERSON>i {{count}} s<PERSON><PERSON><PERSON> a megbeszéléshez", "connectedTwoMembers": "{{first}} és {{second}} c<PERSON><PERSON><PERSON><PERSON> a megbeszéléshez", "disconnected": "szétkapcsolt", "focus": "Konferencia fókusz", "focusFail": "A(z) {{component}} nem érhe<PERSON> el – próbálkozzon újra {{ms}} másodperc múlva", "grantedTo": "A moderátori jogok megadva {{to}} számára!", "groupTitle": "Értesítések", "hostAskedUnmute": "A házigazda szeretné feloldani a némítást", "invitedOneMember": "{{name}} me<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>t", "invitedThreePlusMembers": "{{name}} <PERSON><PERSON> to<PERSON><PERSON><PERSON><PERSON> {{count}} m<PERSON><PERSON> s<PERSON> ka<PERSON>t me<PERSON>t", "invitedTwoMembers": "{{first}} és {{second}} kapott me<PERSON>", "kickParticipant": "{{kicked}} eltávolította {{kicker}}", "me": "<PERSON><PERSON><PERSON>", "moderationInEffectCSDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, em<PERSON><PERSON> fel a kez<PERSON>, ha meg szeretné o<PERSON> a videót", "moderationInEffectCSTitle": "A tartalommegosztást a moderátor let<PERSON>a", "moderationInEffectDescription": "<PERSON><PERSON><PERSON>, emelje fel a kez<PERSON>, ha beszélni szeretne", "moderationInEffectTitle": "A mikrofont a moderátor n<PERSON>", "moderationInEffectVideoDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, em<PERSON><PERSON> fel a <PERSON>ez<PERSON>, ha azt s<PERSON>et<PERSON>é, hogy videója látható legyen", "moderationInEffectVideoTitle": "A videót a moderátor <PERSON>", "moderationRequestFromModerator": "A házigazda szeretné feloldani a némítást", "moderationRequestFromParticipant": "<PERSON><PERSON><PERSON><PERSON><PERSON> akar", "moderationStartedTitle": "A moderálás megkezdődött", "moderationStoppedTitle": "A moderálás le<PERSON>t", "moderationToggleDescription": "szerző: {{participantDisplayName}}", "moderator": "A moderátori jogok megadva!", "muted": "Elnémítottad a beszélgetést.", "mutedRemotelyDescription": "<PERSON>ig felold<PERSON> a <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, amikor k<PERSON>ll a beszélgetésre. Ha végzett, né<PERSON><PERSON><PERSON><PERSON> v<PERSON>, hogy távol tartsa a zajt a megbeszéléstől.", "mutedRemotelyTitle": "{{participantDisplayName}} elnémította <PERSON>!", "mutedTitle": "Le vagy némítva!", "newDeviceAction": "<PERSON><PERSON><PERSON><PERSON>", "newDeviceAudioTitle": "Új audioeszköz <PERSON>lelve", "newDeviceCameraTitle": "<PERSON>j kamera észlelve", "oldElectronClientDescription1": "<PERSON><PERSON>, hogy a Találkozni órával kliens egy régi verziój<PERSON>, amely ismert biztonsági réseket tartalmaz. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, frissítse a mi <PERSON>alunkat", "oldElectronClientDescription2": "<PERSON><PERSON><PERSON><PERSON> build", "oldElectronClientDescription3": "jelen<PERSON>!", "passwordRemovedRemotely": "$t(lockRoomPasswordUppercase) egy másik résztvevő eltávolította", "passwordSetRemotely": "$t(lockRoomPasswordUppercase) egy másik résztvevő által beállított", "raiseHandAction": "<PERSON><PERSON><PERSON> fel a kez<PERSON>t", "raisedHand": "{{name}} s<PERSON><PERSON><PERSON> f<PERSON>ólalni.", "reactionSounds": "Hangok letiltása", "reactionSoundsForAll": "A hangok letiltása mindenki s<PERSON>ámára", "screenShareNoAudio": "A Hang megosztása négyzet nincs bejelölve az ablakválasztó képernyőn.", "screenShareNoAudioTitle": "<PERSON><PERSON> si<PERSON> me<PERSON> a rendszer<PERSON>ot!", "somebody": "<PERSON><PERSON>", "startSilentDescription": "A hang engedélyezésé<PERSON>z c<PERSON>lakozzon újra az értekezlethez", "startSilentTitle": "Hangkimenet nélkül c<PERSON>lakoztál!", "suboptimalBrowserWarning": "Attól tartunk, a találkozási élménye nem lesz olyan nagyszerű itt. Keressük a módját ennek jav<PERSON><PERSON><PERSON>, de addig k<PERSON>r<PERSON>, próbá<PERSON><PERSON> meg v<PERSON>melyik <a href='{{recommendedBrowserPageLink}}' target='_blank'>teljes mértékben támogatott böngészőt</a> használni.", "suboptimalExperienceTitle": "Figyelmeztetés a böngészőre", "unmute": "Némítás feloldása", "videoMutedRemotelyDescription": "<PERSON><PERSON>.", "videoMutedRemotelyTitle": "{{participantDisplayName}} letilt<PERSON>a a ka<PERSON>á<PERSON>!"}, "participantsPane": {"actions": {"allow": "Engedélyezze a résztvevőknek, hogy:", "askUnmute": "<PERSON><PERSON><PERSON><PERSON> meg a némítás feloldását", "blockEveryoneMicCamera": "Mindenki mikrofonjának és kamerájának letiltása", "breakoutRooms": "<PERSON><PERSON><PERSON><PERSON>", "forceMute": "<PERSON>í<PERSON>", "forceMuteAll": "<PERSON>ring Thoring All", "forceMuteAllVideo": "Videó némításának kényszerítése", "forceMuteEveryoneElse": "Némítás kényszerítése mindenki másra", "forceMuteEveryoneElseVideo": "Kényszerített némítás Mindenki más videó", "forceMuteVideo": "Videó némításának kényszerítése", "invite": "Hí<PERSON>j meg valakit", "mute": "Néma", "muteAll": "Az összes némítása", "muteEveryoneElse": "Mindenki más némítása", "startModeration": "Kapcsolja fel saj<PERSON> ma<PERSON>, vagy ind<PERSON> el a videót", "stopEveryonesVideo": "Mindenki videój<PERSON><PERSON>ll<PERSON>", "stopVideo": "Állítsa le a videót", "unForceMute": "v<PERSON><PERSON><PERSON><PERSON> er<PERSON>", "unForceMuteAll": "Visszavonta az összes erő némí<PERSON>", "unForceMuteVideo": "A videó némításának kényszerített visszavonása", "unblockEveryoneMicCamera": "Mindenki mikrofonjának és kamerájának feloldása", "unforceMuteAllVideo": "<PERSON><PERSON>za<PERSON><PERSON>ényszerített videó némítása", "unforceMuteEveryoneElse": "Kényszer visszavonása Mindenki más némítása", "unforceMuteEveryoneElseVideo": "Kényszer visszavonása Mindenki más videó némítása"}, "close": "<PERSON><PERSON><PERSON><PERSON>", "header": "Résztvevők", "headings": {"lobby": "Előcsarnok ({{count}})", "participantsList": "A megbeszélés résztvevői ({{count}})", "waitingLobby": "Várakoz<PERSON> a hallban ({{count}})"}, "search": "Résztvevők keresése"}, "passwordDigitsOnly": "Legfeljebb {{number}} számjegy", "passwordSetRemotely": "egy másik résztvevő által beállított", "polls": {"answer": {"skip": "Kiha<PERSON><PERSON>", "submit": "<PERSON><PERSON><PERSON><PERSON>"}, "by": "Szerző: {{ name }}", "create": {"addOption": "<PERSON><PERSON><PERSON>", "answerPlaceholder": "{{index}}. lehetőség", "cancel": "M<PERSON>gs<PERSON>", "create": "Hozzon létre egy <PERSON>", "pollOption": "Szavazási lehetőség {{index}}", "pollQuestion": "Szavazási kérdés", "questionPlaceholder": "<PERSON><PERSON><PERSON> fel egy <PERSON>", "removeOption": "<PERSON><PERSON>ó eltávolítása", "send": "<PERSON><PERSON><PERSON><PERSON>"}, "notification": {"description": "Nyissa meg a Szavazások lapot a szavazáshoz", "title": "Új szavazást adtak ehhez a találkozóhoz"}, "results": {"changeVote": "Szavazat módosítása", "empty": "A találkozón még nincsenek szavazások. Indítson szavazást itt!", "hideDetailedResults": "Részletek elrejtése", "showDetailedResults": "Részletek megjelenítése", "vote": "Szavazás"}}, "poweredby": "© Találkozni órával LLC", "prejoin": {"alreadyOneConferenceIsRunningInBackground": "<PERSON><PERSON><PERSON> egy konferencia fut a háttérben.", "audioAndVideoError": "Hang és videó hiba:", "audioDeviceProblem": "Probléma van az audioeszközzel", "audioOnlyError": "Hang hiba:", "audioTrackError": "<PERSON><PERSON> létrehozni.", "callMe": "<PERSON><PERSON><PERSON><PERSON> fel", "callMeAtNumber": "Hívjon ezen a számon:", "calling": "Hívás", "configuringDevices": "Eszközök konfigurálása...", "connectedWithAudioQ": "<PERSON>n ka<PERSON>ban áll a hanggal?", "connection": {"good": "Jól néz ki az internetkapcsolatod!", "nonOptimal": "Az internetkapcsolat nem optimális", "poor": "Rossz az internetkapcsolatod"}, "connectionDetails": {"audioClipping": "<PERSON><PERSON><PERSON><PERSON><PERSON>, hogy a <PERSON>j<PERSON>t le<PERSON>.", "audioHighQuality": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy hangja kiváló minőségű legyen.", "audioLowNoVideo": "<PERSON><PERSON><PERSON><PERSON><PERSON>, hogy a hangmin<PERSON>ség gyenge, <PERSON><PERSON> ninc<PERSON> kép.", "goodQuality": "Döbbenetes! A média minősége kiváló lesz.", "noMediaConnectivity": "Nem találtunk módot a médiakapcsolat létrehozására ehhez a teszthez. Ezt általában tűzfal vagy NAT okozza.", "noVideo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy a videód szörnyű lesz.", "undetectable": "Ha továbbra sem tud hívásokat kezdeményezni a böngészőben, j<PERSON>sol<PERSON><PERSON>, hogy <PERSON>, hogy a hangszórók, a mikrofon és a kamera megfelelően be van-e állítva, biztosított-e a böngészőjének a mikrofon és a kamera használati jogait, és hogy a böngésző verziója naprakész. -dátum. Ha továbbra is problémái vannak a hívással, lépjen kapcsolatba a webalkalmazás fejlesztőjével.", "veryPoorConnection": "<PERSON><PERSON><PERSON> vár<PERSON>, hogy a hívás minősége nagyon rossz lesz.", "videoFreezing": "<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON>, hogy a vide<PERSON> lefagy, feketévé válik és pixeles lesz.", "videoHighQuality": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy a videód jó minőségű legyen.", "videoLowQuality": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy videód <PERSON>asebességet és felbontást tekintve gyenge minőségű legyen.", "videoTearing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy vide<PERSON>d pixeles legyen, vagy legyen benne vizu<PERSON><PERSON> m<PERSON>."}, "copyAndShare": "Az értekezlet linkjének másolása és megosztása", "dashboard": "Műszerfal", "daysAgo": "{{daysCount}} napja", "dialInMeeting": "Tárcsázzon be az értekezletbe", "dialInPin": "Tárcsázzon be az értekezletbe, és írja be a PIN kódot:", "dialing": "Tárcsázás", "doNotShow": "Ne jelenítse meg újra ezt a képernyőt", "enterMeetingIdOrLink": "<PERSON><PERSON><PERSON> be a megbeszélés azonosítóját vagy <PERSON>", "errorDialOut": "<PERSON><PERSON>", "errorDialOutDisconnected": "<PERSON><PERSON> tárcsázni. Szétkapcsolt", "errorDialOutFailed": "<PERSON>em si<PERSON> tárcsázni. A hívás sikertelen", "errorDialOutStatus": "Hiba a kitárcsázási állapot lekérésekor", "errorMissingEmail": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adja meg e-mail-címét az értekezlethez való csatlakozáshoz", "errorMissingName": "Az értekezlethez való csatlakozáshoz adja meg a nevét", "errorNameLength": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> be legalább 3 betűt a nevében", "errorStatusCode": "Hiba a t<PERSON><PERSON>, állapotkód: {{status}}", "errorValidation": "A szám érvényesítése nem sikerült", "features": "Jellemzők", "guestNotAllowedMsg": "Vendég nem csatlakozhat ehhez a megbeszéléshez", "iWantToDialIn": "be <PERSON><PERSON>", "initiated": "Hívás k<PERSON>ezve", "invalidEmail": "Érvénytelen e-mail", "joinAMeeting": "Csatlakozzon egy értekezlethez", "joinAudioByPhone": "Csatlakozzon a telefon hangjával", "joinMeeting": "Csatlakozz a találkozóhoz", "joinMeetingGuest": "Csatlakozzon a megbeszéléshez vendégként", "joinWithoutAudio": "Csatlakozz hang nélkül", "keyboardShortcuts": "Billentyűparancsok engedélyezése", "linkCopied": "Link a vágólapra másolva", "logout": "Kijelentkezés", "lookGood": "<PERSON><PERSON> tű<PERSON>, hogy a mikrofon megfelelően működik", "maximumAllowedParticipantsErr": "Elérte a résztvevők maximális számát ezen a megbeszélésen. Lépjen ka<PERSON>ola<PERSON>ba a találkozó szervezőjével.", "meetingReminder": "A megbeszélés ekkor kezdődik: {{time}}. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, csatlakozzon a tervezett időpontban vagy k<PERSON>.", "multipleConferenceInitiation": "Több konferencia kezdeményezése", "oops": "Hoppá!", "oppsMaximumAllowedParticipantsErr": "Hoppá! Elérte a résztvevők megengedett maximális számát. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, v<PERSON><PERSON><PERSON> meg, amíg a résztvevők távoznak, vagy lé<PERSON> ka<PERSON> a találkozó szervezőjével.", "or": "vagy", "parallelMeetingsLicencesErr": "<PERSON><PERSON> elind<PERSON> a Megbeszélést. Győződjön meg arró<PERSON>, hogy rendelkezik aktív licenccel a párhuzamos értekezletekhez való csatlakozáshoz", "peopleInTheCall": "Emberek a hívásban", "pleaseEnterEmail": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adja meg az e-mail címet", "pleaseEnterFullName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adja meg a teljes nevet", "premeeting": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "profile": "Profil", "readyToJoin": "<PERSON><PERSON>zen áll a c<PERSON>lakozásra?", "recentMeetings": "Legu<PERSON><PERSON><PERSON><PERSON>ozó<PERSON>", "screenSharingError": "Képernyőmegosztási hiba:", "showScreen": "Megbeszélés el<PERSON> k<PERSON>ő engedélyezése", "signinsignup": "Bejelentkezés / Regisztráció", "startWithPhone": "Kezdje a telefon hangjával", "subScriptionInactiveErr": "Az előfizetése inaktív. <PERSON><PERSON> elindí<PERSON> a Megbeszélést.", "systemUpgradedInformation": "Rendszerünket frissítettük 2.0-s verzióra. Igényelje ezt a találkozót a megbeszélés jelszavának megosztásával", "userNotAllowedToJoin": "A felhasználó nem csatlakozhat", "videoOnlyError": "<PERSON><PERSON><PERSON> hiba:", "videoTrackError": "<PERSON><PERSON> videósávot létrehozni.", "viewAllNumbers": "az összes szám megtekintése", "waitForModeratorMsg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, am<PERSON><PERSON> a moderátor c<PERSON> a híváshoz.", "waitForModeratorMsgDynamic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, v<PERSON><PERSON><PERSON>, amíg {{Moderator}} csatlakozik a híváshoz.", "youAreNotAllowed": "Önnek nincs megengedve"}, "presenceStatus": {"busy": "Elfoglalt", "calling": "Hívás...", "connected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "connecting": "Csatlakozás...", "connecting2": "Csatlakozás*...", "disconnected": "Szétkapcsolt", "expired": "<PERSON><PERSON><PERSON><PERSON>", "ignored": "Figyelmen kívül hagyva", "initializingCall": "Hívás inicializálása...", "invalidToken": "Érv<PERSON><PERSON><PERSON>", "invited": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rejected": "Elutasítva", "ringing": "Csengetés...", "signInAsHost": "Jelentkezzen be házigazdaként"}, "profile": {"avatar": "<PERSON><PERSON><PERSON><PERSON>", "setDisplayNameLabel": "<PERSON>z Ön me<PERSON>jelení<PERSON>tt neve", "setEmailInput": "<PERSON><PERSON><PERSON> be az e-mail címet", "setEmailLabel": "<PERSON><PERSON> email címe", "title": "Profil"}, "raisedHand": "Szeretnék beszélni", "recording": {"authDropboxText": "Feltöltés a Dropboxba", "availableS3Space": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> terület: {{s3_used_space}} / {{s3_free_space}}", "availableSpace": "Ren<PERSON><PERSON><PERSON><PERSON><PERSON>: {{spaceLeft}} MB (k<PERSON><PERSON><PERSON><PERSON>belül {{duration}} perc felvétel)", "beta": "BÉTA", "busy": "Dolgozunk a felvételi erőforrások felszabadításán. K<PERSON><PERSON><PERSON><PERSON><PERSON>, próbálja újra néhány perc múlva.", "busyTitle": "Jelenleg az összes rögzítő foglalt", "copyLink": "<PERSON>", "error": "A rögzítés nem sikerült. Kérjük, pr<PERSON><PERSON><PERSON><PERSON><PERSON>.", "errorFetchingLink": "Hiba történt a rögzítési link lekérésekor.", "expandedOff": "A felvétel leállt", "expandedOn": "A találkozóról jelenleg felvétel készül.", "expandedPending": "A felvétel elkezdődik...", "failedToStart": "A felvételt nem si<PERSON>ült elindítani", "fileSharingdescription": "Ossza meg a felvételt az értekezlet résztvevőivel", "limitNotificationDescriptionNative": "A nagy kereslet miatt a felvétel {{limit}} percre korlátozódik. Korlátlan számú felvételhez próbálja ki a <3>{{app}}</3> alkalmazást.", "limitNotificationDescriptionWeb": "A nagy kereslet miatt a felvétel {{limit}} percre korlátozódik. Korlátlan számú felvételhez próbálja ki a <a href={{url}} rel='noopener noreferrer' target='_blank'>{{app}}</a> alkalmazást.", "linkGenerated": "Létrehoztunk egy linket a felvételéhez.", "live": "ÉLŐ", "loggedIn": "Bejelentkezve mint {{userName}}", "off": "A felvétel leállt", "offBy": "{{name}} le<PERSON><PERSON><PERSON><PERSON><PERSON> a felvételt", "on": "A felvétel elkezdődött", "onBy": "{{name}} el<PERSON><PERSON><PERSON><PERSON> a felvételt", "pending": "Felkészülés a találkozó rögzítésére...", "rec": "Eljegyzés", "recLive": "ÉLŐ + FELVÉTEL", "serviceDescription": "A felvételt a felvételi szolgáltatás elmenti", "serviceDescriptionCloud": "Felhőfelvétel", "serviceName": "Felvételi s<PERSON>", "signIn": "Jelentkezzen be", "signOut": "Jelentkezzen ki", "unavailable": "Hoppá! A(z) {{serviceName}} jelenleg nem elérhető. Dolgozunk a probléma megoldásán. K<PERSON><PERSON><PERSON><PERSON><PERSON>, prób<PERSON><PERSON><PERSON>.", "unavailableTitle": "A felvétel nem érhető el", "uploadToCloud": "Feltöltés a felhőbe"}, "sectionList": {"pullToRefresh": "<PERSON><PERSON><PERSON> meg a frissíté<PERSON>"}, "security": {"about": "Hozzáadhat egy $t(lockRoomPassword) értéket az értekezlethez. A résztvevőknek meg kell adniuk a $t(lockRoomPassword) k<PERSON><PERSON>t, miel<PERSON><PERSON> csatlakozhatnak az értekezlethez.", "aboutReadOnly": "A moderátor résztvevői hozzáadhatnak egy $t(lockRoomPassword) értéket az értekezlethez. A résztvevőknek meg kell adniuk a $t(lockRoomPassword) kódot, miel<PERSON>tt csatlakozhatnak az értekezlethez.", "insecureRoomNameWarning": "A szoba neve nem biztonságos. Nem kívánt résztvevők csatlakozhatnak a konferenciához. Fontolja meg a megbeszélés biztonságát a biztonsági gomb segítségével.", "securityOptions": "Biztonsági lehetőségek"}, "settings": {"calendar": {"about": "A {{appName}} naptárintegr<PERSON><PERSON><PERSON> segítségével biztonságosan hozzáférhet a naptárához, így az olvasni tudja a közelgő eseményeket.", "disconnect": "Leválasztás", "microsoftSignIn": "Jelentkezzen be a Microsofttal", "signedIn": "Jelenleg a(z) {{email}} naptári eseményeinek elérése. Kattintson az alábbi Lev<PERSON>lasztás gombra a naptáresemények elérésének leállításához.", "title": "<PERSON><PERSON><PERSON><PERSON>"}, "desktopShareFramerate": "Asztali megosztási képsebesség", "desktopShareHighFpsWarning": "A nagyobb képkockasebesség az asztali megosztásnál hatással lehet a sávszélességre. Az új beállítások érvénybe léptetéséhez újra kell indítania a képernyőmegosztást.", "desktopShareWarning": "Az új beállítások érvénybe léptetéséhez újra kell indítania a képernyőmegosztást.", "devices": "Eszközök", "followMe": "Mindenki követ engem", "framesPerSecond": "képkocka/másodperc", "incomingMessage": "Bejövő üzenet", "language": "Nyelv", "languageSettings": "<PERSON><PERSON><PERSON><PERSON>", "loggedIn": "Bejelentkezve mint {{name}}", "microphones": "Mikrofonok", "moderator": "<PERSON><PERSON><PERSON><PERSON>", "more": "<PERSON><PERSON><PERSON>", "name": "Név", "noDevice": "<PERSON><PERSON><PERSON> sem", "noLanguagesAvailable": "<PERSON><PERSON><PERSON> nyelv", "participantJoined": "Résztvevő csatlakozott", "participantLeft": "Résztvevő balra", "playSounds": "<PERSON> lej<PERSON><PERSON><PERSON><PERSON><PERSON> be<PERSON>ol<PERSON>", "sameAsSystem": "U<PERSON><PERSON>z, mint a rendszer ({{label}})", "selectAudioOutput": "Audio kimenet", "selectCamera": "<PERSON><PERSON><PERSON>", "selectLanguage": "Válassza a Nyelv lehetőséget", "selectMic": "Mikrofon", "sounds": "<PERSON><PERSON>", "speakers": "Hangszórók", "startAudioMuted": "Mindenki némán kezdi", "startVideoMuted": "Mindenki elrejtőzik", "talkWhileMuted": "Besz<PERSON><PERSON>jen n<PERSON>", "title": "Beállítások elemre"}, "settingsView": {"advanced": "<PERSON><PERSON><PERSON>", "alertCancel": "M<PERSON>gs<PERSON>", "alertOk": "RENDBEN", "alertTitle": "Figyelmeztetés", "alertURLText": "A megadott szerver URL érvénytelen", "buildInfoSection": "Építési információ", "conferenceSection": "Konferencia", "disableCallIntegration": "A natív hívásintegráció letiltása", "disableCrashReporting": "A hibajelentés letiltása", "disableCrashReportingWarning": "Biztosan letiltja a hibajelentést? A beállítás az alkalmazás újraindítása után lép érvénybe.", "disableP2P": "A Peer-To-Peer mód let<PERSON>a", "displayName": "Megjelenítési név", "email": "Email", "header": "Beállítások elemre", "profileSection": "Profil", "serverURL": "Szerver URL", "showAdvanced": "Speciális beállítások megjelenítése", "startWithAudioMuted": "Kezdje némított <PERSON>", "startWithVideoMuted": "Kezdje a videó némításával", "version": "Változat"}, "share": {"dialInfoText": "=====\n\nCsak be akar tárcs<PERSON>zni a telefonján?\n\n{{defaultDialInNumber}}Kat<PERSON><PERSON><PERSON> erre a linkre a találkozó telefonszámainak tárcsázásához\n{{dialInfoPageUrl}}", "mainText": "Az értekezlethez való csatlakozáshoz kattintson az alábbi linkre:\n{{roomUrl}}"}, "speaker": "Hangszóró", "speakerStats": {"hours": "{{count}} h", "minutes": "{{count}} p", "name": "Név", "seconds": "{{count}} s", "speakerStats": "Hang<PERSON><PERSON><PERSON><PERSON> stat<PERSON>", "speakerTime": "Hangszóró ideje"}, "startupoverlay": {"genericTitle": "Az értekezlethez mikrofont és kamerát kell használni.", "policyText": "", "title": "Az {{app}}-nek hasz<PERSON>lnia kell a mikrofont és a kamerát."}, "suspendedoverlay": {"rejoinKeyTitle": "Csatlakozzon újra", "text": "Nyomja meg az <i>Rejoin</i> gombot az újracsatlakozáshoz.", "title": "Videohívása me<PERSON>, mert a számítógép al<PERSON>ó <PERSON> kerü<PERSON>."}, "toolbar": {"Settings": "Beállítások elemre", "Share": "Részesedés", "accessibilityLabel": {"Settings": "Váltsd át a beállításokat", "audioOnly": "Csak hang váltása", "audioRoute": "Hangeszköz kezelése", "boo": "Le<PERSON><PERSON><PERSON>", "callQuality": "Videóminőség kezelése", "carmode": "<PERSON><PERSON><PERSON> mó<PERSON>", "cc": "Feliratok váltása", "chat": "Csevegés <PERSON> / bezárása", "clap": "Taps", "collapse": "Összeomlás", "document": "Megosztott dokumentum váltása", "donationCLP": "A C&P Connect beállításai", "donationLink": "DonorBox beállításai", "download": "Töltse le alkalmazásainkat", "embedMeeting": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "expand": "<PERSON>tsa ki", "feedback": "Hagyjon visszajelzést", "fullScreen": "Teljes képernyő váltása", "genericIFrame": "Megosztott alkalmazás váltása", "giphy": "Kapcsolja be a GIPHY menüt", "grantModerator": "<PERSON>", "hangup": "Hagyja el a találkozót", "help": "Seg<PERSON><PERSON><PERSON>g", "invite": "Hívjon meg embereket", "kick": "Kick résztvevő", "laugh": "Nevetés", "leaveConference": "Hagyja el a találkozót", "like": "<PERSON><PERSON><PERSON>", "lobbyButton": "Lobby mód engedélyezése/letiltása", "localRecording": "Váltsd át a helyi felvételvezérlőket", "lockRoom": "Az é<PERSON>zlet jelszavának váltása", "moreActions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "moreActionsMenu": "<PERSON><PERSON><PERSON><PERSON><PERSON> m<PERSON>k <PERSON>", "moreOptions": "Tov<PERSON><PERSON><PERSON>hetőségek megjelenítése", "mute": "Némítás / Némítás feloldása", "muteEveryone": "Mindenki némítása", "muteEveryoneElse": "Mindenki más némítása", "muteEveryoneElsesVideo": "Mindenki más kamerájának let<PERSON>", "muteEveryonesVideo": "<PERSON><PERSON><PERSON>", "participants": "Résztvevők", "party": "<PERSON><PERSON>", "pip": "Kép a képben mód váltása", "privateMessage": "Küldj privát üzenetet", "profile": "Szerkessze profilját", "raiseHand": "<PERSON>elje fel / engedje le a kez<PERSON>t", "reactionsMenu": "Reakciók menü me<PERSON> / bezárása", "recording": "Felvétel váltása", "remoteMute": "Résztvevő némítása", "remoteVideoMute": "A résztvevő kamerájának letiltása", "removeDonation": "Távolítsa el a DonorBoxot", "rmoveCDonation": "Távolítsa el a C&P-t", "security": "Biztonsági lehetőségek", "selectBackground": "Válassza a Háttér lehetőséget", "shareRoom": "Hí<PERSON>j meg valakit", "shareYourScreen": "Indítsa el / állítsa le a képernyő megosztását", "shareaudio": "Hang megosztása", "sharedvideo": "Kapcsolja be a YouTube videómegosztást", "shortcuts": "Parancsikonok váltása", "show": "Mutasd a színpadon", "speakerStats": "Hangszóróstatisztikák váltása", "surprised": "<PERSON><PERSON><PERSON><PERSON>d<PERSON><PERSON>", "tileView": "Csempenézet váltása", "toggleCamera": "Kamera váltása", "toggleFilmstrip": "Filmszalag váltása", "toggleReactions": "Reakciók váltása", "videoblur": "<PERSON><PERSON><PERSON> el<PERSON>ódásának váltása", "videomute": "Kamera indítása / leállítása"}, "addPeople": "Adjon hozzá személyeket a híváshoz", "audioOnlyOff": "<PERSON>z al<PERSON>sony sávszélességű mód letiltása", "audioOnlyOn": "Engedélyezze az alacsony sávszélességű módot", "audioRoute": "Hangeszköz kezelése", "audioSettings": "Hangbeállítások", "authenticate": "Hitelesítés", "boo": "Le<PERSON><PERSON><PERSON>", "callQuality": "Videóminőség kezelése", "chat": "Csevegés <PERSON> / bezárása", "clap": "Taps", "closeChat": "Csevegés <PERSON>", "closeParticipantsPane": "Zárja be a résztvevők ablaktáblát", "closeReactionsMenu": "<PERSON><PERSON><PERSON><PERSON> be a reakciók menüt", "disableNoiseSuppression": "Zajcsökkentés letiltása", "disableReactionSounds": "Kikapcsolhatja a reakcióhangokat ezen a megbeszélésen", "documentClose": "<PERSON><PERSON><PERSON><PERSON> <PERSON> a LivePadot", "documentOpen": "LivePad megosztása", "donationCLP": "A C&P Connect beállításai", "donationLink": "DonorBox beállításai", "download": "Töltse le alkalmazásainkat", "e2ee": "Végpontok közötti titkosítás", "embedMeeting": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enterFullScreen": "Teljes k<PERSON>ő megtekintése", "enterTileView": "Lépjen be a csempe nézetbe", "exitFullScreen": "Lépjen ki a teljes képernyőből", "exitTileView": "Lépjen ki a csempe nézetből", "feedback": "Hagyjon visszajelzést", "genericIFrameClose": "Állítsa le a táblát", "genericIFrameOpen": "Ossza meg a tá<PERSON>lát", "genericIFrameWeb": "<PERSON><PERSON><PERSON><PERSON>", "hangUpText": "<PERSON><PERSON><PERSON>, hogy le akarja tenni a telefont?", "hangUpforEveryOne": "<PERSON><PERSON><PERSON>", "hangUpforMe": "Csak nekem tedd le", "hangup": "Szabadság", "help": "Seg<PERSON><PERSON><PERSON>g", "hideReactions": "<PERSON><PERSON><PERSON><PERSON>", "iOSStopScreenShareAlertMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ts<PERSON> le a képernyő megosztását a hívás leállítása előtt.", "iOSStopScreenShareAlertTitle": "Képernyőmegosztás leállítása", "invite": "Hívjon meg embereket", "inviteViaCalendar": "Meghívás a naptáron keresztül", "laugh": "Nevetés", "leaveConference": "Hagyja el a találkozót", "like": "<PERSON><PERSON><PERSON>", "lobbyButtonDisable": "<PERSON><PERSON> mód let<PERSON>", "lobbyButtonEnable": "<PERSON><PERSON> mód engedé<PERSON>ez<PERSON>", "login": "Bejelentkezés", "logout": "Kijelentkezés", "lowerYourHand": "Engedje le a kez<PERSON>t", "moreActions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "moreOptions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mute": "Némítás / Némítás feloldása", "muteEveryone": "Mindenki némítása", "muteEveryonesVideo": "<PERSON><PERSON><PERSON>", "noAudioSignalDesc": "Ha nem szándékosan némította el a rendszerbeállításokból vagy a hardverből, fontolja meg az eszköz cseréjét.", "noAudioSignalDescSuggestion": "Ha nem szándékosan némította el a rendszerbeállításokból vagy a hardverből, fontolja meg a javasolt eszközre váltást.", "noAudioSignalDialInDesc": "Betárcsázhat a következőkkel is:", "noAudioSignalDialInLinkDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "noAudioSignalTitle": "Nem érkezik bemenet a mikrofonból!", "noisyAudioInputDesc": "<PERSON><PERSON>, mintha a mikrofonja zajt adna. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, fontolja meg a némítást vagy az eszköz cseréjét.", "noisyAudioInputTitle": "A mikrofonja zajosnak tűnik!", "openChat": "<PERSON><PERSON><PERSON> meg a csevegést", "openReactionsMenu": "Nyissa meg a reakciók menüt", "participants": "Résztvevők", "party": "Ünneplés", "pip": "<PERSON><PERSON><PERSON>", "privateMessage": "Küldj privát üzenetet", "profile": "Szerkessze profilját", "raiseHand": "<PERSON>elje fel / engedje le a kez<PERSON>t", "raiseYourHand": "<PERSON><PERSON><PERSON> fel a kez<PERSON>t", "reactionBoo": "<PERSON><PERSON>", "reactionClap": "Küldje el a taps reakciót", "reactionLaugh": "Küldj nevető reakciót", "reactionLike": "Küldje el a remek reakciót", "reactionParty": "Küldj party popper reakciót", "reactionSurprised": "K<PERSON>ldj meglepett reakciót", "removeDonation": "Távolítsa el a DonorBoxot", "rmoveCDonation": "Távolítsa el a C&P-t", "security": "Biztonsági lehetőségek", "selectBackground": "Válassza a Háttér lehetőséget", "shareRoom": "Hí<PERSON>j meg valakit", "shareaudio": "Hang megosztása", "sharedvideo": "Oszd meg a Youtube-ot", "shortcuts": "Parancsikonok megtekintése", "showReactions": "<PERSON><PERSON><PERSON><PERSON>", "speakerStats": "Hang<PERSON><PERSON><PERSON><PERSON> stat<PERSON>", "startScreenSharing": "Képernyőmegosztás indítása", "startSubtitles": "Indítsa el a feliratokat", "stopAudioSharing": "Hangmegosztás leállítása", "stopScreenSharing": "Állítsa le a képernyő megosztását", "stopSharedVideo": "Állítsa le a YouTube-videót", "stopSubtitles": "Hagyd abba a felirato<PERSON>", "surprised": "<PERSON><PERSON><PERSON><PERSON>d<PERSON><PERSON>", "talkWhileMutedPopup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>? El van némítva.", "tileViewToggle": "Csempenézet váltása", "toggleCamera": "Kamera váltása", "videoSettings": "<PERSON><PERSON><PERSON>", "videomute": "Kamera indítása / leállítása", "voiceCommand": "Nyissa meg a <PERSON>", "whiteBoardOpen": "Ossza meg a tá<PERSON>lát", "zoomin": "Nagyítás", "zoomout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "transcribing": {"ClosedCaptions": "<PERSON><PERSON><PERSON>", "LiveCaptions": "<PERSON><PERSON><PERSON>", "NoCaptionsAvailable": "<PERSON><PERSON><PERSON> felirat", "OpenCloseCaptions": "<PERSON><PERSON>ssa meg / bez<PERSON><PERSON><PERSON> a feliratokat", "Transcribing": "Átírás", "TranscribingNotAvailable": "<PERSON><PERSON> á<PERSON>í<PERSON> nem áll rendelkezésre", "TranscriptionLangDefaultNote": "Megjegyzés: A transzkripció nem érhető el a {{language}} ta<PERSON><PERSON><PERSON><PERSON><PERSON> nyelven, teh<PERSON><PERSON> alapértelmezés szerint angolul.", "TranscriptionLanguageCannotBeChangedOngoingCall": "A transzkripciós nyelv nem változtatható meg a folyamatban lévő híváson. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, csatlakozzon újra a hatálybalépéshez.", "TranscriptionLanguageCantChange": "A transzkripciós nyelv '\\ nem változik", "Transcriptions": "<PERSON><PERSON><PERSON><PERSON>", "Translate": "Fordítás", "TranslateTo": "Fordítás ide", "Translating": "<PERSON><PERSON><PERSON><PERSON>", "TranslationNotAvailable": "A fordítás nem áll rendelkezésre", "ccButtonTooltip": "Feliratok indítása / leállítása", "error": "<PERSON>z átír<PERSON> nem sikerült. K<PERSON>rj<PERSON>k, pr<PERSON><PERSON><PERSON><PERSON><PERSON>.", "expandedLabel": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON> be <PERSON> ka<PERSON>ol<PERSON>", "failedToStart": "<PERSON><PERSON> az á<PERSON>í<PERSON>", "labelToolTip": "A találkozó átírása folyamatban van", "off": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "pending": "Felkészülés az értekezlet átírására...", "sourceLanguageDesc": "Jelenleg a találkozó nyelve a következőre van beállítva: {{sourceLanguage}}", "sourceLanguageHere": "Itt módosíthatod", "start": "Indítsa el a feliratok megjelenítését", "stop": "Hagyd abba a feliratok megjelenítését", "subtitlesOff": "Kikapcsolva", "subtitlesTitle": "Feliratok", "tr": "TR", "transcriptionQuotaExceeded": "<PERSON><PERSON><PERSON> a hónapban túllépték az átírási kvótát", "transcriptionQuotaExceededTitle": "Átírási kvóta túllépve"}, "userMedia": {"androidGrantPermissions": "Válassza az <b><i>Engedélyezés</i></b> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, amikor a böngésző engedélyeket kér.", "chromeGrantPermissions": "Válassza az <b><i>Engedélyezés</i></b> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, amikor a böngésző engedélyeket kér.", "edgeGrantPermissions": "Válassza az <b><i><PERSON><PERSON></i></b> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, amikor a böngésző engedélyeket kér.", "electronGrantPermissions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ad<PERSON> engedély<PERSON> a kamera és a mikrofon használatára", "firefoxGrantPermissions": "Válassza a <b><i>Kiválasztott eszköz megosztása</i></b> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, amikor a böngésző engedélyeket kér.", "iexplorerGrantPermissions": "Válassza az <b><i>OK</i></b> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, amikor a böngész<PERSON> engedélyeket kér.", "nwjsGrantPermissions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ad<PERSON> engedély<PERSON> a kamera és a mikrofon használatára", "operaGrantPermissions": "Válassza az <b><i>Engedélyezés</i></b> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, amikor a böngésző engedélyeket kér.", "react-nativeGrantPermissions": "Válassza az <b><i>Engedélyezés</i></b> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, amikor a böngésző engedélyeket kér.", "safariGrantPermissions": "Válassza az <b><i>OK</i></b> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, amikor a böngész<PERSON> engedélyeket kér."}, "videoSIPGW": {"busy": "Dolgozunk az erőforrások felszabadításán. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, próbálja újra néhány perc múlva.", "busyTitle": "A szobaszerviz jelenleg foglalt", "errorAlreadyInvited": "{{displayName}} m<PERSON><PERSON> <PERSON>", "errorInvite": "A konferencia még nem j<PERSON> létre. K<PERSON>rjük, pró<PERSON><PERSON><PERSON><PERSON> k<PERSON>.", "errorInviteFailed": "Dolgozunk a probléma megoldásán. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, próbá<PERSON>ja ú<PERSON> k<PERSON>őbb.", "errorInviteFailedTitle": "{{displayName}} meghívása nem si<PERSON>ült", "errorInviteTitle": "Hiba történt a szoba meghívásakor", "pending": "{{displayName}} me<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>t"}, "videoStatus": {"audioOnly": "Audi", "audioOnlyExpanded": "<PERSON><PERSON> al<PERSON> sávszélességű módban van. <PERSON><PERSON><PERSON> a módban csak hang- és képernyőmegosztást kap.", "callQuality": "<PERSON><PERSON><PERSON>", "hd": "HD", "hdTooltip": "Nagy fel<PERSON>ú videó megtekintése", "highDefinition": "<PERSON><PERSON>", "labelTooiltipNoVideo": "<PERSON><PERSON><PERSON> v<PERSON>", "labelTooltipAudioOnly": "Alacsony <PERSON>sz<PERSON>lesség mód engedélyezve", "ld": "LD", "ldTooltip": "Alacsony felbontású videó megtekintése", "lowDefinition": "Alac<PERSON>y f<PERSON>", "onlyAudioAvailable": "Csak hang érhető el", "onlyAudioSupported": "<PERSON><PERSON><PERSON> a böngészőben csak a hangot támogatjuk.", "sd": "SD", "sdTooltip": "Normál felbontású videó megtekintése", "standardDefinition": "Szab<PERSON><PERSON><PERSON> def<PERSON><PERSON><PERSON>", "uhd": "Uhd", "uhdTooltip": "Ultra nagy felbontású videó megtekintése", "uhighDefinition": "Ultra nagy felbontású"}, "videothumbnail": {"connectionInfo": "Csatlakozási információ", "domute": "Néma", "domuteOthers": "Mindenki más némítása", "domuteVideo": "<PERSON><PERSON><PERSON>", "domuteVideoOfOthers": "Mindenki más kamerájának let<PERSON>", "flip": "Flip", "grantModerator": "<PERSON>", "kick": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> eltávolítása", "moderator": "<PERSON><PERSON><PERSON><PERSON>", "mute": "A résztvevő el van némítva", "muted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "remoteControl": "Távirányító indítása / leállítása", "show": "Mutasd a színpadon", "videoMuted": "A kamera letiltva", "videomute": "A résztvevő leállította a kamerát"}, "virtualBackground": {"addBackground": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "appliedCustomImageTitle": "Egyedi k<PERSON>ve", "apply": "Alkalmazni", "blur": "Elhom<PERSON><PERSON>", "customImg": "<PERSON><PERSON><PERSON><PERSON>", "deleteImage": "<PERSON><PERSON><PERSON>", "desktopShare": "<PERSON><PERSON><PERSON>i <PERSON>", "desktopShareError": "<PERSON><PERSON>ztali megosztást létrehozni", "enableBlur": "Elmosódás engedélyezése", "image1": "Strand", "image2": "<PERSON><PERSON><PERSON><PERSON> fal", "image3": "Fe<PERSON>ér ü<PERSON> szoba", "image4": "Fekete állólámpa", "image5": "<PERSON><PERSON>", "image6": "<PERSON><PERSON><PERSON>", "image7": "Napkelte", "none": "<PERSON><PERSON><PERSON> sem", "pleaseWait": "Kérem, várjon...", "removeBackground": "Távolítsa el a hátteret", "slightBlur": "<PERSON><PERSON><PERSON><PERSON>", "switchBackgroundTitle": "Háttér váltása", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "uploadedImage": "Feltöltött kép {{index}}", "virtualImagesTitle": "Virt<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "webAssemblyWarning": "A WebAssembly nem támo<PERSON>ott"}, "voicecommand": {"activePIPLabel": "Aktív PIP", "clickOnMic": "Kat<PERSON>tson a mikrofon és hang parancsra", "hints": {"StopScreenSharing": "Képernyőmegosztás leállítása", "closeLivePad": "<PERSON><PERSON><PERSON><PERSON> <PERSON> a LivePadot", "closeWhiteboard": "<PERSON><PERSON><PERSON><PERSON> be a tá<PERSON>lát", "closeYoutube": "<PERSON><PERSON><PERSON> be a Youtube-ot", "hints": "Tippek", "invitePeople": "Emberek <PERSON>", "lowerHand": "<PERSON><PERSON>ó <PERSON>", "openChatBox": "<PERSON><PERSON><PERSON> meg a chat dobozt", "openClickAndPledge": "<PERSON><PERSON><PERSON> me<PERSON>, katti<PERSON>on és fogadja el", "openDonorbox": "<PERSON><PERSON><PERSON> meg a Donorboxot", "openFullScreen": "Teljes k<PERSON> megnyitása", "openLivePad": "<PERSON><PERSON>ssa meg a LivePadot", "openLivestream": "<PERSON><PERSON>ő közvetítés megnyitás<PERSON>", "openParticipantPane": "Nyissa meg a Résztvevő ablaktáblát", "openRecording": "Nyissa meg a Felvétel lehetőséget", "openSettings": "Nyissa meg a beállításokat", "openSpeakerStats": "Nyissa meg a hangszóró statisztikáit", "openVideoQualityDialog": "Nyissa meg a videó minőségi párbeszédpanelt", "openVirtualBackground": "<PERSON><PERSON><PERSON> meg a virtu<PERSON><PERSON> h<PERSON>et", "openWhiteboard": "<PERSON><PERSON><PERSON> meg a tá<PERSON>t", "openYoutube": "<PERSON><PERSON><PERSON> meg a Youtube-ot", "raiseHand": "<PERSON><PERSON><PERSON> fel a kez<PERSON>t", "removeClickAndPledge": "Távolítsa el a kattintást és fogadja el", "removeDonorbox": "Távolítsa el a Donorboxot", "startScreenSharing": "Képernyőmegosztás indítása"}, "inActivePIPLabel": "Aktív PIP-ben", "pleaseWaitWeAreRecording": "K<PERSON>rem, várjon, felveszünk", "vcLabel": "Hangutasítás", "voiceCommandForMeethour": "Hangutasítás a Találkozni órával számára"}, "volumeSlider": "Hangerő csúszka", "welcomepage": {"accessibilityLabel": {"join": "Koppintson a csatlakozáshoz", "roomname": "<PERSON><PERSON><PERSON> be a megbeszélés azonosítóját"}, "addMeetingName": "Adja hozzá az értekezlet nevét", "appDescription": "<PERSON><PERSON><PERSON><PERSON>, videocsevegjen az egész csapattal. Valójában mindenkit hívjon meg, akit ismer. Az {{app}} egy tel<PERSON><PERSON> tit<PERSON>, 100%-ban nyílt forráskódú videokonferencia-megoldás, amelyet egész nap, minden nap ingyenesen használhat – fiók nélkül.", "audioVideoSwitch": {"audio": "Hang", "video": "<PERSON><PERSON><PERSON>"}, "calendar": "<PERSON><PERSON><PERSON><PERSON>", "connectCalendarButton": "Csatlakoztassa a naptárát", "connectCalendarText": "Csatlakoztassa naptárát az összes értekezlet megtekintéséhez az {{app}} alkalmazásban. Ezenkívül adjon hozzá {{provider}} találkozót a naptárához, és indítsa el őket egyetlen kattintással.", "developerPlan": "Fejlesztői terv", "enterRoomTitle": "Indítson új találkozót vagy adja meg a meglévő szoba nevét", "enterprisePlan": "Vállalati terv", "enterpriseSelfHostPlan": "Enterprise Self Host terv", "features": "Jellemzők", "footer": {"allRightsReserved": "Minden jog fenntartva", "androidAppDownload": "Android alkalmazás letöltése", "apiDocumentation": "API dokumentáció", "app": "Alkalmazás", "blog": "Blog", "company": "<PERSON><PERSON><PERSON><PERSON>", "contact": "Érintkezés", "copyright": "<PERSON><PERSON><PERSON><PERSON><PERSON> jog", "copyrightText": "Copyright 2020 – 2024 Találkozni órával LLC. Minden jog fenntartva", "developers": "Fejlesztők", "disclaimer": "<PERSON><PERSON>", "download": "Letöltés", "email": "Email", "faqs": "GYIK", "followUs": "Kövess minket", "helpDesk": "Ügyfélszolgálat", "home": "<PERSON><PERSON><PERSON>", "iOSAppDownload": "iOS alkalmazás letöltése", "inTheNews": "A Hírekben", "integrations": "Integrációk", "knowledgeBase": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "meethour": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "meethourLLC": "Ismerje meg a Hour LLC-t", "officeAddress": "8825 Stanford, Suite 205 Columbia, MD 21045", "phone": "Telefon", "privacyPolicy": "Adatvé<PERSON><PERSON>", "productPresentation": "Termékbemutató", "refundCancellationPolicy": "Visszatérítési és lemondási feltételek", "termsConditions": "Általános Szerződési Feltételek", "testimonials": "Beszámolók", "webMobileSDK": "Web és mobil SDK", "whoAreYou": "Ki vagy te"}, "forHospitals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "freeBannerDescription": "Ingyenes és korlátlan HD minőségű videokonferencia, mint még soha. Bárhonnan csatlakozhat a találkozóhoz.", "freePlan": "Ingyenes terv", "getHelp": "GYIK", "go": "Hozzon létre értekezletet vagy c<PERSON> hozz<PERSON>", "goSmall": "Hozzon létre értekezletet vagy c<PERSON> hozz<PERSON>", "header": {"accelerateDevelopmentWithPrebuiltSDKs": "Gyorsítsa fel a fejlesztést előre beépített SDK-kkal.", "apiStatus": "API állapota", "appointmentSchedulingVideoConference": "Időpont-ütemezés és videokonferencia.", "blog": "Blog", "customIntegrationDedicatedSupport": "Egyedi integráció és dedikált támogatás", "customTailoredVideoMeetings": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON> videotalálkozók.", "developer": "Fejlesztő", "developers": "Fejlesztők", "documentation": "Do<PERSON>ment<PERSON><PERSON>ó", "eMail": "Email", "edTech": "Edtech", "engagingOnlineLearningForEducators": "Lebilincselő online tanulás oktatók számára.", "engagingVirtualEventExperiences": "Lebilincselő virtuális események élményei.", "enterprise": "V<PERSON><PERSON>lk<PERSON><PERSON>", "enterpriseSelfHost": "Enterprise Self Host", "features": "Jellemzők", "fitness": "Megfelelőség", "free": "Ingyenes", "fundraiseEffortlesslyWithinVideoConferences": "Adománygyűjtés könnyedén videokonferenciákon.", "fundraisingDonate": "Adománygyűjtés / adományozás", "fundraisingDonateOnline": "Adománygyűjtés / Online adományozás", "getStarted": "Kezdje el", "hdQualityVideoConferenceApp": "HD minőségű videokonferencia alkalmazás", "help": "Seg<PERSON><PERSON><PERSON>g", "helpDesk": "S<PERSON>g<PERSON><PERSON><PERSON><PERSON>", "highQualityLiveEventStreaming": "<PERSON><PERSON><PERSON><PERSON><PERSON> minőségű élő esemény közvetítés.", "hostVideoConferenceOnYourServers": "Szervezzen videokonferenciát a szerverein.", "industries": "Iparágak", "integrateVideoCallWithinYourWebsiteApp": "Integrálja a videohívást webhelyén/alkalmazásán belül.", "interactiveVirtualLearningSolutions": "Interaktív virtuális tanulási megoldások.", "joinAMeeting": "Csatlakozzon egy értekezlethez", "knowledgeBase": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "liveStreaming": "<PERSON><PERSON><PERSON>vet<PERSON>", "meethour": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "myCaly": "<PERSON>z <PERSON>n <PERSON>m", "myCalyPricing": "MyCaly árképzés.", "mycaly": "Mycaly", "noAdsRecordingLiveStreaming": "Nincsenek hirdetések + felvétel + élő közvetítés.", "noTimeLimitGroupCalls": "Nincs időkorlát az 1:1-es és csoportos hívásoknál.", "preBuiltSDKs": "Előre elkészített SDK-k", "pricing": "Árképzés", "pro": "<PERSON>i", "products": "Termékek", "resources": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scheduleADemo": "Ütemezzen be egy bemu<PERSON>t", "simplifiedAPIReferences": "Egyszerűsített API-referenciák", "smoothVideoOnboardingExperience": "Si<PERSON> videó bevezetési élmény.", "solutions": "<PERSON><PERSON><PERSON><PERSON>", "stayuptodateWithOurBlog": "Legyen naprakész blogunkkal", "systemHealthStatusandUpdates": "A rendszer állapota és frissítései", "tailoredSolutionsForYourHealthcareNeeds": "Testre szabott megoldások egészségügyi igényeire.", "telehealth": "Távegészségügy", "useCases": "Használati esetek", "videoConference": "Videokonferencia", "videoConferencePlans": "Videokonferencia tervek", "videoConferencePricing": "Videokonferencia díjszabása.", "videoConferencing": "Videokonferencia", "videoKYC": "Videó KYC", "virtualClassrooms": "Virtu<PERSON><PERSON>", "virtualEvents": "Virtu<PERSON><PERSON>", "virtualSolutionForHomeFitness": "Virtu<PERSON><PERSON> me<PERSON> otthoni fit<PERSON>.", "webinarSessionsWithIndustryLeaders": "Webináriumok az iparág vezetőivel.", "webinars": "Webináriumok"}, "headerSubtitle": "Biztonságos és HD minőségű találkozók", "headerTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "invalidMeetingID": "Érvénytelen megbeszélésazonosító", "jitsiOnMobile": "Találkozni órával mobilon – töltse le alkalmazásainkat, és kezdjen megbeszélé<PERSON> b<PERSON><PERSON><PERSON><PERSON>", "join": "LÉTREHOZÁS / CSATLAKOZÁS", "joinAMeeting": "Csatlakozzon egy értekezlethez", "logo": {"calendar": "<PERSON><PERSON><PERSON><PERSON>", "desktopPreviewThumbnail": "<PERSON>z<PERSON>i előnézeti miniatűr", "googleLogo": "Google logó", "logoDeepLinking": "Jitsi meet log<PERSON>", "microsoftLogo": "Microsoft logó", "policyLogo": "Politika logója"}, "meetingDate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "meetingDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "meetingIsReady": "A találkozó készen áll", "mobileDownLoadLinkAndroid": "Mobil alkalmazás letöltése Androidra", "mobileDownLoadLinkFDroid": "Töltse le az F-Droid mobilalkalmazást", "mobileDownLoadLinkIos": "Mobil alkalmazás letöltése iOS-re", "moderatedMessage": "Vagy <a href=\"{{url}}\" rel=\"noopener noreferrer\" target=\"_blank\">foglaljon le egy találkozó URL-jét</a> <PERSON><PERSON><PERSON>, ahol Ön az egyetlen moderátor.", "oopsDeviceClockorTimezoneErr": "Hoppá! Valami elromlott. Győződjön meg arról, hogy az eszköz órája/időzónája pontos", "oopsThereSeemsToBeProblem": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> t<PERSON>, hogy prob<PERSON><PERSON> van", "pleaseWaitForTheStartMeeting": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, v<PERSON><PERSON><PERSON> meg, amíg a {{moderator}} elind<PERSON><PERSON><PERSON> ezt a megbeszélést.", "preRegistrationMsg": "A találkozóhoz előzetes regisztráció szükséges. Regisztrálja magát a böngészőben, és csatlakozzon vissza a meghívott e-mail linkről.", "pricing": "Árképzés", "privacy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "privateMeetingErr": "<PERSON><PERSON>ű<PERSON>, hogy egy privát találkozóhoz csatlakozik. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, csatlakozzon a meghívott e-mail cím has<PERSON>.", "proPlan": "proPlan", "recentList": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recentListDelete": "Bejegyzés törlése", "recentListEmpty": "A legutóbbi listája jelenleg üres. Csevegés a csapattal, és itt megtalálja az összes legutóbbi találkozóját.", "reducedUIText": "Üdvözöljük az {{app}} szolgáltatásban!", "registerNow": "Regisztrá<PERSON><PERSON> most", "roomNameAllowedChars": "A találkozó neve nem tartalmazhatja a következő karaktereket: ?, &, :, ', \", %, #.", "roomname": "<PERSON><PERSON><PERSON> be a megbeszélés azonosítóját", "roomnameHint": "Adja meg annak a helyiségnek a nevét vagy U<PERSON>-j<PERSON>t, am<PERSON><PERSON>z csatlakozni szeretne. Kitalálhat egy nevet, csak hagyja, hogy az ismerősök tudják, hogy ugyanazt a nevet írják be.", "scheduleAMeeting": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sendFeedback": "Visszajelzés küldése", "shiftingVirtualMeetToReality": "A virtuális ta<PERSON><PERSON><PERSON><PERSON>ó áttérés a valóságba", "solutions": "<PERSON><PERSON><PERSON><PERSON>", "startMeeting": "Kezdje meg a találkozót", "terms": "Feltételek", "timezone": "Id<PERSON><PERSON><PERSON><PERSON>", "title": "100%-ban ingyenes, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, végpontokig titkosított és HD minőségű videokonferencia-megoldás", "tryNowItsFree": "Próbá<PERSON><PERSON> ki <PERSON>, ingyenes", "waitingInLobby": "Várakozás az előcsarnokban. {{moderator}} hamarosan beenged.", "youtubeHelpTutorial": "YouTube Súgó-oktatóanyagok"}}