// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 60;
	objects = {

/* Begin PBXBuildFile section */
		0B412F181EDEC65D00B1A0A6 /* MeetHourView.h in Headers */ = {isa = PBXBuildFile; fileRef = 0B412F161EDEC65D00B1A0A6 /* MeetHourView.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0B412F191EDEC65D00B1A0A6 /* MeetHourView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0B412F171EDEC65D00B1A0A6 /* MeetHourView.m */; };
		0B412F221EDEF6EA00B1A0A6 /* MeetHourViewDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = 0B412F1B1EDEC80100B1A0A6 /* MeetHourViewDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0B49424520AD8DBD00BD2DE0 /* outgoingStart.wav in Resources */ = {isa = PBXBuildFile; fileRef = 0B49424320AD8DBD00BD2DE0 /* outgoingStart.wav */; };
		0B49424620AD8DBD00BD2DE0 /* outgoingRinging.wav in Resources */ = {isa = PBXBuildFile; fileRef = 0B49424420AD8DBD00BD2DE0 /* outgoingRinging.wav */; };
		0B93EF7E1EC9DDCD0030D24D /* RCTBridgeWrapper.h in Headers */ = {isa = PBXBuildFile; fileRef = 0B93EF7C1EC9DDCD0030D24D /* RCTBridgeWrapper.h */; };
		0B93EF7F1EC9DDCD0030D24D /* RCTBridgeWrapper.m in Sources */ = {isa = PBXBuildFile; fileRef = 0B93EF7D1EC9DDCD0030D24D /* RCTBridgeWrapper.m */; };
		0BA13D311EE83FF8007BEF7F /* ExternalAPI.m in Sources */ = {isa = PBXBuildFile; fileRef = 0BA13D301EE83FF8007BEF7F /* ExternalAPI.m */; };
		0BB9AD771F5EC6CE001C08DB /* CallKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0BB9AD761F5EC6CE001C08DB /* CallKit.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		0BB9AD791F5EC6D7001C08DB /* Intents.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0BB9AD781F5EC6D7001C08DB /* Intents.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		0BB9AD7B1F5EC8F4001C08DB /* CallKit.m in Sources */ = {isa = PBXBuildFile; fileRef = 0BB9AD7A1F5EC8F4001C08DB /* CallKit.m */; };
		0BB9AD7D1F60356D001C08DB /* AppInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 0BB9AD7C1F60356D001C08DB /* AppInfo.m */; };
		0BC4B8691F8C03A700CE8B21 /* CallKitIcon.png in Resources */ = {isa = PBXBuildFile; fileRef = 0BC4B8681F8C01E100CE8B21 /* CallKitIcon.png */; };
		0BCA495F1EC4B6C600B793EE /* AudioMode.m in Sources */ = {isa = PBXBuildFile; fileRef = 0BCA495C1EC4B6C600B793EE /* AudioMode.m */; };
		0BCA49601EC4B6C600B793EE /* POSIX.m in Sources */ = {isa = PBXBuildFile; fileRef = 0BCA495D1EC4B6C600B793EE /* POSIX.m */; };
		0BCA49611EC4B6C600B793EE /* Proximity.m in Sources */ = {isa = PBXBuildFile; fileRef = 0BCA495E1EC4B6C600B793EE /* Proximity.m */; };
		0BD906EA1EC0C00300C8C18E /* MeetHour.h in Headers */ = {isa = PBXBuildFile; fileRef = 0BD906E81EC0C00300C8C18E /* MeetHour.h */; settings = {ATTRIBUTES = (Public, ); }; };
		19AD99C1264A8F7E00520DF4 /* join_meeting.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 19AD99C0264A8F7E00520DF4 /* join_meeting.mp3 */; };
		43EA585CE8A0DA3456F82114 /* libPods-MeetHourSDK.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 82F0745DB8C8BEE6028AD7D3 /* libPods-MeetHourSDK.a */; };
		4E51B76425E5345E0038575A /* ScheenshareEventEmiter.h in Headers */ = {isa = PBXBuildFile; fileRef = 4E51B76225E5345E0038575A /* ScheenshareEventEmiter.h */; };
		4E51B76525E5345E0038575A /* ScheenshareEventEmiter.m in Sources */ = {isa = PBXBuildFile; fileRef = 4E51B76325E5345E0038575A /* ScheenshareEventEmiter.m */; };
		698737C4266F859300AE0910 /* WebRTC.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 698737C3266F859300AE0910 /* WebRTC.xcframework */; };
		6C31EDC820C06D490089C899 /* recordingOn.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 6C31EDC720C06D490089C899 /* recordingOn.mp3 */; };
		6C31EDCA20C06D530089C899 /* recordingOff.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 6C31EDC920C06D530089C899 /* recordingOff.mp3 */; };
		75635B0A20751D6D00F29C9F /* joined.wav in Resources */ = {isa = PBXBuildFile; fileRef = 75635B0820751D6D00F29C9F /* joined.wav */; };
		75635B0B20751D6D00F29C9F /* left.wav in Resources */ = {isa = PBXBuildFile; fileRef = 75635B0920751D6D00F29C9F /* left.wav */; };
		84443AE32CF4CEC700885265 /* reactions-raised-hand.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 84443ADC2CF4CEC600885265 /* reactions-raised-hand.mp3 */; };
		84443AE42CF4CEC700885265 /* reactions-applause.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 84443ADD2CF4CEC700885265 /* reactions-applause.mp3 */; };
		84443AE52CF4CEC700885265 /* reactions-boo.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 84443ADE2CF4CEC700885265 /* reactions-boo.mp3 */; };
		84443AE62CF4CEC700885265 /* reactions-surprise.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 84443ADF2CF4CEC700885265 /* reactions-surprise.mp3 */; };
		84443AE72CF4CEC700885265 /* reactions-crickets.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 84443AE02CF4CEC700885265 /* reactions-crickets.mp3 */; };
		84443AE82CF4CEC700885265 /* reactions-thumbs-up.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 84443AE12CF4CEC700885265 /* reactions-thumbs-up.mp3 */; };
		84443AE92CF4CEC700885265 /* reactions-laughter.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 84443AE22CF4CEC700885265 /* reactions-laughter.mp3 */; };
		8493A81F2DC12EA000D56B4A /* GoogleMobileAds.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8493A81E2DC12EA000D56B4A /* GoogleMobileAds.xcframework */; };
		8493A8202DC12EA000D56B4A /* GoogleMobileAds.xcframework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 8493A81E2DC12EA000D56B4A /* GoogleMobileAds.xcframework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		8618E94C28929C1900B610FD /* meet-hour-waiting.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 8618E94B28929C1900B610FD /* meet-hour-waiting.mp3 */; };
		87FE6F3321E52437004A5DC7 /* incomingMessage.wav in Resources */ = {isa = PBXBuildFile; fileRef = 87FE6F3221E52437004A5DC7 /* incomingMessage.wav */; };
		A4414AE020B37F1A003546E6 /* rejected.wav in Resources */ = {isa = PBXBuildFile; fileRef = A4414ADF20B37F1A003546E6 /* rejected.wav */; };
		A4A934E9212F3ADB001E9388 /* Dropbox.m in Sources */ = {isa = PBXBuildFile; fileRef = A4A934E8212F3ADB001E9388 /* Dropbox.m */; };
		C30F88D0CB0F4F5593216D24 /* liveStreamingOff.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = C30F88D1CB0F4F5593216D24 /* liveStreamingOff.mp3 */; };
		C30F88D2CB0F4F5593216D24 /* liveStreamingOn.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = C30F88D3CB0F4F5593216D24 /* liveStreamingOn.mp3 */; };
		C6245F5D2053091D0040BE68 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = C6245F5B2053091D0040BE68 /* <EMAIL> */; };
		C6245F5E2053091D0040BE68 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = C6245F5C2053091D0040BE68 /* <EMAIL> */; };
		C69EFA0C209A0F660027712B /* MHCallKitEmitter.swift in Sources */ = {isa = PBXBuildFile; fileRef = C69EFA09209A0F650027712B /* MHCallKitEmitter.swift */; };
		C69EFA0D209A0F660027712B /* MHCallKitProxy.swift in Sources */ = {isa = PBXBuildFile; fileRef = C69EFA0A209A0F660027712B /* MHCallKitProxy.swift */; };
		C69EFA0E209A0F660027712B /* MHCallKitListener.swift in Sources */ = {isa = PBXBuildFile; fileRef = C69EFA0B209A0F660027712B /* MHCallKitListener.swift */; };
		C6A34261204EF76800E062DD /* DragGestureController.swift in Sources */ = {isa = PBXBuildFile; fileRef = C6A3425E204EF76800E062DD /* DragGestureController.swift */; };
		C6CC49AF207412CF000DFA42 /* PiPViewCoordinator.swift in Sources */ = {isa = PBXBuildFile; fileRef = C6CC49AE207412CF000DFA42 /* PiPViewCoordinator.swift */; };
		C81E9AB925AC5AD800B134D9 /* ExternalAPI.h in Headers */ = {isa = PBXBuildFile; fileRef = C81E9AB825AC5AD800B134D9 /* ExternalAPI.h */; };
		C8AFD27F2462C613000293D2 /* InfoPlistUtil.h in Headers */ = {isa = PBXBuildFile; fileRef = C8AFD27D2462C613000293D2 /* InfoPlistUtil.h */; settings = {ATTRIBUTES = (Public, ); }; };
		C8AFD2802462C613000293D2 /* InfoPlistUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = C8AFD27E2462C613000293D2 /* InfoPlistUtil.m */; };
		DE438CDA2350934700DD541D /* JavaScriptSandbox.m in Sources */ = {isa = PBXBuildFile; fileRef = DE438CD82350934700DD541D /* JavaScriptSandbox.m */; };
		DE65AACA2317FFCD00290BEC /* LogUtils.h in Headers */ = {isa = PBXBuildFile; fileRef = DE65AAC92317FFCD00290BEC /* LogUtils.h */; };
		DE65AACC2318028300290BEC /* MeetHourBaseLogHandler+Private.h in Headers */ = {isa = PBXBuildFile; fileRef = DE65AACB2318028300290BEC /* MeetHourBaseLogHandler+Private.h */; };
		DE762DB422AFDE76000DEBD6 /* MeetHourUserInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = DE762DB322AFDE76000DEBD6 /* MeetHourUserInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DE762DB622AFDE8D000DEBD6 /* MeetHourUserInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = DE762DB522AFDE8D000DEBD6 /* MeetHourUserInfo.m */; };
		DE81A2D42316AC4D00AE1940 /* MeetHourLogger.h in Headers */ = {isa = PBXBuildFile; fileRef = DE81A2D22316AC4D00AE1940 /* MeetHourLogger.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DE81A2D52316AC4D00AE1940 /* MeetHourLogger.m in Sources */ = {isa = PBXBuildFile; fileRef = DE81A2D32316AC4D00AE1940 /* MeetHourLogger.m */; };
		DE81A2D92316AC7600AE1940 /* LogBridge.m in Sources */ = {isa = PBXBuildFile; fileRef = DE81A2D72316AC7600AE1940 /* LogBridge.m */; };
		DE81A2DE2317ED5400AE1940 /* MeetHourBaseLogHandler.h in Headers */ = {isa = PBXBuildFile; fileRef = DE81A2DC2317ED5400AE1940 /* MeetHourBaseLogHandler.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DE81A2DF2317ED5400AE1940 /* MeetHourBaseLogHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = DE81A2DD2317ED5400AE1940 /* MeetHourBaseLogHandler.m */; };
		DEA9F284258A5D9900D4CD74 /* MeetHourSDK.h in Headers */ = {isa = PBXBuildFile; fileRef = DEA9F283258A5D9900D4CD74 /* MeetHourSDK.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DEAD3226220C497000E93636 /* MeetHourConferenceOptions.h in Headers */ = {isa = PBXBuildFile; fileRef = DEAD3224220C497000E93636 /* MeetHourConferenceOptions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		DEAD3227220C497000E93636 /* MeetHourConferenceOptions.m in Sources */ = {isa = PBXBuildFile; fileRef = DEAD3225220C497000E93636 /* MeetHourConferenceOptions.m */; };
		DEAFA779229EAD520033A7FA /* RNRootView.m in Sources */ = {isa = PBXBuildFile; fileRef = DEAFA778229EAD520033A7FA /* RNRootView.m */; };
		DEFC743F21B178FA00E4DD96 /* LocaleDetector.m in Sources */ = {isa = PBXBuildFile; fileRef = DEFC743D21B178FA00E4DD96 /* LocaleDetector.m */; };
		DEFE535421FB1BF800011A3A /* MeetHour.m in Sources */ = {isa = PBXBuildFile; fileRef = DEFE535321FB1BF800011A3A /* MeetHour.m */; };
		DEFE535621FB2E8300011A3A /* ReactUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = DEFE535521FB2E8300011A3A /* ReactUtils.m */; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		8493A8212DC12EA000D56B4A /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				8493A8202DC12EA000D56B4A /* GoogleMobileAds.xcframework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		09A78016288AF50ACD28A10D /* Pods-MeetHourSDK.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-MeetHourSDK.debug.xcconfig"; path = "../Pods/Target Support Files/Pods-MeetHourSDK/Pods-MeetHourSDK.debug.xcconfig"; sourceTree = "<group>"; };
		0B412F161EDEC65D00B1A0A6 /* MeetHourView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MeetHourView.h; sourceTree = "<group>"; };
		0B412F171EDEC65D00B1A0A6 /* MeetHourView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MeetHourView.m; sourceTree = "<group>"; };
		0B412F1B1EDEC80100B1A0A6 /* MeetHourViewDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MeetHourViewDelegate.h; sourceTree = "<group>"; };
		0B49424320AD8DBD00BD2DE0 /* outgoingStart.wav */ = {isa = PBXFileReference; lastKnownFileType = audio.wav; name = outgoingStart.wav; path = ../../sounds/outgoingStart.wav; sourceTree = "<group>"; };
		0B49424420AD8DBD00BD2DE0 /* outgoingRinging.wav */ = {isa = PBXFileReference; lastKnownFileType = audio.wav; name = outgoingRinging.wav; path = ../../sounds/outgoingRinging.wav; sourceTree = "<group>"; };
		0B93EF7A1EC608550030D24D /* CoreText.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreText.framework; path = System/Library/Frameworks/CoreText.framework; sourceTree = SDKROOT; };
		0B93EF7C1EC9DDCD0030D24D /* RCTBridgeWrapper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RCTBridgeWrapper.h; sourceTree = "<group>"; };
		0B93EF7D1EC9DDCD0030D24D /* RCTBridgeWrapper.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RCTBridgeWrapper.m; sourceTree = "<group>"; };
		0BA13D301EE83FF8007BEF7F /* ExternalAPI.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ExternalAPI.m; sourceTree = "<group>"; };
		0BB9AD761F5EC6CE001C08DB /* CallKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CallKit.framework; path = System/Library/Frameworks/CallKit.framework; sourceTree = SDKROOT; };
		0BB9AD781F5EC6D7001C08DB /* Intents.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Intents.framework; path = System/Library/Frameworks/Intents.framework; sourceTree = SDKROOT; };
		0BB9AD7A1F5EC8F4001C08DB /* CallKit.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CallKit.m; sourceTree = "<group>"; };
		0BB9AD7C1F60356D001C08DB /* AppInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AppInfo.m; sourceTree = "<group>"; };
		0BC4B8681F8C01E100CE8B21 /* CallKitIcon.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; name = CallKitIcon.png; path = "../../react/features/mobile/call-integration/CallKitIcon.png"; sourceTree = "<group>"; };
		0BCA495C1EC4B6C600B793EE /* AudioMode.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AudioMode.m; sourceTree = "<group>"; };
		0BCA495D1EC4B6C600B793EE /* POSIX.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = POSIX.m; sourceTree = "<group>"; };
		0BCA495E1EC4B6C600B793EE /* Proximity.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = Proximity.m; sourceTree = "<group>"; };
		0BD906E51EC0C00300C8C18E /* MeetHourSDK.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = MeetHourSDK.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		0BD906E81EC0C00300C8C18E /* MeetHour.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MeetHour.h; sourceTree = "<group>"; };
		0BD906E91EC0C00300C8C18E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		19AD99C0264A8F7E00520DF4 /* join_meeting.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; name = join_meeting.mp3; path = ../../sounds/join_meeting.mp3; sourceTree = "<group>"; };
		423D68AC746BF3CEEEC06A8A /* Pods-MeetHourSDK.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-MeetHourSDK.release.xcconfig"; path = "../Pods/Target Support Files/Pods-MeetHourSDK/Pods-MeetHourSDK.release.xcconfig"; sourceTree = "<group>"; };
		4E51B76225E5345E0038575A /* ScheenshareEventEmiter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ScheenshareEventEmiter.h; sourceTree = "<group>"; };
		4E51B76325E5345E0038575A /* ScheenshareEventEmiter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ScheenshareEventEmiter.m; sourceTree = "<group>"; };
		698737C3266F859300AE0910 /* WebRTC.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; name = WebRTC.xcframework; path = "../../node_modules/react-native-webrtc/apple/WebRTC.xcframework"; sourceTree = "<group>"; };
		6C31EDC720C06D490089C899 /* recordingOn.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; name = recordingOn.mp3; path = ../../sounds/recordingOn.mp3; sourceTree = "<group>"; };
		6C31EDC920C06D530089C899 /* recordingOff.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; name = recordingOff.mp3; path = ../../sounds/recordingOff.mp3; sourceTree = "<group>"; };
		75635B0820751D6D00F29C9F /* joined.wav */ = {isa = PBXFileReference; lastKnownFileType = audio.wav; name = joined.wav; path = ../../sounds/joined.wav; sourceTree = "<group>"; };
		75635B0920751D6D00F29C9F /* left.wav */ = {isa = PBXFileReference; lastKnownFileType = audio.wav; name = left.wav; path = ../../sounds/left.wav; sourceTree = "<group>"; };
		82F0745DB8C8BEE6028AD7D3 /* libPods-MeetHourSDK.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-MeetHourSDK.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		842EDC062DC11FB100F7B1C7 /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
		84443ADC2CF4CEC600885265 /* reactions-raised-hand.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; name = "reactions-raised-hand.mp3"; path = "../../sounds/reactions-raised-hand.mp3"; sourceTree = "<group>"; };
		84443ADD2CF4CEC700885265 /* reactions-applause.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; name = "reactions-applause.mp3"; path = "../../sounds/reactions-applause.mp3"; sourceTree = "<group>"; };
		84443ADE2CF4CEC700885265 /* reactions-boo.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; name = "reactions-boo.mp3"; path = "../../sounds/reactions-boo.mp3"; sourceTree = "<group>"; };
		84443ADF2CF4CEC700885265 /* reactions-surprise.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; name = "reactions-surprise.mp3"; path = "../../sounds/reactions-surprise.mp3"; sourceTree = "<group>"; };
		84443AE02CF4CEC700885265 /* reactions-crickets.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; name = "reactions-crickets.mp3"; path = "../../sounds/reactions-crickets.mp3"; sourceTree = "<group>"; };
		84443AE12CF4CEC700885265 /* reactions-thumbs-up.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; name = "reactions-thumbs-up.mp3"; path = "../../sounds/reactions-thumbs-up.mp3"; sourceTree = "<group>"; };
		84443AE22CF4CEC700885265 /* reactions-laughter.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; name = "reactions-laughter.mp3"; path = "../../sounds/reactions-laughter.mp3"; sourceTree = "<group>"; };
		8493A81E2DC12EA000D56B4A /* GoogleMobileAds.xcframework */ = {isa = PBXFileReference; expectedSignature = "AppleDeveloperProgram:EQHXZ8M8AV:Google LLC"; lastKnownFileType = wrapper.xcframework; name = GoogleMobileAds.xcframework; path = "../Pods/Google-Mobile-Ads-SDK/Frameworks/GoogleMobileAdsFramework/GoogleMobileAds.xcframework"; sourceTree = "<group>"; };
		8618E94B28929C1900B610FD /* meet-hour-waiting.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; name = "meet-hour-waiting.mp3"; path = "../../sounds/meet-hour-waiting.mp3"; sourceTree = "<group>"; };
		87FE6F3221E52437004A5DC7 /* incomingMessage.wav */ = {isa = PBXFileReference; lastKnownFileType = audio.wav; name = incomingMessage.wav; path = ../../sounds/incomingMessage.wav; sourceTree = "<group>"; };
		891FE43DAD30BC8976683100 /* Pods-MeetHourSDK.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-MeetHourSDK.release.xcconfig"; path = "../Pods/Target Support Files/Pods-MeetHourSDK/Pods-MeetHourSDK.release.xcconfig"; sourceTree = "<group>"; };
		98E09B5C73D9036B4ED252FC /* Pods-MeetHour.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-MeetHour.debug.xcconfig"; path = "../Pods/Target Support Files/Pods-MeetHour/Pods-MeetHour.debug.xcconfig"; sourceTree = "<group>"; };
		9C77CA3CC919B081F1A52982 /* Pods-MeetHour.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-MeetHour.release.xcconfig"; path = "../Pods/Target Support Files/Pods-MeetHour/Pods-MeetHour.release.xcconfig"; sourceTree = "<group>"; };
		A4414ADF20B37F1A003546E6 /* rejected.wav */ = {isa = PBXFileReference; lastKnownFileType = audio.wav; name = rejected.wav; path = ../../sounds/rejected.wav; sourceTree = "<group>"; };
		A4A934E8212F3ADB001E9388 /* Dropbox.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = Dropbox.m; sourceTree = "<group>"; };
		A4A934EB21349A06001E9388 /* Dropbox.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = Dropbox.h; sourceTree = "<group>"; };
		C30F88D1CB0F4F5593216D24 /* liveStreamingOff.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; name = liveStreamingOff.mp3; path = ../../sounds/liveStreamingOff.mp3; sourceTree = "<group>"; };
		C30F88D3CB0F4F5593216D24 /* liveStreamingOn.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; name = liveStreamingOn.mp3; path = ../../sounds/liveStreamingOn.mp3; sourceTree = "<group>"; };
		C6245F5B2053091D0040BE68 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; name = "<EMAIL>"; path = "src/picture-in-picture/<EMAIL>"; sourceTree = "<group>"; };
		C6245F5C2053091D0040BE68 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; name = "<EMAIL>"; path = "src/picture-in-picture/<EMAIL>"; sourceTree = "<group>"; };
		C69EFA09209A0F650027712B /* MHCallKitEmitter.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MHCallKitEmitter.swift; sourceTree = "<group>"; };
		C69EFA0A209A0F660027712B /* MHCallKitProxy.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MHCallKitProxy.swift; sourceTree = "<group>"; };
		C69EFA0B209A0F660027712B /* MHCallKitListener.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MHCallKitListener.swift; sourceTree = "<group>"; };
		C6A3425E204EF76800E062DD /* DragGestureController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DragGestureController.swift; sourceTree = "<group>"; };
		C6CC49AE207412CF000DFA42 /* PiPViewCoordinator.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PiPViewCoordinator.swift; sourceTree = "<group>"; };
		C6F99C13204DB63D0001F710 /* MeetHourView+Private.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "MeetHourView+Private.h"; sourceTree = "<group>"; };
		C81E9AB825AC5AD800B134D9 /* ExternalAPI.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ExternalAPI.h; sourceTree = "<group>"; };
		C8AFD27D2462C613000293D2 /* InfoPlistUtil.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = InfoPlistUtil.h; sourceTree = "<group>"; };
		C8AFD27E2462C613000293D2 /* InfoPlistUtil.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = InfoPlistUtil.m; sourceTree = "<group>"; };
		D66E2DEBF7DF54B1C5F4D312 /* Pods-MeetHourSDK.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-MeetHourSDK.debug.xcconfig"; path = "../Pods/Target Support Files/Pods-MeetHourSDK/Pods-MeetHourSDK.debug.xcconfig"; sourceTree = "<group>"; };
		DE438CD82350934700DD541D /* JavaScriptSandbox.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = JavaScriptSandbox.m; sourceTree = "<group>"; };
		DE65AAC92317FFCD00290BEC /* LogUtils.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = LogUtils.h; sourceTree = "<group>"; };
		DE65AACB2318028300290BEC /* MeetHourBaseLogHandler+Private.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "MeetHourBaseLogHandler+Private.h"; sourceTree = "<group>"; };
		DE762DB322AFDE76000DEBD6 /* MeetHourUserInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MeetHourUserInfo.h; sourceTree = "<group>"; };
		DE762DB522AFDE8D000DEBD6 /* MeetHourUserInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MeetHourUserInfo.m; sourceTree = "<group>"; };
		DE762DB722AFE166000DEBD6 /* MeetHourUserInfo+Private.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "MeetHourUserInfo+Private.h"; sourceTree = "<group>"; };
		DE81A2D22316AC4D00AE1940 /* MeetHourLogger.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MeetHourLogger.h; sourceTree = "<group>"; };
		DE81A2D32316AC4D00AE1940 /* MeetHourLogger.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MeetHourLogger.m; sourceTree = "<group>"; };
		DE81A2D72316AC7600AE1940 /* LogBridge.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = LogBridge.m; sourceTree = "<group>"; };
		DE81A2DC2317ED5400AE1940 /* MeetHourBaseLogHandler.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MeetHourBaseLogHandler.h; sourceTree = "<group>"; };
		DE81A2DD2317ED5400AE1940 /* MeetHourBaseLogHandler.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MeetHourBaseLogHandler.m; sourceTree = "<group>"; };
		DEA9F283258A5D9900D4CD74 /* MeetHourSDK.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MeetHourSDK.h; sourceTree = "<group>"; };
		DEAD3224220C497000E93636 /* MeetHourConferenceOptions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MeetHourConferenceOptions.h; sourceTree = "<group>"; };
		DEAD3225220C497000E93636 /* MeetHourConferenceOptions.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MeetHourConferenceOptions.m; sourceTree = "<group>"; };
		DEAD3228220C734300E93636 /* MeetHourConferenceOptions+Private.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "MeetHourConferenceOptions+Private.h"; sourceTree = "<group>"; };
		DEAFA777229EAD3B0033A7FA /* RNRootView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RNRootView.h; sourceTree = "<group>"; };
		DEAFA778229EAD520033A7FA /* RNRootView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RNRootView.m; sourceTree = "<group>"; };
		DEFC743D21B178FA00E4DD96 /* LocaleDetector.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = LocaleDetector.m; sourceTree = "<group>"; };
		DEFE535321FB1BF800011A3A /* MeetHour.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MeetHour.m; sourceTree = "<group>"; };
		DEFE535521FB2E8300011A3A /* ReactUtils.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ReactUtils.m; sourceTree = "<group>"; };
		DEFE535721FB2E9E00011A3A /* ReactUtils.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ReactUtils.h; sourceTree = "<group>"; };
		DEFE535821FB311F00011A3A /* MeetHour+Private.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "MeetHour+Private.h"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		0BD906E11EC0C00300C8C18E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				698737C4266F859300AE0910 /* WebRTC.xcframework in Frameworks */,
				8493A81F2DC12EA000D56B4A /* GoogleMobileAds.xcframework in Frameworks */,
				0BB9AD791F5EC6D7001C08DB /* Intents.framework in Frameworks */,
				0BB9AD771F5EC6CE001C08DB /* CallKit.framework in Frameworks */,
				43EA585CE8A0DA3456F82114 /* libPods-MeetHourSDK.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		0BCA49681EC4BBE500B793EE /* Resources */ = {
			isa = PBXGroup;
			children = (
				84443ADD2CF4CEC700885265 /* reactions-applause.mp3 */,
				84443ADE2CF4CEC700885265 /* reactions-boo.mp3 */,
				84443AE02CF4CEC700885265 /* reactions-crickets.mp3 */,
				84443AE22CF4CEC700885265 /* reactions-laughter.mp3 */,
				84443ADC2CF4CEC600885265 /* reactions-raised-hand.mp3 */,
				84443ADF2CF4CEC700885265 /* reactions-surprise.mp3 */,
				84443AE12CF4CEC700885265 /* reactions-thumbs-up.mp3 */,
				8618E94B28929C1900B610FD /* meet-hour-waiting.mp3 */,
				19AD99C0264A8F7E00520DF4 /* join_meeting.mp3 */,
				87FE6F3221E52437004A5DC7 /* incomingMessage.wav */,
				0BC4B8681F8C01E100CE8B21 /* CallKitIcon.png */,
				C6245F5B2053091D0040BE68 /* <EMAIL> */,
				C6245F5C2053091D0040BE68 /* <EMAIL> */,
				75635B0820751D6D00F29C9F /* joined.wav */,
				75635B0920751D6D00F29C9F /* left.wav */,
				C30F88D1CB0F4F5593216D24 /* liveStreamingOff.mp3 */,
				C30F88D3CB0F4F5593216D24 /* liveStreamingOn.mp3 */,
				0B49424420AD8DBD00BD2DE0 /* outgoingRinging.wav */,
				0B49424320AD8DBD00BD2DE0 /* outgoingStart.wav */,
				6C31EDC920C06D530089C899 /* recordingOff.mp3 */,
				6C31EDC720C06D490089C899 /* recordingOn.mp3 */,
				A4414ADF20B37F1A003546E6 /* rejected.wav */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		0BD906DB1EC0C00300C8C18E = {
			isa = PBXGroup;
			children = (
				9C3C6FA2341729836589B856 /* Frameworks */,
				C5E72ADFC30ED96F9B35F076 /* Pods */,
				0BD906E61EC0C00300C8C18E /* Products */,
				0BCA49681EC4BBE500B793EE /* Resources */,
				0BD906E71EC0C00300C8C18E /* src */,
			);
			sourceTree = "<group>";
		};
		0BD906E61EC0C00300C8C18E /* Products */ = {
			isa = PBXGroup;
			children = (
				0BD906E51EC0C00300C8C18E /* MeetHourSDK.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		0BD906E71EC0C00300C8C18E /* src */ = {
			isa = PBXGroup;
			children = (
				0BB9AD7C1F60356D001C08DB /* AppInfo.m */,
				0BCA495C1EC4B6C600B793EE /* AudioMode.m */,
				C69EFA02209A0EFD0027712B /* callkit */,
				A4A934E7212F3AB8001E9388 /* dropbox */,
				0BA13D301EE83FF8007BEF7F /* ExternalAPI.m */,
				0BD906E91EC0C00300C8C18E /* Info.plist */,
				DE438CD82350934700DD541D /* JavaScriptSandbox.m */,
				0BD906E81EC0C00300C8C18E /* MeetHour.h */,
				DEFE535821FB311F00011A3A /* MeetHour+Private.h */,
				DEA9F283258A5D9900D4CD74 /* MeetHourSDK.h */,
				DEFE535321FB1BF800011A3A /* MeetHour.m */,
				DEAD3224220C497000E93636 /* MeetHourConferenceOptions.h */,
				DEAD3228220C734300E93636 /* MeetHourConferenceOptions+Private.h */,
				DEAD3225220C497000E93636 /* MeetHourConferenceOptions.m */,
				DE762DB322AFDE76000DEBD6 /* MeetHourUserInfo.h */,
				DE762DB722AFE166000DEBD6 /* MeetHourUserInfo+Private.h */,
				DE762DB522AFDE8D000DEBD6 /* MeetHourUserInfo.m */,
				DE81A2D22316AC4D00AE1940 /* MeetHourLogger.h */,
				DE81A2D32316AC4D00AE1940 /* MeetHourLogger.m */,
				DE81A2DC2317ED5400AE1940 /* MeetHourBaseLogHandler.h */,
				DE65AACB2318028300290BEC /* MeetHourBaseLogHandler+Private.h */,
				DE81A2DD2317ED5400AE1940 /* MeetHourBaseLogHandler.m */,
				0B412F161EDEC65D00B1A0A6 /* MeetHourView.h */,
				0B412F171EDEC65D00B1A0A6 /* MeetHourView.m */,
				DE81A2D72316AC7600AE1940 /* LogBridge.m */,
				DE65AAC92317FFCD00290BEC /* LogUtils.h */,
				DEAFA777229EAD3B0033A7FA /* RNRootView.h */,
				DEAFA778229EAD520033A7FA /* RNRootView.m */,
				C6F99C13204DB63D0001F710 /* MeetHourView+Private.h */,
				0B412F1B1EDEC80100B1A0A6 /* MeetHourViewDelegate.h */,
				DEFC743D21B178FA00E4DD96 /* LocaleDetector.m */,
				C6A3426B204F127900E062DD /* picture-in-picture */,
				0BCA495D1EC4B6C600B793EE /* POSIX.m */,
				0BCA495E1EC4B6C600B793EE /* Proximity.m */,
				DEFE535721FB2E9E00011A3A /* ReactUtils.h */,
				DEFE535521FB2E8300011A3A /* ReactUtils.m */,
				0B93EF7C1EC9DDCD0030D24D /* RCTBridgeWrapper.h */,
				0B93EF7D1EC9DDCD0030D24D /* RCTBridgeWrapper.m */,
				C8AFD27D2462C613000293D2 /* InfoPlistUtil.h */,
				C8AFD27E2462C613000293D2 /* InfoPlistUtil.m */,
				C81E9AB825AC5AD800B134D9 /* ExternalAPI.h */,
				4E51B76225E5345E0038575A /* ScheenshareEventEmiter.h */,
				4E51B76325E5345E0038575A /* ScheenshareEventEmiter.m */,
			);
			path = src;
			sourceTree = "<group>";
		};
		9C3C6FA2341729836589B856 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				8493A81E2DC12EA000D56B4A /* GoogleMobileAds.xcframework */,
				842EDC062DC11FB100F7B1C7 /* JavaScriptCore.framework */,
				698737C3266F859300AE0910 /* WebRTC.xcframework */,
				0BB9AD761F5EC6CE001C08DB /* CallKit.framework */,
				0B93EF7A1EC608550030D24D /* CoreText.framework */,
				0BB9AD781F5EC6D7001C08DB /* Intents.framework */,
				82F0745DB8C8BEE6028AD7D3 /* libPods-MeetHourSDK.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		A4A934E7212F3AB8001E9388 /* dropbox */ = {
			isa = PBXGroup;
			children = (
				A4A934EB21349A06001E9388 /* Dropbox.h */,
				A4A934E8212F3ADB001E9388 /* Dropbox.m */,
			);
			path = dropbox;
			sourceTree = "<group>";
		};
		C5E72ADFC30ED96F9B35F076 /* Pods */ = {
			isa = PBXGroup;
			children = (
				98E09B5C73D9036B4ED252FC /* Pods-MeetHour.debug.xcconfig */,
				9C77CA3CC919B081F1A52982 /* Pods-MeetHour.release.xcconfig */,
				09A78016288AF50ACD28A10D /* Pods-MeetHourSDK.debug.xcconfig */,
				891FE43DAD30BC8976683100 /* Pods-MeetHourSDK.release.xcconfig */,
				D66E2DEBF7DF54B1C5F4D312 /* Pods-MeetHourSDK.debug.xcconfig */,
				423D68AC746BF3CEEEC06A8A /* Pods-MeetHourSDK.release.xcconfig */,
			);
			name = Pods;
			sourceTree = "<group>";
		};
		C69EFA02209A0EFD0027712B /* callkit */ = {
			isa = PBXGroup;
			children = (
				0BB9AD7A1F5EC8F4001C08DB /* CallKit.m */,
				C69EFA09209A0F650027712B /* MHCallKitEmitter.swift */,
				C69EFA0B209A0F660027712B /* MHCallKitListener.swift */,
				C69EFA0A209A0F660027712B /* MHCallKitProxy.swift */,
			);
			path = callkit;
			sourceTree = "<group>";
		};
		C6A3426B204F127900E062DD /* picture-in-picture */ = {
			isa = PBXGroup;
			children = (
				C6A3425E204EF76800E062DD /* DragGestureController.swift */,
				C6CC49AE207412CF000DFA42 /* PiPViewCoordinator.swift */,
			);
			path = "picture-in-picture";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		0BD906E21EC0C00300C8C18E /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				DE762DB422AFDE76000DEBD6 /* MeetHourUserInfo.h in Headers */,
				0B412F181EDEC65D00B1A0A6 /* MeetHourView.h in Headers */,
				0B93EF7E1EC9DDCD0030D24D /* RCTBridgeWrapper.h in Headers */,
				DE81A2DE2317ED5400AE1940 /* MeetHourBaseLogHandler.h in Headers */,
				DEA9F284258A5D9900D4CD74 /* MeetHourSDK.h in Headers */,
				4E51B76425E5345E0038575A /* ScheenshareEventEmiter.h in Headers */,
				DE65AACC2318028300290BEC /* MeetHourBaseLogHandler+Private.h in Headers */,
				0B412F221EDEF6EA00B1A0A6 /* MeetHourViewDelegate.h in Headers */,
				0BD906EA1EC0C00300C8C18E /* MeetHour.h in Headers */,
				DE81A2D42316AC4D00AE1940 /* MeetHourLogger.h in Headers */,
				DE65AACA2317FFCD00290BEC /* LogUtils.h in Headers */,
				DEAD3226220C497000E93636 /* MeetHourConferenceOptions.h in Headers */,
				C81E9AB925AC5AD800B134D9 /* ExternalAPI.h in Headers */,
				C8AFD27F2462C613000293D2 /* InfoPlistUtil.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		0BD906E41EC0C00300C8C18E /* MeetHourSDK */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0BD906ED1EC0C00300C8C18E /* Build configuration list for PBXNativeTarget "MeetHourSDK" */;
			buildPhases = (
				26796D8589142D80C8AFDA51 /* [CP] Check Pods Manifest.lock */,
				0BD906E01EC0C00300C8C18E /* Sources */,
				0BD906E11EC0C00300C8C18E /* Frameworks */,
				0BD906E21EC0C00300C8C18E /* Headers */,
				0BD906E31EC0C00300C8C18E /* Resources */,
				0BCA49651EC4B77500B793EE /* Package React bundle */,
				9ED23F30562DAEBE28204731 /* [CP] Copy Pods Resources */,
				DF855469CF87A5B9A8CCF4E9 /* [CP-User] [RNGoogleMobileAds] Configuration */,
				8493A8212DC12EA000D56B4A /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = MeetHourSDK;
			productName = "Meet Hour Meet SDK";
			productReference = 0BD906E51EC0C00300C8C18E /* MeetHourSDK.framework */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		0BD906DC1EC0C00300C8C18E /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1340;
				ORGANIZATIONNAME = MeetHour;
				TargetAttributes = {
					0BD906E41EC0C00300C8C18E = {
						CreatedOnToolsVersion = 8.3.2;
						LastSwiftMigration = 1010;
						ProvisioningStyle = Automatic;
					};
				};
			};
			buildConfigurationList = 0BD906DF1EC0C00300C8C18E /* Build configuration list for PBXProject "sdk" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 0BD906DB1EC0C00300C8C18E;
			productRefGroup = 0BD906E61EC0C00300C8C18E /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				0BD906E41EC0C00300C8C18E /* MeetHourSDK */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		0BD906E31EC0C00300C8C18E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				84443AE32CF4CEC700885265 /* reactions-raised-hand.mp3 in Resources */,
				84443AE42CF4CEC700885265 /* reactions-applause.mp3 in Resources */,
				84443AE52CF4CEC700885265 /* reactions-boo.mp3 in Resources */,
				84443AE62CF4CEC700885265 /* reactions-surprise.mp3 in Resources */,
				84443AE72CF4CEC700885265 /* reactions-crickets.mp3 in Resources */,
				84443AE82CF4CEC700885265 /* reactions-thumbs-up.mp3 in Resources */,
				84443AE92CF4CEC700885265 /* reactions-laughter.mp3 in Resources */,
				8618E94C28929C1900B610FD /* meet-hour-waiting.mp3 in Resources */,
				19AD99C1264A8F7E00520DF4 /* join_meeting.mp3 in Resources */,
				87FE6F3321E52437004A5DC7 /* incomingMessage.wav in Resources */,
				0B49424520AD8DBD00BD2DE0 /* outgoingStart.wav in Resources */,
				C30F88D0CB0F4F5593216D24 /* liveStreamingOff.mp3 in Resources */,
				C30F88D2CB0F4F5593216D24 /* liveStreamingOn.mp3 in Resources */,
				6C31EDCA20C06D530089C899 /* recordingOff.mp3 in Resources */,
				A4414AE020B37F1A003546E6 /* rejected.wav in Resources */,
				0B49424620AD8DBD00BD2DE0 /* outgoingRinging.wav in Resources */,
				C6245F5D2053091D0040BE68 /* <EMAIL> in Resources */,
				6C31EDC820C06D490089C899 /* recordingOn.mp3 in Resources */,
				0BC4B8691F8C03A700CE8B21 /* CallKitIcon.png in Resources */,
				75635B0B20751D6D00F29C9F /* left.wav in Resources */,
				75635B0A20751D6D00F29C9F /* joined.wav in Resources */,
				C6245F5E2053091D0040BE68 /* <EMAIL> in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		0BCA49651EC4B77500B793EE /* Package React bundle */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
			);
			name = "Package React bundle";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export NODE_BINARY=node\nexport NODE_ARGS=\"--max_old_space_size=4096\"\nexport ENTRY_FILE=\"${PROJECT_DIR}/../../index.ios.js\"\n\n../../node_modules/react-native/scripts/react-native-xcode.sh\n";
		};
		26796D8589142D80C8AFDA51 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-MeetHourSDK-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		9ED23F30562DAEBE28204731 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-MeetHourSDK/Pods-MeetHourSDK-resources.sh",
				"${PODS_CONFIGURATION_BUILD_DIR}/PromisesObjC/FBLPromises_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/AppAuth/AppAuthCore_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/GTMAppAuth/GTMAppAuth_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/GTMSessionFetcher/GTMSessionFetcher_Core_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/Google-Mobile-Ads-SDK/GoogleMobileAdsResources.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/GoogleSignIn/GoogleSignIn.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/GoogleUserMessagingPlatform/UserMessagingPlatformResources.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/PromisesSwift/Promises_Privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/RNCAsyncStorage/RNCAsyncStorage_resources.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/React-Core/RCTI18nStrings.bundle",
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/FBLPromises_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/AppAuthCore_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/GTMAppAuth_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/GTMSessionFetcher_Core_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/GoogleMobileAdsResources.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/GoogleSignIn.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/UserMessagingPlatformResources.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Promises_Privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/RNCAsyncStorage_resources.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/RCTI18nStrings.bundle",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-MeetHourSDK/Pods-MeetHourSDK-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		DF855469CF87A5B9A8CCF4E9 /* [CP-User] [RNGoogleMobileAds] Configuration */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			name = "[CP-User] [RNGoogleMobileAds] Configuration";
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/usr/bin/env bash\n#\n# Copyright (c) 2016-present Invertase Limited & Contributors\n#\n# Licensed under the Apache License, Version 2.0 (the \"License\");\n# you may not use this library except in compliance with the License.\n# You may obtain a copy of the License at\n#\n#   http://www.apache.org/licenses/LICENSE-2.0\n#\n# Unless required by applicable law or agreed to in writing, software\n# distributed under the License is distributed on an \"AS IS\" BASIS,\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n# See the License for the specific language governing permissions and\n# limitations under the License.\n#\n\n##########################################################################\n##########################################################################\n#\n#  NOTE THAT IF YOU CHANGE THIS FILE YOU MUST RUN pod install AFTERWARDS\n#\n#  This file is installed as an Xcode build script in the project file\n#  by cocoapods, and you will not see your changes until you pod install\n#\n##########################################################################\n##########################################################################\n\nset -e\n\n_MAX_LOOKUPS=2;\n_SEARCH_RESULT=''\n_RN_ROOT_EXISTS=''\n_CURRENT_LOOKUPS=1\n_PROJECT_ABBREVIATION=\"RNGoogleMobileAds\"\n_JSON_ROOT=\"'react-native-google-mobile-ads'\"\n_JSON_FILE_NAME='app.json'\n_JSON_OUTPUT_BASE64='e30=' # { }\n_CURRENT_SEARCH_DIR=${PROJECT_DIR}\n_PLIST_BUDDY=/usr/libexec/PlistBuddy\n_TARGET_PLIST=\"${BUILT_PRODUCTS_DIR}/${INFOPLIST_PATH}\"\n_DSYM_PLIST=\"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Info.plist\"\n_PACKAGE_JSON_NAME='package.json'\n\n# plist arrays\n_PLIST_ENTRY_KEYS=()\n_PLIST_ENTRY_TYPES=()\n_PLIST_ENTRY_VALUES=()\n\nfunction setPlistValue {\n  echo \"info:      setting plist entry '$1' of type '$2' in file '$4'\"\n  ${_PLIST_BUDDY} -c \"Add :$1 $2 '$3'\" $4 || echo \"info:      '$1' already exists\"\n}\n\nfunction getJsonKeyValue () {\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    ruby -KU -e \"require 'rubygems';require 'json'; output=JSON.parse('$1'); puts output[$_JSON_ROOT]['$2']\"\n  else\n    echo \"\"\n  fi;\n}\n\nfunction jsonBoolToYesNo () {\n  if [[ $1 == \"false\" ]]; then\n    echo \"NO\"\n  elif [[ $1 == \"true\" ]]; then\n    echo \"YES\"\n  else echo \"NO\"\n  fi\n}\n\necho \"info: -> ${_PROJECT_ABBREVIATION} build script started\"\necho \"info: 1) Locating ${_JSON_FILE_NAME} file:\"\n\nif [[ -z ${_CURRENT_SEARCH_DIR} ]]; then\n  _CURRENT_SEARCH_DIR=$(pwd)\nfi;\n\nwhile true; do\n  _CURRENT_SEARCH_DIR=$(dirname \"$_CURRENT_SEARCH_DIR\")\n  if [[ \"$_CURRENT_SEARCH_DIR\" == \"/\" ]] || [[ ${_CURRENT_LOOKUPS} -gt ${_MAX_LOOKUPS} ]]; then break; fi;\n  echo \"info:      ($_CURRENT_LOOKUPS of $_MAX_LOOKUPS) Searching in '$_CURRENT_SEARCH_DIR' for a ${_JSON_FILE_NAME} file.\"\n  _SEARCH_RESULT=$(find \"$_CURRENT_SEARCH_DIR\" -maxdepth 2 -name ${_JSON_FILE_NAME} -print | /usr/bin/head -n 1)\n  if [[ ${_SEARCH_RESULT} ]]; then\n    echo \"info:      ${_JSON_FILE_NAME} found at $_SEARCH_RESULT\"\n    break;\n  fi;\n  _CURRENT_LOOKUPS=$((_CURRENT_LOOKUPS+1))\ndone\n\nif [[ ${_SEARCH_RESULT} ]]; then\n  _JSON_OUTPUT_RAW=$(cat \"${_SEARCH_RESULT}\")\n  _RN_ROOT_EXISTS=$(ruby -KU -e \"require 'rubygems';require 'json'; output=JSON.parse('$_JSON_OUTPUT_RAW'); puts output[$_JSON_ROOT]\" || echo '')\n\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    if ! python3 --version >/dev/null 2>&1; then echo \"python3 not found, app.json file processing error.\" && exit 1; fi\n    _JSON_OUTPUT_BASE64=$(python3 -c 'import json,sys,base64;print(base64.b64encode(bytes(json.dumps(json.loads(open('\"'${_SEARCH_RESULT}'\"', '\"'rb'\"').read())['${_JSON_ROOT}']), '\"'utf-8'\"')).decode())' || echo \"e30=\")\n  fi\n\n  _PLIST_ENTRY_KEYS+=(\"google_mobile_ads_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n\n  # config.delay_app_measurement_init\n  _DELAY_APP_MEASUREMENT=$(getJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"delay_app_measurement_init\")\n  if [[ $_DELAY_APP_MEASUREMENT == \"true\" ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GADDelayAppMeasurementInit\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"YES\")\n  fi\n\n  # config.ios_app_id\n  _IOS_APP_ID=$(getJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"ios_app_id\")\n  if [[ $_IOS_APP_ID ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GADApplicationIdentifier\")\n    _PLIST_ENTRY_TYPES+=(\"string\")\n    _PLIST_ENTRY_VALUES+=(\"$_IOS_APP_ID\")\n  fi\n\n  # config.sk_ad_network_items\n  _SK_AD_NETWORK_ITEMS=$(getJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"sk_ad_network_items\")\n  if [[ $_SK_AD_NETWORK_ITEMS ]]; then\n    _PLIST_ENTRY_KEYS+=(\"SKAdNetworkItems\")\n    _PLIST_ENTRY_TYPES+=(\"array\")\n    _PLIST_ENTRY_VALUES+=(\"\")\n\n    oldifs=$IFS\n    IFS=\"\n\"\n    array=($(echo \"$_SK_AD_NETWORK_ITEMS\"))\n    IFS=$oldifs\n    for i in \"${!array[@]}\"; do\n      _PLIST_ENTRY_KEYS+=(\"SKAdNetworkItems:$i:SKAdNetworkIdentifier\")\n      _PLIST_ENTRY_TYPES+=(\"string\")\n      _PLIST_ENTRY_VALUES+=(\"${array[i]}\")  \n    done\n  fi\n\n    # config.user_tracking_usage_description\n  _USER_TRACKING_USAGE_DESCRIPTION=$(getJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"user_tracking_usage_description\")\n  if [[ $_USER_TRACKING_USAGE_DESCRIPTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"NSUserTrackingUsageDescription\")\n    _PLIST_ENTRY_TYPES+=(\"string\")\n    _PLIST_ENTRY_VALUES+=(\"$_USER_TRACKING_USAGE_DESCRIPTION\")\n  fi\nelse\n  _PLIST_ENTRY_KEYS+=(\"google_mobile_ads_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n  echo \"warning:   A ${_JSON_FILE_NAME} file was not found, whilst this file is optional it is recommended to include it to auto-configure services.\"\nfi;\n\necho \"info: 2) Injecting Info.plist entries: \"\n\n# Log out the keys we're adding\nfor i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n  echo \"    ->  $i) ${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\"\ndone\n\nif ! [[ -f \"${_TARGET_PLIST}\" ]]; then\n  echo \"error: unable to locate Info.plist to set properties. App will crash without GADApplicationIdentifier set.\"\n  exit 1\nfi\n\nif ! [[ $_IOS_APP_ID ]]; then\n  echo \"warning: ios_app_id key not found in react-native-google-mobile-ads key in app.json. App will crash without it.\"\n  echo \"         You can safely ignore this warning if you are using our Expo config plugin.\"\n  exit 0\nfi\n\nfor plist in \"${_TARGET_PLIST}\" \"${_DSYM_PLIST}\" ; do\n  if [[ -f \"${plist}\" ]]; then\n\n    # paths with spaces break the call to setPlistValue. temporarily modify\n    # the shell internal field separator variable (IFS), which normally\n    # includes spaces, to consist only of line breaks\n    oldifs=$IFS\n    IFS=\"\n\"\n\n    for i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n      setPlistValue \"${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\" \"${plist}\"\n    done\n\n    # restore the original internal field separator value\n    IFS=$oldifs\n  else\n    echo \"warning:   A Info.plist build output file was not found (${plist})\"\n  fi\ndone\n\necho \"info: <- ${_PROJECT_ABBREVIATION} build script finished\"\n";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		0BD906E01EC0C00300C8C18E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				0BB9AD7B1F5EC8F4001C08DB /* CallKit.m in Sources */,
				DE81A2DF2317ED5400AE1940 /* MeetHourBaseLogHandler.m in Sources */,
				0BB9AD7D1F60356D001C08DB /* AppInfo.m in Sources */,
				DE81A2D92316AC7600AE1940 /* LogBridge.m in Sources */,
				DEAFA779229EAD520033A7FA /* RNRootView.m in Sources */,
				DE762DB622AFDE8D000DEBD6 /* MeetHourUserInfo.m in Sources */,
				DEAD3227220C497000E93636 /* MeetHourConferenceOptions.m in Sources */,
				0B93EF7F1EC9DDCD0030D24D /* RCTBridgeWrapper.m in Sources */,
				0BA13D311EE83FF8007BEF7F /* ExternalAPI.m in Sources */,
				0BCA49601EC4B6C600B793EE /* POSIX.m in Sources */,
				C8AFD2802462C613000293D2 /* InfoPlistUtil.m in Sources */,
				C6CC49AF207412CF000DFA42 /* PiPViewCoordinator.swift in Sources */,
				DEFC743F21B178FA00E4DD96 /* LocaleDetector.m in Sources */,
				0BCA495F1EC4B6C600B793EE /* AudioMode.m in Sources */,
				0BCA49611EC4B6C600B793EE /* Proximity.m in Sources */,
				C69EFA0C209A0F660027712B /* MHCallKitEmitter.swift in Sources */,
				DEFE535621FB2E8300011A3A /* ReactUtils.m in Sources */,
				C6A34261204EF76800E062DD /* DragGestureController.swift in Sources */,
				4E51B76525E5345E0038575A /* ScheenshareEventEmiter.m in Sources */,
				A4A934E9212F3ADB001E9388 /* Dropbox.m in Sources */,
				C69EFA0D209A0F660027712B /* MHCallKitProxy.swift in Sources */,
				DE81A2D52316AC4D00AE1940 /* MeetHourLogger.m in Sources */,
				C69EFA0E209A0F660027712B /* MHCallKitListener.swift in Sources */,
				0B412F191EDEC65D00B1A0A6 /* MeetHourView.m in Sources */,
				DEFE535421FB1BF800011A3A /* MeetHour.m in Sources */,
				DE438CDA2350934700DD541D /* JavaScriptSandbox.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		0BD906EB1EC0C00300C8C18E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CC = "";
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				CXX = "";
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_BITCODE = YES;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers/platform/ios",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers/react/renderer/components/view/platform/cxx",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 12.1;
				LD = "";
				LDPLUSPLUS = "";
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = "$(inherited)";
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				USE_HERMES = true;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		0BD906EC1EC0C00300C8C18E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CC = "";
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				CXX = "";
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_BITCODE = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers/platform/ios",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers/react/renderer/components/view/platform/cxx",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 12.1;
				LD = "";
				LDPLUSPLUS = "";
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = "$(inherited)";
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				TARGETED_DEVICE_FAMILY = "1,2";
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		0BD906EE1EC0C00300C8C18E /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = D66E2DEBF7DF54B1C5F4D312 /* Pods-MeetHourSDK.debug.xcconfig */;
			buildSettings = {
				BUILD_LIBRARY_FOR_DISTRIBUTION = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_IDENTITY = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = "";
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				INFOPLIST_FILE = src/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_BUNDLE_IDENTIFIER = go.meethour.io.MeetHourSDK.ios;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = YES;
				SUPPORTS_MACCATALYST = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		0BD906EF1EC0C00300C8C18E /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 423D68AC746BF3CEEEC06A8A /* Pods-MeetHourSDK.release.xcconfig */;
			buildSettings = {
				BUILD_LIBRARY_FOR_DISTRIBUTION = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_IDENTITY = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = "";
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				INFOPLIST_FILE = src/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = go.meethour.io.MeetHourSDK.ios;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = YES;
				SUPPORTS_MACCATALYST = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "";
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		0BD906DF1EC0C00300C8C18E /* Build configuration list for PBXProject "sdk" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0BD906EB1EC0C00300C8C18E /* Debug */,
				0BD906EC1EC0C00300C8C18E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		0BD906ED1EC0C00300C8C18E /* Build configuration list for PBXNativeTarget "MeetHourSDK" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0BD906EE1EC0C00300C8C18E /* Debug */,
				0BD906EF1EC0C00300C8C18E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 0BD906DC1EC0C00300C8C18E /* Project object */;
}
