<html itemscope itemtype="http://schema.org/Product" prefix="og: http://ogp.me/ns#" xmlns="http://www.w3.org/1999/html">

<head>
    <meta charset="utf-8" />
    <title>Meet Hour - Embed Meeting</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <link rel="shortcut icon" href="https://meethour.io/images/logo.png?v=1" />
</head>

<body>
    <style>
        body {
            margin: auto;
        }

        #loader {
            border: 12px solid #f3f3f3;
            border-radius: 50%;
            border-top: 12px solid #444444;
            width: 70px;
            height: 70px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            100% {
                transform: rotate(360deg);
            }
        }

        .center {
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            margin: auto;
        }
    </style>
    <div id="loader" class="center"></div>
    <div id="conference"></div>
    <script src="https://meethour.io/external_api.js"></script>
    <script>
        document.onreadystatechange = function () {
            if (document.readyState !== "complete") {
                document.querySelector("body").style.visibility = "hidden";
                document.querySelector("#loader").style.visibility =
                    "visible";
            } else {
                document.querySelector("#loader").style.display = "none";
                document.querySelector("body").style.visibility = "visible";
            }
        };
    </script>
    <script>
        var url = window.location.href;
        var splitUrl = url.split('?');	
        var substr=splitUrl[1];
        
        if(typeof(substr)!='undefined' && substr.split('=')[0]=='roomName')
        {
            var rn = substr.split('=')[1];

            var domain = "meethour.io";
            var options = {
                roomName: rn,
                parentNode: document.querySelector("#conference"),
                interfaceConfigOverwrite: { 
                    TOOLBAR_BUTTONS: ['microphone','camera','closedcaptions','desktop','fullscreen','fodeviceselection','hangup','chat','etherpad','sharedvideo','raisehand','videoquality','filmstrip','stats','settings','shortcuts','tileview','mute-everyone','security','zoomin','zoomout']
                }
            };
            // Initialization of MeetHour External API
            var api = new MeetHourExternalAPI(domain, options);

            // To close the window once hangup is done
            api.addEventListener("readyToClose", () => {
                this.window.close();
            });

            // To close the window once hangup is done
            api.addEventListener("beforeunload", () => {
                if (confirm("Conference in Progress. Want to exit?")) {
                    this.window.close();
                } else {
                    // Do Nothing
                }
            });
        }
        else {
            window.location.href = "https://meethour.io";
        }
    </script>
</body>
</html>