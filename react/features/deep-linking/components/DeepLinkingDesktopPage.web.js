// @flow

import { BrowserDetection } from '@jitsi/js-utils';
import React, { Component } from 'react';
import type { Dispatch } from 'redux';

import { CHROME } from '../../../../browser';
import { createDeepLinkingPageEvent, sendAnalytics } from '../../analytics';
import { stateType } from '../../app/stateTypes';
import { translate } from '../../base/i18n';
import { removeUrlState } from '../../base/jwt';
import { Platform } from '../../base/react';
import { connect } from '../../base/redux';
import { Footer } from '../../welcome/components/Footer';
import { Header } from '../../welcome/components/Header';
import {
    openDesktopApp,
    openWebApp
} from '../actions';
import { PlatformOS } from '../constants';

import { styleDeeplinking } from './styleWeb';

/**
 * The type of the React {@code Component} props of
 * {@link DeepLinkingDesktopPage}.
 */
 type Props = {

    /**
     * Used to dispatch actions from the buttons.
     */
    dispatch: Dispatch<any>,

    /**
     * Used to obtain translations.
     */
    t: Function,

    pcode: string,

    /**
     * The URL of the location.
     */
    locationUrl: URL,

    /**
     * The interface config.
     */
    interfaceConfig: Object
};

/**
 * React component representing the deep linking page.
 *
 * @class DeepLinkingDesktopPage
 */
class DeepLinkingDesktopPage<P : Props> extends Component<P> {
    /**
     * Initializes a new {@code DeepLinkingDesktopPage} instance.
     *
     * @param {Object} props - The read-only React {@code Component} props with
     * which the new instance is to be initialized.
     */
    constructor(props: P) {
        super(props);
        this.state = {
            active: true
        };

        // Bind event handlers so they are only bound once per instance.
        this._onLaunchWeb = this._onLaunchWeb.bind(this);
        this._onTryAgain = this._onTryAgain.bind(this);
        this._onDownloadAPP = this._onDownloadAPP.bind(this);
        this._onLaunchElectron = this._onLaunchElectron.bind(this);
    }

    /**
     * Implements the Component's componentDidMount method.
     *
     * @inheritdoc
     */
    componentDidMount() {

        if (document.body) {
            document.body.style.overflowY = 'scroll';
        }
        sendAnalytics(
            createDeepLinkingPageEvent(
                'displayed', 'DeepLinkingDesktop', { isMobileBrowser: false }));
        setTimeout(() => {
            this.props.dispatch(removeUrlState());
        }, 2000);

        this._onLaunchElectron();
    }

    /**
     * Renders the component.
     *
     * @returns {ReactElement}
     */
    render() {
        const { t } = this.props;

        return (
            <>
                <Header
                    desktopDeepLink = { true }
                    prejoin = { false } />
                <div
                    className = 'container'
                    style = { styleDeeplinking.container }>
                    <div
                        className = 'invalidMeeting'
                        style = { styleDeeplinking.invalidMeeting }>
                        <div style = { styleDeeplinking.floatRight }>
                            <div className = 'jitsi-icon' />
                        </div>
                        <h4
                            style = { styleDeeplinking.joinMeeting }>
                            {t('deepLinking.joinMeetingWithDesktopApp')}</h4>
                        <div
                            style = { styleDeeplinking.launchMeeting }>
                            { <a
                                className = 'btn btn-primary'
                                href = { '#' }
                                onClick = { this._onLaunchElectron }><i className = 'bx bx-desktop pr-2' />
                                {t('deepLinking.launchMeetingInDesktopApp')}</a> }
                            <div style = { styleDeeplinking.marginTop10 }>Or
                            </div>
                            <div style = { styleDeeplinking.marginTop10 }>
                                <a
                                    href = '#'
                                    onClick = { this._onLaunchWeb }><i className = 'bx bx-desktop' />{t('deepLinking.continueWithBrowser')}</a>
                            </div>
                            { (<><div style = { styleDeeplinking.marginTop20 }>
                                {t('deepLinking.ifYouDontHaveTheAppYet')}:</div>
                            <div style = { styleDeeplinking.marginTop10 }> <a
                                className = 'btn btn-primary'
                                href = '#'
                                onClick = { this._onDownloadAPP }><i className = 'bx bx-download' /> {t('deepLinking.downloadApp')}
                            </a>
                            </div></>)}
                        </div>
                    </div>
                </div>
                <Footer
                    prejoin = { false }
                    style = { styleDeeplinking.width100 } />
            </>
        );

    }

    _onTryAgain: () => void;

    /**
     * Handles try again button clicks.
     *
     * @returns {void}
     */
    _onTryAgain() {
        sendAnalytics(
            createDeepLinkingPageEvent(
                'clicked', 'tryAgainButton', { isMobileBrowser: false }));
        this.props.dispatch(openDesktopApp());
    }

    _onLaunchElectron: () => void;

    /**
     * Handles try again button clicks.
     *
     * @param {Event} e - Event.
     * @returns {void}
     */
    _onLaunchElectron(e) {
        e && e.preventDefault();
        const { pcode } = this.props;

        const locationUrl: URL = this.props?.locationUrl;
        const urll = locationUrl.href;

        let appProtocol = urll.replace(/^https?:\/\//, this.props.interfaceConfig.DESKTOP_APP_SCHEME === undefined ? 'go.meethour.io://' : `${this.props.interfaceConfig.DESKTOP_APP_SCHEME}://`);

        const browser = new BrowserDetection();
        const browserName = browser.getName();

        // Adding this block to avoid the big JWT Token to be opened in Chrome & Windows
        if (locationUrl && Platform.OS === 'windows' && browserName === CHROME) {
            const urlv = `${locationUrl.origin + locationUrl.pathname}${pcode ? `?pcode=${pcode}` : ''}`;

            appProtocol = urlv.replace(/^https?:\/\//, this.props.interfaceConfig.DESKTOP_APP_SCHEME === undefined ? 'go.meethour.io://' : `${this.props.interfaceConfig.DESKTOP_APP_SCHEME}://`);
        }

        const iframe = document.createElement('iframe');

        iframe.style.display = 'none';
        iframe.src = appProtocol;
        document.body.appendChild(iframe);
    }


    _onDownloadAPP: () => void;

    /**
     * Handles try again button clicks.
     *
     * @param {Event} e - Event.
     * @returns {void}
     */
    _onDownloadAPP(e) {
        e.preventDefault();

        if (this.props.interfaceConfig?.WINDOWS_DOWNLOAD_LINK || this.props.interfaceConfig?.MAC_DOWNLOAD_LINK || this.props.interfaceConfig?.LINUX_DOWNLOAD_LINK) {
            if (Platform.OS === 'windows') {
                window.open(this.props.interfaceConfig?.WINDOWS_DOWNLOAD_LINK, '_blank');
            } else if (Platform.OS === 'macos') {
                window.open(this.props.interfaceConfig?.MAC_DOWNLOAD_LINK, '_blank');
            } else if (Platform.OS === 'linux') {
                window.open(this.props.interfaceConfig?.LINUX_DOWNLOAD_LINK, '_blank');
            }

            return;
        }

        const url = PlatformOS[Platform.OS];

        window.open(url, '_blank');

    }

    _onLaunchWeb: () => void;

    /**
     * Handles launch web button clicks.
     *
     * @param {Event} e - Event.
     * @returns {void}
     */
    _onLaunchWeb(e) {
        e.preventDefault();
        sendAnalytics(
            createDeepLinkingPageEvent(
                'clicked', 'launchWebButton', { isMobileBrowser: false }));
        this.props.dispatch(openWebApp());
    }
}

export default translate(connect(state => {
    return {
        locationUrl: state['features/base/connection'].locationURL,
        pcode: state[stateType.mtApi].md5password,
        interfaceConfig: state['features/base/config'].interfaceConfig
    };
})(DeepLinkingDesktopPage));
