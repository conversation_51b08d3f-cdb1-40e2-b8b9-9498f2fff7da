/* eslint-disable max-len */
/* eslint-disable react/jsx-no-target-blank */
/* eslint-disable react-native/no-inline-styles */
/* eslint-disable react-native/no-color-literals */
// @flow
import { useMediaQuery } from '@material-ui/core';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

// import {
//     Icon,
//     IconFacebook,
//     IconInstagram,
//     IconYoutube,
//     IconLinkedin,
//     IconTwitter
// } from '../../base/icons';

import { styleWeb } from './stylesWeb';

declare var config: Object;

const colorWhite = {
    color: 'white'
};
const h3Style = {
    color: 'white',
    fontSize: '12px'
};
type Props = {

    /**
     * Style props for footer.
     */
    style?: Object,

    /**
     * Changing the style based on prejoint styles.
     *
     */
    prejoin: ?boolean,
};

export const Footer = (props: Props) => {
    const { t } = useTranslation();
    const matched = useMediaQuery('(max-width:760px)');
    const { interfaceConfig } = useSelector(
        state => state['features/base/config']
    );

    React.useEffect(() => {
        if (
            interfaceConfig
            && interfaceConfig?.disablePrejoinFooter === true
            && !matched
        ) {
            const querySelcted = document.querySelector('#lobby-screen');

            if (querySelcted?.hasOwnProperty('style')) {
                querySelcted.style.minHeight = '850px';
            }
        }

        if (
            interfaceConfig
            && interfaceConfig?.disablePrejoinFooter === true
            && matched
        ) {
            const querySelcted = document.querySelector('#lobby-screen');

            if (querySelcted?.hasOwnProperty('style')) {
                querySelcted.style.minHeight = '995px';
            }
        }
    }, [ interfaceConfig, matched ]);

    if (props?.prejoin) {
        return (
            <React.Fragment>
                {config?.MEGAMENU_HEADER ? (
                    <footer
                        className = { `${
                            interfaceConfig?.disablePrejoinFooter === true
                            && !matched
                                ? 'headerFooter'
                                : ''
                        } footer-container` }
                        id = 'footer'
                        style = { props?.style }>
                        {interfaceConfig
                            && !interfaceConfig?.disablePrejoinFooter && (
                            <>
                                <div className = 'footer-top'>
                                    <div className = 'container'>
                                        <div
                                            className = 'row-footer'
                                            style = {{
                                                display: 'flex',
                                                justifyContent:
                                                        'space-around'
                                            }}>
                                            <div
                                                className = 'col-xxl-4 col-xl-4 col-lg-4  wow '
                                                data-wow-delay = '.3s'>
                                                <div className = 'footer__widget mb-50'>
                                                    <div className = 'footer__widget-title mb-25'>
                                                        <div className = 'footer__logo'>
                                                            <a href = '/'>
                                                                <img
                                                                    alt = 'Meet Hour'
                                                                    src = 'images/logo.SVG'
                                                                    style = {{
                                                                        width: '176px',
                                                                        height: '30px'
                                                                    }} />
                                                            </a>
                                                        </div>
                                                    </div>
                                                    <div className = 'footer__widget-content'>
                                                        <p className = ''>
                                                            {t('welcomepage.footer.officeAddress')}
                                                        </p>
                                                        <p className = ''>
                                                            {t('welcomepage.footer.phone')}:
                                                            <a
                                                                href = 'tel:+13128009410'
                                                                style = {{
                                                                    color: '#fff'
                                                                }}>
                                                                +1 (312) 800
                                                                9410
                                                            </a>
                                                        </p>
                                                        <p className = ''>
                                                            {t('welcomepage.footer.email')}:
                                                            <a
                                                                href = 'mailto:<EMAIL>'
                                                                style = {{
                                                                    color: '#fff'
                                                                }}>
                                                                <EMAIL>
                                                            </a>
                                                        </p>
                                                    </div>

                                                    <div className = 'footer__widget mt-30'>
                                                        <div className = 'footer__widget-title'>
                                                            <h3
                                                                style = {{
                                                                    color: '#fff'
                                                                }}>
                                                                {t('welcomepage.footer.followUs')}
                                                            </h3>
                                                        </div>
                                                        <div className = 'footer__widget-content'>
                                                            <div className = 'footer__social social-links'>
                                                                <a
                                                                    href = 'https://www.facebook.com/meethour/'
                                                                    target = '_blank'>
                                                                    <i className = 'bx bxl-facebook' />
                                                                </a>

                                                                <a
                                                                    href = 'https://twitter.com/MeetHourApp/'
                                                                    target = '_blank'>
                                                                    <svg
                                                                        height = '14'
                                                                        style = {{
                                                                            verticalAlign: 'text-top'
                                                                        }}
                                                                        viewBox = '0 0 512 512'
                                                                        width = '14'
                                                                        xmlns = 'http://www.w3.org/2000/svg'>
                                                                        <path
                                                                            d = 'M389.2 48h70.6L305.6 224.2 487 464H345L233.7 318.6 106.5 464H35.8L200.7 275.5 26.8 48H172.4L272.9 180.9 389.2 48zM364.4 421.8h39.1L151.1 88h-42L364.4 421.8z'
                                                                            fill = '#ffffff' /></svg></a>

                                                                <a
                                                                    href = 'https://www.instagram.com/MeetHourApp/'
                                                                    target = '_blank'>
                                                                    <i className = 'bx bxl-instagram' />
                                                                </a>

                                                                <a
                                                                    href = 'https://www.linkedin.com/company/meethour/'
                                                                    target = '_blank'>
                                                                    <i className = 'bx bxl-linkedin' />
                                                                </a>

                                                                <a
                                                                    href = 'https://www.youtube.com/channel/UCbB-py_vQt2lXJ1VMMVIH7w'
                                                                    target = '_blank'>
                                                                    <i className = 'bx bxl-youtube' />
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div className = 'footer__widget mt-30'>
                                                        <img
                                                            alt = 'secure ssl'
                                                            className = 'mr-10'
                                                            src = '/images/secure-ssl.png'
                                                            style = {{
                                                                width: '137px',
                                                                height: '70px'
                                                            }} />
                                                        <img
                                                            alt = 'secure payment'
                                                            className = 'mr-10'
                                                            src = '/images/secure-payment.png'
                                                            style = {{
                                                                width: '137px',
                                                                height: '70px'
                                                            }} />
                                                        <img
                                                            alt = 'data privacy'
                                                            src = '/images/data-privacy.png'
                                                            style = {{
                                                                width: '70px',
                                                                height: '70px'
                                                            }} />
                                                    </div>
                                                </div>
                                            </div>

                                            <div
                                                className = 'col-xxl-2 col-xl-2 col-lg-2  wow '
                                                data-wow-delay = '.5s'>
                                                <div className = 'footer__widget mb-50'>
                                                    <div className = 'footer__widget-title mb-10'>
                                                        <h3
                                                            style = {{
                                                                color: '#fff'
                                                            }}>
                                                            {t('welcomepage.header.products')}
                                                        </h3>
                                                    </div>
                                                    <div className = 'footer__widget-content'>
                                                        <div className = 'footer__link'>
                                                            <a
                                                                href = '/products/video-conference'
                                                                style = {{
                                                                    color: '#fff'
                                                                }}>
                                                                {t('welcomepage.header.videoConference')}
                                                            </a>
                                                            <hr
                                                                style = {{
                                                                    backgroundColor:
                                                                            'hsla(0, 0%, 100%, .2)'
                                                                }} />
                                                            <div>
                                                                <a
                                                                    href = '/products/video-conference/features'
                                                                    style = {{
                                                                        color: '#fff'
                                                                    }}>
                                                                    {t('welcomepage.header.features')}
                                                                </a>
                                                            </div>
                                                            <br />
                                                            <div>
                                                                <a
                                                                    href = '/products/video-conference/pricing'
                                                                    style = {{
                                                                        color: '#fff'
                                                                    }}>
                                                                    {t('welcomepage.header.pricing')}
                                                                </a>
                                                            </div>
                                                            <hr
                                                                style = {{
                                                                    backgroundColor:
                                                                            'hsla(0, 0%, 100%, .2)'
                                                                }} />
                                                            <div>
                                                                <a
                                                                    href = '/products/video-conference/free'
                                                                    style = {{
                                                                        color: '#fff'
                                                                    }}>
                                                                    {t('welcomepage.freePlan', { lng: 'en' })}
                                                                </a>{' '}
                                                            </div>

                                                            <br />
                                                            <div>
                                                                <a
                                                                    href = '/products/video-conference/pro'
                                                                    style = {{
                                                                        color: '#fff'
                                                                    }}>
                                                                    {t('welcomepage.proPlan', { lng: 'en' })}
                                                                </a>{' '}
                                                            </div>

                                                            <br />
                                                            <div>
                                                                <a
                                                                    href = '/products/video-conference/developer'
                                                                    style = {{
                                                                        color: '#fff'
                                                                    }}>
                                                                    {t('welcomepage.developerPlan', { lng: 'en' })}
                                                                </a>
                                                            </div>

                                                            <br />
                                                            <div>
                                                                <a
                                                                    href = '/products/video-conference/enterprise'
                                                                    style = {{
                                                                        color: '#fff'
                                                                    }}>
                                                                    {t('welcomepage.enterprisePlan', { lng: 'en' })}
                                                                </a>
                                                            </div>

                                                            <br />
                                                            <div>
                                                                <a
                                                                    href = '/products/video-conference/enterprise-selfhost'
                                                                    style = {{
                                                                        color: '#fff'
                                                                    }}>
                                                                    {t('welcomepage.enterpriseSelfHostPlan', { lng: 'en' })}
                                                                </a>
                                                            </div>

                                                            <hr
                                                                style = {{
                                                                    backgroundColor:
                                                                            'hsla(0, 0%, 100%, .2)'
                                                                }} />

                                                            <div>
                                                                <a
                                                                    href = 'https://mycaly.io/'
                                                                    style = {{
                                                                        color: '#fff'
                                                                    }}
                                                                    target = '_blank'>
                                                                    {t('welcomepage.header.myCaly', { lng: 'en' })}
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div
                                                className = 'col-xxl-2 col-xl-2 col-lg-2 wow '
                                                data-wow-delay = '.5s'>
                                                <div className = 'footer__widget mb-50'>
                                                    <div className = 'footer__widget-title mb-10'>
                                                        <h3
                                                            style = {{
                                                                color: '#fff'
                                                            }}>
                                                            {t('welcomepage.header.solutions')}
                                                        </h3>
                                                    </div>
                                                    <div className = 'footer__widget-content'>
                                                        <div className = 'footer__link'>
                                                            <div>
                                                                <a
                                                                    href = '/solutions/usecases/videoconferencing'
                                                                    style = {{
                                                                        color: '#fff'
                                                                    }}>
                                                                    {t('welcomepage.header.videoConferencing')}
                                                                </a>
                                                            </div>

                                                            <br />

                                                            <div>
                                                                <a
                                                                    href = '/solutions/usecases/livestreaming'
                                                                    style = {{
                                                                        color: '#fff'
                                                                    }}>
                                                                    {t('welcomepage.header.liveStreaming')}
                                                                </a>
                                                            </div>

                                                            <br />
                                                            <div>
                                                                <a
                                                                    href = '/solutions/usecases/virtualclassrooms'
                                                                    style = {{
                                                                        color: '#fff'
                                                                    }}>
                                                                    {t('welcomepage.header.virtualClassrooms')}
                                                                </a>
                                                            </div>

                                                            <br />
                                                            <div>
                                                                <a
                                                                    href = '/solutions/usecases/virtualevents'
                                                                    style = {{
                                                                        color: '#fff'
                                                                    }}>
                                                                    {t('welcomepage.header.virtualEvents')}
                                                                </a>
                                                            </div>

                                                            <br />
                                                            <div>
                                                                <a
                                                                    href = '/solutions/usecases/ekyc'
                                                                    style = {{
                                                                        color: '#fff'
                                                                    }}>
                                                                    {t('welcomepage.header.videoKYC')}
                                                                </a>
                                                            </div>

                                                            <br />
                                                            <div>
                                                                <a
                                                                    href = '/solutions/usecases/webinars'
                                                                    style = {{
                                                                        color: '#fff'
                                                                    }}>
                                                                    {t('welcomepage.header.webinars')}
                                                                </a>
                                                            </div>

                                                            <br />
                                                            <div>
                                                                <a
                                                                    href = '/solutions/usecases/fundraising'
                                                                    style = {{
                                                                        color: '#fff'
                                                                    }}>
                                                                    {t('welcomepage.header.fundraisingDonate')}
                                                                </a>
                                                            </div>

                                                            <hr
                                                                style = {{
                                                                    backgroundColor:
                                                                            'hsla(0, 0%, 100%, .2)'
                                                                }} />
                                                            <div>
                                                                <a
                                                                    href = '/solutions/industries/edtech'
                                                                    style = {{
                                                                        color: '#fff'
                                                                    }}>
                                                                    {t('welcomepage.header.edTech')}
                                                                </a>
                                                            </div>

                                                            <br />
                                                            <div>
                                                                <a
                                                                    href = '/solutions/industries/fitness'
                                                                    style = {{
                                                                        color: '#fff'
                                                                    }}>
                                                                    {t('welcomepage.header.fitness')}
                                                                </a>
                                                            </div>

                                                            <br />
                                                            <div>
                                                                <a
                                                                    href = '/solutions/industries/healthcare'
                                                                    style = {{
                                                                        color: '#fff'
                                                                    }}>
                                                                    {t('welcomepage.header.telehealth')}
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div
                                                className = 'col-xxl-2 col-xl-2 col-lg-2 wow '
                                                data-wow-delay = '.5s'>
                                                <div className = 'footer__widget mb-50'>
                                                    <div className = 'footer__widget-title mb-10'>
                                                        <h3
                                                            style = {{
                                                                color: '#fff'
                                                            }}>
                                                            {t('welcomepage.footer.company')}
                                                        </h3>
                                                    </div>
                                                    <div className = 'footer__widget-content'>
                                                        <div className = 'footer__link'>
                                                            <div>
                                                                <a
                                                                    href = '/whoweare'
                                                                    style = {{
                                                                        color: '#fff'
                                                                    }}>
                                                                    {t('welcomepage.footer.whoAreYou')}
                                                                </a>{' '}
                                                            </div>

                                                            <br />
                                                            <div>
                                                                <a
                                                                    href = '/inthenews'
                                                                    style = {{
                                                                        color: '#fff'
                                                                    }}>
                                                                    {t('welcomepage.footer.inTheNews')}
                                                                </a>
                                                            </div>

                                                            <br />
                                                            <div>
                                                                <a
                                                                    href = 'https://testimonials.meethour.io/all'
                                                                    style = {{
                                                                        color: '#fff'
                                                                    }}
                                                                    target = '_blank'>
                                                                    {t('welcomepage.footer.testimonials')}
                                                                </a>
                                                            </div>

                                                            <br />
                                                            <div>
                                                                <a
                                                                    href = '/contact'
                                                                    style = {{
                                                                        color: '#fff'
                                                                    }}>
                                                                    {t('welcomepage.footer.contact')}
                                                                </a>
                                                            </div>

                                                            <br />
                                                            <div>
                                                                <a
                                                                    href = 'https://meethour.tawk.help/'
                                                                    style = {{
                                                                        color: '#fff'
                                                                    }}
                                                                    target = '_blank'>
                                                                    {t('welcomepage.footer.helpDesk')}
                                                                </a>
                                                            </div>

                                                            <br />
                                                            <div>
                                                                <a
                                                                    href = '/faqs'
                                                                    style = {{
                                                                        color: '#fff'
                                                                    }}>
                                                                    {t('welcomepage.footer.faqs')}
                                                                </a>
                                                            </div>

                                                            <br />
                                                            <div>
                                                                <a
                                                                    href = 'http://kb.meethour.io/category/how-to'
                                                                    style = {{
                                                                        color: '#fff'
                                                                    }}
                                                                    target = '_blank'>
                                                                    {t('welcomepage.footer.knowledgeBase')}
                                                                </a>
                                                            </div>

                                                            <br />
                                                            <div>
                                                                <a
                                                                    href = '/blog'
                                                                    style = {{
                                                                        color: '#fff'
                                                                    }}>
                                                                    {t('welcomepage.footer.blog')}
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div
                                                className = 'col-xxl-2 col-xl-2 col-lg-2 wow '
                                                data-wow-delay = '.7s'>
                                                <div className = 'footer__widget mb-50'>
                                                    <div className = 'footer__widget-title mb-10'>
                                                        <h3
                                                            style = {{
                                                                color: '#fff'
                                                            }}>
                                                            {t('welcomepage.footer.app')}
                                                        </h3>
                                                    </div>
                                                    <div className = 'footer__widget-content'>
                                                        <div className = 'footer__link'>
                                                            <div>
                                                                <a
                                                                    href = '/meethour-download'
                                                                    style = {{
                                                                        color: '#fff'
                                                                    }}>
                                                                    {t('welcomepage.footer.download')}
                                                                </a>
                                                            </div>

                                                            <br />
                                                            <div>
                                                                <a
                                                                    href = '/integration'
                                                                    style = {{
                                                                        color: '#fff'
                                                                    }}>
                                                                    {t('welcomepage.footer.integrations')}
                                                                </a>
                                                            </div>

                                                            <br />
                                                            <div>
                                                                <a
                                                                    href = '/developers'
                                                                    style = {{
                                                                        color: '#fff'
                                                                    }}>
                                                                    {t('welcomepage.footer.developers')}
                                                                </a>
                                                            </div>

                                                            <br />

                                                            <div>
                                                                <a
                                                                    href = 'https://bit.ly/meethour-ppt'
                                                                    style = {{
                                                                        color: '#fff'
                                                                    }}
                                                                    target = '_blank'>
                                                                    {t('welcomepage.footer.productPresentation')}
                                                                </a>
                                                            </div>

                                                            <br />
                                                            <div>
                                                                <a
                                                                    href = 'https://docs.v-empower.com/docs/MeetHour-API'
                                                                    style = {{
                                                                        color: '#fff'
                                                                    }}
                                                                    target = '_blank'>
                                                                    {t('welcomepage.footer.apiDocumentation')}
                                                                </a>
                                                            </div>

                                                            <br />
                                                            <div>
                                                                <a
                                                                    href = '/sdks'
                                                                    style = {{
                                                                        color: '#fff'
                                                                    }}>
                                                                    {t('welcomepage.footer.webMobileSDK')}
                                                                </a>
                                                            </div>

                                                            <br />
                                                            <div>
                                                                <a
                                                                    href = '/disclaimer'
                                                                    style = {{
                                                                        color: '#fff'
                                                                    }}>
                                                                    {t('welcomepage.footer.disclaimer')}
                                                                </a>
                                                            </div>

                                                            <br />
                                                            <div>
                                                                <a
                                                                    href = '/privacypolicy'
                                                                    style = {{
                                                                        color: '#fff'
                                                                    }}>
                                                                    {t('welcomepage.footer.privacyPolicy')}
                                                                </a>
                                                            </div>

                                                            <br />
                                                            <div>
                                                                <a
                                                                    href = '/termsandconditions'
                                                                    style = {{
                                                                        color: '#fff'
                                                                    }}>
                                                                    {t('welcomepage.footer.termsConditions')}
                                                                </a>
                                                            </div>

                                                            <br />
                                                            <div>
                                                                <a
                                                                    href = '/refundandcancellation'
                                                                    style = {{
                                                                        color: '#fff'
                                                                    }}>
                                                                    {t('welcomepage.footer.refundCancellationPolicy')}
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div
                                    className = 'container d-md-flex py-4 justify-content-center'
                                    style = {{
                                        borderTop:
                                                '1px solid hsla(0,0%,100%,.1)'
                                    }}>
                                    <div className = 'copyright'>
                                        <strong>
                                            <span>
                                                &copy; {t('welcomepage.footer.copyrightText')}
                                            </span>
                                        </strong>
                                    </div>
                                    <div className = 'social-links text-center text-md-right pt-3 pt-md-0'>
                                        {/* <a
=======
                                    </div>

                                    <div
                                        className="col-xxl-2 col-xl-2 col-lg-2 col-md-4 col-sm-6 wow "
                                        data-wow-delay=".5s"
                                    >
                                        <div className="footer__widget mb-50">
                                            <div className="footer__widget-title mb-10">
                                                <h3 style={{ color: "#fff" }}>
                                                    Solutions
                                                </h3>
                                            </div>
                                            <div className="footer__widget-content">
                                                <div className="footer__link">
                                                    <div>
                                                        <a
                                                            href="/solutions/usecases/videoconferencing"
                                                            style={{
                                                                color: "#fff",
                                                            }}
                                                        >
                                                            Video Conferencing
                                                        </a>
                                                    </div>

                                                    <br />

                                                    <div>
                                                        <a
                                                            href="/solutions/usecases/livestreaming"
                                                            style={{
                                                                color: "#fff",
                                                            }}
                                                        >
                                                            Live Streaming
                                                        </a>
                                                    </div>

                                                    <br />
                                                    <div>
                                                        <a
                                                            href="/solutions/usecases/virtualclassrooms"
                                                            style={{
                                                                color: "#fff",
                                                            }}
                                                        >
                                                            Virtual Clasrooms
                                                        </a>
                                                    </div>

                                                    <br />
                                                    <div>
                                                        <a
                                                            href="/solutions/usecases/virtualevents"
                                                            style={{
                                                                color: "#fff",
                                                            }}
                                                        >
                                                            Virtual Events
                                                        </a>
                                                    </div>

                                                    <br />
                                                    <div>
                                                        <a
                                                            href="/solutions/usecases/ekyc"
                                                            style={{
                                                                color: "#fff",
                                                            }}
                                                        >
                                                            Video eKYC{" "}
                                                        </a>
                                                    </div>

                                                    <br />
                                                    <div>
                                                        <a
                                                            href="/solutions/usecases/webinars"
                                                            style={{
                                                                color: "#fff",
                                                            }}
                                                        >
                                                            Webinars
                                                        </a>
                                                    </div>

                                                    <br />
                                                    <div>
                                                        <a
                                                            href="/solutions/usecases/fundraising"
                                                            style={{
                                                                color: "#fff",
                                                            }}
                                                        >
                                                            Fundraising / Donate
                                                        </a>
                                                    </div>

                                                    <hr style={{
                                                            backgroundColor: "hsla(0, 0%, 100%, .2)",

                                                        }}></hr>
                                                    <div>
                                                        <a
                                                            href="/solutions/industries/edtech"
                                                            style={{
                                                                color: "#fff",
                                                            }}
                                                        >
                                                            EdTech
                                                        </a>
                                                    </div>

                                                    <br />
                                                    <div>
                                                        <a
                                                            href="/solutions/industries/fitness"
                                                            style={{
                                                                color: "#fff",
                                                            }}
                                                        >
                                                            Fitness
                                                        </a>
                                                    </div>

                                                    <br />
                                                    <div>
                                                        <a
                                                            href="/solutions/industries/healthcare"
                                                            style={{
                                                                color: "#fff",
                                                            }}
                                                        >
                                                            Telehealth
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div
                                        className="col-xxl-2 col-xl-2 col-lg-2 col-md-4 col-sm-6 wow "
                                        data-wow-delay=".5s"
                                    >
                                        <div className="footer__widget mb-50">
                                            <div className="footer__widget-title mb-10">
                                                <h3 style={{ color: "#fff" }}>
                                                    Company
                                                </h3>
                                            </div>
                                            <div className="footer__widget-content">
                                                <div className="footer__link">
                                                    <div>
                                                        <a
                                                            href="/whoweare"
                                                            style={{
                                                                color: "#fff",
                                                            }}
                                                        >
                                                            Who we are{" "}
                                                        </a>{" "}
                                                    </div>

                                                    <br />
                                                    <div>
                                                        <a
                                                            href="/inthenews"
                                                            style={{
                                                                color: "#fff",
                                                            }}
                                                        >
                                                            In the News{" "}
                                                        </a>
                                                    </div>

                                                    <br />
                                                    <div>
                                                        <a
                                                            href="https://testimonials.meethour.io/all"
                                                            target="_Blank"
                                                            style={{
                                                                color: "#fff",
                                                            }}
                                                        >
                                                            Testimonials
                                                        </a>
                                                    </div>

                                                    <br />
                                                    <div>
                                                        <a
                                                            href="/contact"
                                                            style={{
                                                                color: "#fff",
                                                            }}
                                                        >
                                                            Contact{" "}
                                                        </a>
                                                    </div>

                                                    <br />
                                                    <div>
                                                        <a
                                                            href="https://meethour.tawk.help/"
                                                            target="_blank"
                                                            style={{
                                                                color: "#fff",
                                                            }}
                                                        >
                                                            Help Desk
                                                        </a>
                                                    </div>

                                                    <br />
                                                    <div>
                                                        <a
                                                            href="/faqs"
                                                            style={{
                                                                color: "#fff",
                                                            }}
                                                        >
                                                            FAQs
                                                        </a>
                                                    </div>

                                                    <br />
                                                    <div>
                                                        <a
                                                            href="http://kb.meethour.io/category/how-to"
                                                            target="_Blank"
                                                            style={{
                                                                color: "#fff",
                                                            }}
                                                        >
                                                            Knowledge Base
                                                        </a>
                                                    </div>

                                                    <br />
                                                    <div>
                                                        <a
                                                            href="/blog"
                                                            style={{
                                                                color: "#fff",
                                                            }}
                                                        >
                                                            Blog{" "}
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div
                                        className="col-xxl-2 col-xl-2 col-lg-2 col-md-4 col-sm-6 wow "
                                        data-wow-delay=".7s"
                                    >
                                        <div className="footer__widget mb-50">
                                            <div className="footer__widget-title mb-10">
                                                <h3 style={{ color: "#fff" }}>
                                                    App
                                                </h3>
                                            </div>
                                            <div className="footer__widget-content">
                                                <div className="footer__link">
                                                    <div>
                                                        <a
                                                            href="/meethour-download"
                                                            style={{
                                                                color: "#fff",
                                                            }}
                                                        >
                                                            Download{" "}
                                                        </a>
                                                    </div>

                                                    <br />
                                                    <div>
                                                        <a
                                                            href="/integration"
                                                            style={{
                                                                color: "#fff",
                                                            }}
                                                        >
                                                            Integrations{" "}
                                                        </a>
                                                    </div>

                                                    <br />
                                                    <div>
                                                        <a
                                                            href="/developers"
                                                            style={{
                                                                color: "#fff",
                                                            }}
                                                        >
                                                            Developers{" "}
                                                        </a>
                                                    </div>

                                                    <br />

                                                    <div>
                                                        <a
                                                            href="https://bit.ly/meethour-ppt"
                                                            target="_Blank"
                                                            style={{
                                                                color: "#fff",
                                                            }}
                                                        >
                                                            Product Presentation
                                                        </a>
                                                    </div>

                                                    <br />
                                                    <div>
                                                        <a
                                                            href="https://docs.v-empower.com/docs/MeetHour-API"
                                                            target="_Blank"
                                                            style={{
                                                                color: "#fff",
                                                            }}
                                                        >
                                                            API Documentation
                                                        </a>
                                                    </div>

                                                    <br />
                                                    <div>
                                                        <a
                                                            href="/sdks"
                                                            style={{
                                                                color: "#fff",
                                                            }}
                                                        >
                                                            Web & Mobile SDK{" "}
                                                        </a>
                                                    </div>

                                                    <br />
                                                    <div>
                                                        <a
                                                            href="/disclaimer"
                                                            style={{
                                                                color: "#fff",
                                                            }}
                                                        >
                                                            Disclaimer{" "}
                                                        </a>
                                                    </div>

                                                    <br />
                                                    <div>
                                                        <a
                                                            href="/privacypolicy"
                                                            style={{
                                                                color: "#fff",
                                                            }}
                                                        >
                                                            Privacy Policy{" "}
                                                        </a>
                                                    </div>

                                                    <br />
                                                    <div>
                                                        <a
                                                            href="/termsandconditions"
                                                            style={{
                                                                color: "#fff",
                                                            }}
                                                        >
                                                            Terms & Conditions
                                                        </a>
                                                    </div>

                                                    <br />
                                                    <div>
                                                        <a
                                                            href="/refundandcancellation"
                                                            style={{
                                                                color: "#fff",
                                                            }}
                                                        >
                                                            Refund &
                                                            Cancellation Policy
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                                <div className="container d-md-flex py-4 justify-content-center" style={{borderTop:"1px solid hsla(0,0%,100%,.1)"}}>

                                        <div className="copyright">
                                            &copy;
                                            <strong>
                                                <span>&copy; Copyright 2020 - 2024 {t('welcomepage.footer.meethourLLC', { lng: 'en' })}. All Rights Reserved
                                                    </span>
                                            </strong>


                                    </div>
                                    <div className="social-links text-center text-md-right pt-3 pt-md-0">
                                        {/* <a
>>>>>>> 516dfa6c0c6272b0e9390fdeb6e8a144920e204f
                                className = 'twitter'
                                href = '#' >
                                <Icon
                                    className = { '' }
                                    size = { 20 }
                                    src = { IconTwitter } />
                            </a>
                            <a
                                className = 'facebook'
                                href = '#'>                        <Icon
                                    className = { '' }
                                    size = { 20 }
                                    src = { IconFacebook } /></a>
                            <a
                                className = 'instagram'
                                href = '#'><Icon
                                    className = { '' }
                                    size = { 20 }
                                    src = { IconInstagram } /></a>
                            <a
                                className = 'google-plus'
                                href = '#'><Icon
                                    className = { '' }
                                    size = { 20 }
                                    src = { IconYoutube } /></a>
                            <a
                                className = 'linkedin'
                                href = '#'><Icon
                                    className = { '' }
                                    size = { 20 }
                                    src = { IconLinkedin } /></a> */}
                                    </div>
                                </div>
                            </>
                        )}
                        {interfaceConfig
                            && interfaceConfig?.disablePrejoinFooter && (
                            <div
                                className = 'container'
                                style = { styleWeb.disablePrejoinFooter }>
                                <h3 style = { h3Style }>
                                    {interfaceConfig.APP_NAME}
                                    {interfaceConfig.SHOW_POWERED_BY
                                            && <span>. Powered by Meet Hour</span>
                                    }
                                </h3>
                            </div>
                        )}
                    </footer>
                ) : (
                    <footer
                        className = {
                            interfaceConfig?.disablePrejoinFooter === true
                            && !matched
                                ? 'headerFooter'
                                : ''
                        }
                        id = 'footer'
                        style = { props?.style }>
                        {interfaceConfig
                            && !interfaceConfig?.disablePrejoinFooter && (
                            <>
                                <div className = 'footer-top'>
                                    <div className = 'container'>
                                        <div className = 'row'>
                                            <div className = 'col-lg-3 col-md-6 footer-contact'>
                                                <h3 style = { colorWhite }>
                                                    {t('welcomepage.footer.meethourLLC', { lng: 'en' })}
                                                    <span>.</span>
                                                </h3>
                                                <p>
                                                    {t('welcomepage.footer.officeAddress')}
                                                    <br />
                                                    <strong>
                                                        {t('welcomepage.footer.phone')}:
                                                    </strong>{' '}
                                                    <a href = 'tel:+13128009410'>
                                                        +****************
                                                    </a>
                                                    <br />
                                                    <strong>
                                                        {t('welcomepage.footer.email')}:
                                                    </strong>{' '}
                                                    <a href = 'mailto:<EMAIL>'>
                                                        <EMAIL>
                                                    </a>
                                                    <br />
                                                </p>
                                            </div>

                                            <div className = 'col-lg-2 col-md-6 footer-links'>
                                                <h4>{t('welcomepage.footer.company')}</h4>
                                                <ul>
                                                    <li>
                                                        <i className = 'bx bx-chevron-right' />
                                                        <a href = '/'>{t('welcomepage.footer.home')}</a>
                                                    </li>
                                                    <li>
                                                        <i className = 'bx bx-chevron-right' />
                                                        <a href = '/who-we-are.html'>
                                                            {t('welcomepage.footer.whoAreYou')}
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <i className = 'bx bx-chevron-right' />
                                                        <a href = '/#features'>
                                                            {t('welcomepage.header.features')}
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <i className = 'bx bx-chevron-right' />
                                                        <a href = '/#pricing'>
                                                            {t('welcomepage.header.pricing')}
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <i className = 'bx bx-chevron-right' />{' '}
                                                        <a
                                                            href = 'https://testimonials.meethour.io/all'
                                                            // eslint-disable-next-line react/jsx-no-target-blank
                                                            target = '_blank'>
                                                            {t('welcomepage.footer.testimonials')}
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <i className = 'bx bx-chevron-right' />
                                                        <a href = '/news.html'>
                                                            {t('welcomepage.footer.inTheNews')}
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <i className = 'bx bx-chevron-right' />
                                                        <a href = '/contact.html'>
                                                            {t('welcomepage.footer.contact')}
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <i className = 'bx bx-chevron-right' />
                                                        <a href = '/FAQ.html'>
                                                            {t('welcomepage.footer.faqs')}
                                                        </a>
                                                    </li>
                                                </ul>
                                            </div>

                                            <div className = 'col-lg-3 col-md-6 footer-links'>
                                                <h4>{t('welcomepage.footer.app')}</h4>
                                                <ul>
                                                    <li>
                                                        <i className = 'bx bx-chevron-right' />{' '}
                                                        <a
                                                            href = 'https://apps.apple.com/in/app/meet-hour/id1527001689'
                                                            rel = 'noopener noreferrer'
                                                            target = '_blank'>
                                                            {t('welcomepage.footer.iOSAppDownload')}
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <i className = 'bx bx-chevron-right' />
                                                        <a
                                                            href = 'https://play.google.com/store/apps/details?id=go.meethour.io' // eslint-disable-next-line max-len
                                                            rel = 'noopener noreferrer'
                                                            target = '_blank'>
                                                            {t('welcomepage.footer.androidAppDownload')}
                                                        </a>
                                                    </li>

                                                    <li>
                                                        <i className = 'bx bx-chevron-right' />
                                                        <a href = '/disclaimer.html'>
                                                            {t('welcomepage.footer.disclaimer')}
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <i className = 'bx bx-chevron-right' />
                                                        <a href = '/privacy-policy.html'>
                                                            {t('welcomepage.footer.privacyPolicy')}
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <i className = 'bx bx-chevron-right' />
                                                        <a href = '/terms-conditions.html'>
                                                            {t('welcomepage.footer.termsConditions')}
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <i className = 'bx bx-chevron-right' />
                                                        <a href = '/refund-and-cancellation.html'>
                                                            {t('welcomepage.footer.refundCancellationPolicy')}
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <i className = 'bx bx-chevron-right' />{' '}
                                                        <a
                                                            href = 'https://docs.v-empower.com/docs/MeetHour-API '
                                                            target = {
                                                                '_blank'
                                                            }>
                                                            {t('welcomepage.footer.apiDocumentation')}
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <i className = 'bx bx-chevron-right' />{' '}
                                                        <a
                                                            href = 'https://github.com/v-empower/MeetHour-Web-MobileSDKs'
                                                            target = {
                                                                '_blank'
                                                            }>
                                                            {t('welcomepage.footer.webMobileSDK')}
                                                        </a>
                                                    </li>
                                                </ul>
                                            </div>

                                            <div className = 'col-lg-4 col-md-6 footer-newsletter'>
                                                <img
                                                    className = 'img-fluid'
                                                    src = 'images/footer-img.png' />
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div className = 'container d-md-flex py-4'>
                                    <div className = 'mr-md-auto text-center text-md-left'>
                                        <div className = 'copyright'>
                                            &copy; {t('welcomepage.footer.copyright')}{' '}
                                            <strong>
                                                <span>{t('welcomepage.footer.meethourLLC', { lng: 'en' })}.</span>
                                            </strong>
                                            . {t('welcomepage.footer.allRightsReserved')}
                                        </div>
                                    </div>
                                    <div className = 'social-links text-center text-md-right pt-3 pt-md-0'>
                                        {/* <a
                                className = 'twitter'
                                href = '#' >
                                <Icon
                                    className = { '' }
                                    size = { 20 }
                                    src = { IconTwitter } />
                            </a>
                            <a
                                className = 'facebook'
                                href = '#'>                        <Icon
                                    className = { '' }
                                    size = { 20 }
                                    src = { IconFacebook } /></a>
                            <a
                                className = 'instagram'
                                href = '#'><Icon
                                    className = { '' }
                                    size = { 20 }
                                    src = { IconInstagram } /></a>
                            <a
                                className = 'google-plus'
                                href = '#'><Icon
                                    className = { '' }
                                    size = { 20 }
                                    src = { IconYoutube } /></a>
                            <a
                                className = 'linkedin'
                                href = '#'><Icon
                                    className = { '' }
                                    size = { 20 }
                                    src = { IconLinkedin } /></a> */}
                                        <a
                                            href = 'https://www.facebook.com/meethour/'
                                            rel = 'noopener noreferrer'
                                            target = '_blank'>
                                            <i className = 'bx bxl-facebook' />
                                        </a>
                                        <a
                                            href = 'https://twitter.com/MeetHourApp/'
                                            rel = 'noopener noreferrer'
                                            target = '_blank'>
                                            <svg
                                                height = '14'
                                                style = {{
                                                    verticalAlign: 'text-top'
                                                }}
                                                viewBox = '0 0 512 512'
                                                width = '14'
                                                xmlns = 'http://www.w3.org/2000/svg'>
                                                <path
                                                    d = 'M389.2 48h70.6L305.6 224.2 487 464H345L233.7 318.6 106.5 464H35.8L200.7 275.5 26.8 48H172.4L272.9 180.9 389.2 48zM364.4 421.8h39.1L151.1 88h-42L364.4 421.8z'
                                                    fill = '#ffffff' /></svg>
                                        </a>
                                        <a
                                            href = 'https://www.instagram.com/MeetHourApp/'
                                            rel = 'noopener noreferrer'
                                            target = '_blank'>
                                            <i className = 'bx bxl-instagram' />
                                        </a>
                                        <a
                                            href = 'https://www.linkedin.com/company/meethour/'
                                            rel = 'noopener noreferrer'
                                            target = '_blank'>
                                            <i className = 'bx bxl-linkedin' />
                                        </a>
                                        <a
                                            href = ' https://www.youtube.com/channel/UCbB-py_vQt2lXJ1VMMVIH7w/'
                                            rel = 'noopener noreferrer'
                                            target = '_blank'>
                                            <i className = 'bx bxl-youtube' />
                                        </a>
                                    </div>
                                </div>
                            </>
                        )}
                        {interfaceConfig
                            && interfaceConfig?.disablePrejoinFooter && (
                            <div
                                className = 'container'
                                style = { styleWeb.disablePrejoinFooter }>
                                <h3 style = { h3Style }>
                                    {interfaceConfig.APP_NAME}
                                    {interfaceConfig.SHOW_POWERED_BY
                                            && <span>. Powered by Meet Hour</span>
                                    }
                                </h3>
                            </div>
                        )}
                    </footer>
                )}
            </React.Fragment>
        );
    }

    return (
        <React.Fragment>
            {config?.MEGAMENU_HEADER ? (
                <footer
                    className = { `${
                        interfaceConfig?.disablePrejoinFooter === true
                        && !matched
                            ? 'headerFooter'
                            : ''
                    } footer-container` }
                    id = 'footer'
                    style = { props?.style }>
                    {interfaceConfig
                        && !interfaceConfig?.disablePrejoinFooter && (
                        <>
                            <div className = 'footer-top'>
                                <div className = 'container'>
                                    <div
                                        className = 'row-footer'
                                        style = {{
                                            display: 'flex',
                                            justifyContent: 'space-around'
                                        }}>
                                        <div
                                            className = 'col-xxl-4 col-xl-4 col-lg-4 wow '
                                            data-wow-delay = '.3s'>
                                            <div className = 'footer__widget mb-50'>
                                                <div className = 'footer__widget-title mb-25'>
                                                    <div className = 'footer__logo'>
                                                        <a href = '/'>
                                                            <img
                                                                alt = 'Meet Hour'
                                                                src = 'images/logo.SVG'
                                                                style = {{
                                                                    width: '176px',
                                                                    height: '30px'
                                                                }} />
                                                        </a>
                                                    </div>
                                                </div>
                                                <div className = 'footer__widget-content'>
                                                    <p className = ''>
                                                        {t('welcomepage.footer.officeAddress')}
                                                    </p>
                                                    <p className = ''>
                                                        {t('welcomepage.footer.phone')}:
                                                        <a
                                                            href = 'tel:+13128009410'
                                                            style = {{
                                                                color: '#fff'
                                                            }}>
                                                            +1 (312) 800
                                                            9410
                                                        </a>
                                                    </p>
                                                    <p className = ''>
                                                        {t('welcomepage.footer.email')}:
                                                        <a
                                                            href = 'mailto:<EMAIL>'
                                                            style = {{
                                                                color: '#fff'
                                                            }}>
                                                            <EMAIL>
                                                        </a>
                                                    </p>
                                                </div>

                                                <div className = 'footer__widget mt-30'>
                                                    <div className = 'footer__widget-title'>
                                                        <h3
                                                            style = {{
                                                                color: '#fff'
                                                            }}>
                                                            {t('welcomepage.footer.followUs')}
                                                        </h3>
                                                    </div>
                                                    <div className = 'footer__widget-content'>
                                                        <div className = 'footer__social social-links'>
                                                            <a
                                                                href = 'https://www.facebook.com/meethour/'
                                                                target = '_blank'>
                                                                <i className = 'bx bxl-facebook' />
                                                            </a>

                                                            <a
                                                                href = 'https://twitter.com/MeetHourApp/'
                                                                target = '_blank'>
                                                                <svg
                                                                    height = '14'
                                                                    style = {{
                                                                        verticalAlign: 'text-top'
                                                                    }}
                                                                    viewBox = '0 0 512 512'
                                                                    width = '14'
                                                                    xmlns = 'http://www.w3.org/2000/svg'>
                                                                    <path
                                                                        d = 'M389.2 48h70.6L305.6 224.2 487 464H345L233.7 318.6 106.5 464H35.8L200.7 275.5 26.8 48H172.4L272.9 180.9 389.2 48zM364.4 421.8h39.1L151.1 88h-42L364.4 421.8z'
                                                                        fill = '#ffffff' /></svg>
                                                            </a>

                                                            <a
                                                                href = 'https://www.instagram.com/MeetHourApp/'
                                                                target = '_blank'>
                                                                <i className = 'bx bxl-instagram' />
                                                            </a>

                                                            <a
                                                                href = 'https://www.linkedin.com/company/meethour/'
                                                                target = '_blank'>
                                                                <i className = 'bx bxl-linkedin' />
                                                            </a>

                                                            <a
                                                                href = 'https://www.youtube.com/channel/UCbB-py_vQt2lXJ1VMMVIH7w'
                                                                target = '_blank'>
                                                                <i className = 'bx bxl-youtube' />
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div className = 'footer__widget mt-30'>
                                                    <img
                                                        alt = 'secure ssl'
                                                        className = 'mr-10'
                                                        src = '/images/secure-ssl.png'
                                                        style = {{
                                                            width: '137px',
                                                            height: '70px'
                                                        }} />
                                                    <img
                                                        alt = 'secure payment'
                                                        className = 'mr-10'
                                                        src = '/images/secure-payment.png'
                                                        style = {{
                                                            width: '137px',
                                                            height: '70px'
                                                        }} />
                                                    <img
                                                        alt = 'data privacy'
                                                        src = '/images/data-privacy.png'
                                                        style = {{
                                                            width: '70px',
                                                            height: '70px'
                                                        }} />
                                                </div>
                                            </div>
                                        </div>

                                        <div
                                            className = 'col-xxl-2 col-xl-2 col-lg-2 wow '
                                            data-wow-delay = '.5s'>
                                            <div className = 'footer__widget mb-50'>
                                                <div className = 'footer__widget-title mb-10'>
                                                    <h3
                                                        style = {{
                                                            color: '#fff'
                                                        }}>
                                                        {t('welcomepage.header.products')}
                                                    </h3>
                                                </div>
                                                <div className = 'footer__widget-content'>
                                                    <div className = 'footer__link'>
                                                        <a
                                                            href = '/products/video-conference'
                                                            style = {{
                                                                color: '#fff'
                                                            }}>
                                                            {t('welcomepage.header.videoConference')}
                                                        </a>
                                                        <hr
                                                            style = {{
                                                                backgroundColor:
                                                                        'hsla(0, 0%, 100%, .2)'
                                                            }} />
                                                        <div>
                                                            <a
                                                                href = '/products/video-conference/features'
                                                                style = {{
                                                                    color: '#fff'
                                                                }}>
                                                                {t('welcomepage.header.features')}
                                                            </a>
                                                        </div>
                                                        <br />
                                                        <div>
                                                            <a
                                                                href = '/products/video-conference/pricing'
                                                                style = {{
                                                                    color: '#fff'
                                                                }}>
                                                                {t('welcomepage.header.pricing')}
                                                            </a>
                                                        </div>
                                                        <hr
                                                            style = {{
                                                                backgroundColor:
                                                                        'hsla(0, 0%, 100%, .2)'
                                                            }} />
                                                        <div>
                                                            <a
                                                                href = '/products/video-conference/free'
                                                                style = {{
                                                                    color: '#fff'
                                                                }}>
                                                                {t('welcomepage.freePlan', { lng: 'en' })}
                                                            </a>{' '}
                                                        </div>

                                                        <br />
                                                        <div>
                                                            <a
                                                                href = '/products/video-conference/pro'
                                                                style = {{
                                                                    color: '#fff'
                                                                }}>
                                                                {t('welcomepage.proPlan', { lng: 'en' })}
                                                            </a>{' '}
                                                        </div>

                                                        <br />
                                                        <div>
                                                            <a
                                                                href = '/products/video-conference/developer'
                                                                style = {{
                                                                    color: '#fff'
                                                                }}>
                                                                {t('welcomepage.developerPlan', { lng: 'en' })}
                                                            </a>
                                                        </div>

                                                        <br />
                                                        <div>
                                                            <a
                                                                href = '/products/video-conference/enterprise'
                                                                style = {{
                                                                    color: '#fff'
                                                                }}>
                                                                {t('welcomepage.enterprisePlan', { lng: 'en' })}
                                                            </a>
                                                        </div>

                                                        <br />
                                                        <div>
                                                            <a
                                                                href = '/products/video-conference/enterprise-selfhost'
                                                                style = {{
                                                                    color: '#fff'
                                                                }}>
                                                                {t('welcomepage.enterpriseSelfHostPlan', { lng: 'en' })}
                                                            </a>
                                                        </div>

                                                        <hr
                                                            style = {{
                                                                backgroundColor:
                                                                        'hsla(0, 0%, 100%, .2)'
                                                            }} />

                                                        <div>
                                                            <a
                                                                href = 'https://mycaly.io/'
                                                                style = {{
                                                                    color: '#fff'
                                                                }}
                                                                target = '_blank'>
                                                                {t('welcomepage.header.myCaly', { lng: 'en' })}
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div
                                            className = 'col-xxl-2 col-xl-2 col-lg-2 wow '
                                            data-wow-delay = '.5s'>
                                            <div className = 'footer__widget mb-50'>
                                                <div className = 'footer__widget-title mb-10'>
                                                    <h3
                                                        style = {{
                                                            color: '#fff'
                                                        }}>
                                                        {t('welcomepage.header.solutions')}
                                                    </h3>
                                                </div>
                                                <div className = 'footer__widget-content'>
                                                    <div className = 'footer__link'>
                                                        <div>
                                                            <a
                                                                href = '/solutions/usecases/videoconferencing'
                                                                style = {{
                                                                    color: '#fff'
                                                                }}>
                                                                {t('welcomepage.header.videoConferencing')}
                                                            </a>
                                                        </div>

                                                        <br />

                                                        <div>
                                                            <a
                                                                href = '/solutions/usecases/livestreaming'
                                                                style = {{
                                                                    color: '#fff'
                                                                }}>
                                                                {t('welcomepage.header.liveStreaming')}
                                                            </a>
                                                        </div>

                                                        <br />
                                                        <div>
                                                            <a
                                                                href = '/solutions/usecases/virtualclassrooms'
                                                                style = {{
                                                                    color: '#fff'
                                                                }}>
                                                                {t('welcomepage.header.virtualClassrooms')}
                                                            </a>
                                                        </div>

                                                        <br />
                                                        <div>
                                                            <a
                                                                href = '/solutions/usecases/virtualevents'
                                                                style = {{
                                                                    color: '#fff'
                                                                }}>
                                                                {t('welcomepage.header.virtualEvents')}
                                                            </a>
                                                        </div>

                                                        <br />
                                                        <div>
                                                            <a
                                                                href = '/solutions/usecases/ekyc'
                                                                style = {{
                                                                    color: '#fff'
                                                                }}>
                                                                {t('welcomepage.header.videoKYC')}
                                                            </a>
                                                        </div>

                                                        <br />
                                                        <div>
                                                            <a
                                                                href = '/solutions/usecases/webinars'
                                                                style = {{
                                                                    color: '#fff'
                                                                }}>
                                                                {t('welcomepage.header.webinars')}
                                                            </a>
                                                        </div>

                                                        <br />
                                                        <div>
                                                            <a
                                                                href = '/solutions/usecases/fundraising'
                                                                style = {{
                                                                    color: '#fff'
                                                                }}>
                                                                {t('welcomepage.header.fundraisingDonate')}
                                                            </a>
                                                        </div>

                                                        <hr
                                                            style = {{
                                                                backgroundColor:
                                                                        'hsla(0, 0%, 100%, .2)'
                                                            }} />
                                                        <div>
                                                            <a
                                                                href = '/solutions/industries/edtech'
                                                                style = {{
                                                                    color: '#fff'
                                                                }}>
                                                                {t('welcomepage.header.edTech')}
                                                            </a>
                                                        </div>

                                                        <br />
                                                        <div>
                                                            <a
                                                                href = '/solutions/industries/fitness'
                                                                style = {{
                                                                    color: '#fff'
                                                                }}>
                                                                {t('welcomepage.header.fitness')}
                                                            </a>
                                                        </div>

                                                        <br />
                                                        <div>
                                                            <a
                                                                href = '/solutions/industries/healthcare'
                                                                style = {{
                                                                    color: '#fff'
                                                                }}>
                                                                {t('welcomepage.header.telehealth')}
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div
                                            className = 'col-xxl-2 col-xl-2 col-lg-2 wow '
                                            data-wow-delay = '.5s'>
                                            <div className = 'footer__widget mb-50'>
                                                <div className = 'footer__widget-title mb-10'>
                                                    <h3
                                                        style = {{
                                                            color: '#fff'
                                                        }}>
                                                        {t('welcomepage.footer.company')}
                                                    </h3>
                                                </div>
                                                <div className = 'footer__widget-content'>
                                                    <div className = 'footer__link'>
                                                        <div>
                                                            <a
                                                                href = '/whoweare'
                                                                style = {{
                                                                    color: '#fff'
                                                                }}>
                                                                {t('welcomepage.footer.whoAreYou')}
                                                            </a>
                                                        </div>

                                                        <br />
                                                        <div>
                                                            <a
                                                                href = '/inthenews'
                                                                style = {{
                                                                    color: '#fff'
                                                                }}>
                                                                {t('welcomepage.footer.inTheNews')}
                                                            </a>
                                                        </div>

                                                        <br />
                                                        <div>
                                                            <a
                                                                href = 'https://testimonials.meethour.io/all'
                                                                style = {{
                                                                    color: '#fff'
                                                                }}
                                                                target = '_Blank'>
                                                                {t('welcomepage.footer.testimonials')}
                                                            </a>
                                                        </div>

                                                        <br />
                                                        <div>
                                                            <a
                                                                href = '/contact'
                                                                style = {{
                                                                    color: '#fff'
                                                                }}>
                                                                {t('welcomepage.footer.contact')}
                                                            </a>
                                                        </div>

                                                        <br />
                                                        <div>
                                                            <a
                                                                href = 'https://meethour.tawk.help/'
                                                                style = {{
                                                                    color: '#fff'
                                                                }}
                                                                target = '_blank'>
                                                                {t('welcomepage.footer.helpDesk')}
                                                            </a>
                                                        </div>

                                                        <br />
                                                        <div>
                                                            <a
                                                                href = '/faqs'
                                                                style = {{
                                                                    color: '#fff'
                                                                }}>
                                                                {t('welcomepage.footer.faqs')}
                                                            </a>
                                                        </div>

                                                        <br />
                                                        <div>
                                                            <a
                                                                href = 'http://kb.meethour.io/category/how-to'
                                                                style = {{
                                                                    color: '#fff'
                                                                }}
                                                                target = '_Blank'>
                                                                {t('welcomepage.footer.knowledgeBase')}
                                                            </a>
                                                        </div>

                                                        <br />
                                                        <div>
                                                            <a
                                                                href = '/blog'
                                                                style = {{
                                                                    color: '#fff'
                                                                }}>
                                                                {t('welcomepage.footer.blog')}
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div
                                            className = 'col-xxl-2 col-xl-2 col-lg-2  wow '
                                            data-wow-delay = '.7s'>
                                            <div className = 'footer__widget mb-50'>
                                                <div className = 'footer__widget-title mb-10'>
                                                    <h3
                                                        style = {{
                                                            color: '#fff'
                                                        }}>
                                                        {t('welcomepage.footer.app')}
                                                    </h3>
                                                </div>
                                                <div className = 'footer__widget-content'>
                                                    <div className = 'footer__link'>
                                                        <div>
                                                            <a
                                                                href = '/meethour-download'
                                                                style = {{
                                                                    color: '#fff'
                                                                }}>
                                                                {t('welcomepage.footer.download')}
                                                            </a>
                                                        </div>

                                                        <br />
                                                        <div>
                                                            <a
                                                                href = '/integration'
                                                                style = {{
                                                                    color: '#fff'
                                                                }}>
                                                                {t('welcomepage.footer.integrations')}
                                                            </a>
                                                        </div>

                                                        <br />
                                                        <div>
                                                            <a
                                                                href = '/developers'
                                                                style = {{
                                                                    color: '#fff'
                                                                }}>
                                                                {t('welcomepage.footer.developers')}
                                                            </a>
                                                        </div>

                                                        <br />

                                                        <div>
                                                            <a
                                                                href = 'https://bit.ly/meethour-ppt'
                                                                style = {{
                                                                    color: '#fff'
                                                                }}
                                                                target = '_Blank'>
                                                                {t('welcomepage.footer.productPresentation')}
                                                            </a>
                                                        </div>

                                                        <br />
                                                        <div>
                                                            <a
                                                                href = 'https://docs.v-empower.com/docs/MeetHour-API'
                                                                style = {{
                                                                    color: '#fff'
                                                                }}
                                                                target = '_Blank'>
                                                                {t('welcomepage.footer.apiDocumentation')}
                                                            </a>
                                                        </div>

                                                        <br />
                                                        <div>
                                                            <a
                                                                href = '/sdks'
                                                                style = {{
                                                                    color: '#fff'
                                                                }}>
                                                                {t('welcomepage.footer.webMobileSDK')}
                                                            </a>
                                                        </div>

                                                        <br />
                                                        <div>
                                                            <a
                                                                href = '/disclaimer'
                                                                style = {{
                                                                    color: '#fff'
                                                                }}>
                                                                {t('welcomepage.footer.disclaimer')}
                                                            </a>
                                                        </div>

                                                        <br />
                                                        <div>
                                                            <a
                                                                href = '/privacypolicy'
                                                                style = {{
                                                                    color: '#fff'
                                                                }}>
                                                                {t('welcomepage.footer.privacyPolicy')}
                                                            </a>
                                                        </div>

                                                        <br />
                                                        <div>
                                                            <a
                                                                href = '/termsandconditions'
                                                                style = {{
                                                                    color: '#fff'
                                                                }}>
                                                                {t('welcomepage.footer.termsConditions')}
                                                            </a>
                                                        </div>

                                                        <br />
                                                        <div>
                                                            <a
                                                                href = '/refundandcancellation'
                                                                style = {{
                                                                    color: '#fff'
                                                                }}>
                                                                {t('welcomepage.footer.refundCancellationPolicy')}
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div
                                className = 'container d-md-flex py-4 justify-content-center'
                                style = {{
                                    borderTop:
                                            '1px solid hsla(0,0%,100%,.1)'
                                }}>
                                <div className = 'copyright'>
                                    <strong>
                                        <span>
                                            &copy; {t('welcomepage.footer.copyrightText')}
                                        </span>
                                    </strong>
                                </div>
                                <div className = 'social-links text-center text-md-right pt-3 pt-md-0'>
                                    {/* <a
                        className = 'twitter'
                        href = '#' >
                        <Icon
                            className = { '' }
                            size = { 20 }
                            src = { IconTwitter } />
                    </a>
                    <a
                        className = 'facebook'
                        href = '#'>                        <Icon
                            className = { '' }
                            size = { 20 }
                            src = { IconFacebook } /></a>
                    <a
                        className = 'instagram'
                        href = '#'><Icon
                            className = { '' }
                            size = { 20 }
                            src = { IconInstagram } /></a>
                    <a
                        className = 'google-plus'
                        href = '#'><Icon
                            className = { '' }
                            size = { 20 }
                            src = { IconYoutube } /></a>
                    <a
                        className = 'linkedin'
                        href = '#'><Icon
                            className = { '' }
                            size = { 20 }
                            src = { IconLinkedin } /></a> */}
                                </div>
                            </div>
                        </>
                    )}
                    {interfaceConfig
                        && interfaceConfig?.disablePrejoinFooter && (
                        <div
                            className = 'container'
                            style = { styleWeb.disablePrejoinFooter }>
                            <h3 style = { h3Style }>
                                {interfaceConfig.APP_NAME}
                                {interfaceConfig.SHOW_POWERED_BY
                                        && <span>. Powered by Meet Hour</span>
                                }
                            </h3>
                        </div>
                    )}
                </footer>
            ) : (
                <footer
                    className = {
                        interfaceConfig?.disablePrejoinFooter === true
                        && !matched
                            ? 'headerFooter'
                            : ''
                    }
                    id = 'footer'
                    style = { props?.style }>
                    {interfaceConfig
                        && !interfaceConfig?.disablePrejoinFooter && (
                        <>
                            <div className = 'footer-top'>
                                <div className = 'container'>
                                    <div className = 'row'>
                                        <div className = 'col-lg-3 col-md-6  footer-contact'>
                                            <h3 style = { colorWhite }>
                                                {t('welcomepage.footer.meethourLLC', { lng: 'en' })}<span>.</span>
                                            </h3>
                                            <p>
                                                {t('welcomepage.footer.androidAppDofficeAddressownload')}
                                                <br />
                                                <br />
                                                <br />
                                                <strong>{t('welcomepage.footer.phone')}:</strong>
                                                <a href = 'tel:+13128009410'>
                                                    +****************
                                                </a>
                                                <br />
                                                <strong>{t('welcomepage.footer.email')}:</strong>
                                                <a href = 'mailto:<EMAIL>'>
                                                    <EMAIL>
                                                </a>
                                                <br />
                                            </p>
                                        </div>

                                        <div className = 'col-lg-2 col-md-6  footer-links'>
                                            <h4> {t('welcomepage.footer.company')}</h4>
                                            <ul>
                                                <li>
                                                    <i className = 'bx bx-chevron-right' />
                                                    <a href = '/who-we-are.html'>
                                                        {t('welcomepage.footer.whoAreYou')}
                                                    </a>
                                                </li>
                                                <li>
                                                    <i className = 'bx bx-chevron-right' />
                                                    <a href = '#features'>
                                                        {t('welcomepage.header.features')}
                                                    </a>
                                                </li>
                                                <li>
                                                    <i className = 'bx bx-chevron-right' />
                                                    <a href = '#pricing'>
                                                        {t('welcomepage.header.pricing')}
                                                    </a>
                                                </li>
                                                <li>
                                                    <i className = 'bx bx-chevron-right' />{' '}
                                                    <a
                                                        href = 'https://testimonials.meethour.io/all'
                                                        // eslint-disable-next-line react/jsx-no-target-blank
                                                        target = '_blank'>
                                                        {t('welcomepage.footer.testimonials')}
                                                    </a>
                                                </li>

                                                <li>
                                                    <i className = 'bx bx-chevron-right' />
                                                    <a href = '/news.html'>
                                                        {t('welcomepage.footer.inTheNews')}
                                                    </a>
                                                </li>
                                                <li>
                                                    <i className = 'bx bx-chevron-right' />
                                                    <a href = '/contact.html'>
                                                        {t('welcomepage.footer.contact')}
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>

                                        <div className = 'col-lg-3 col-md-6 footer-links'>
                                            <h4> {t('welcomepage.footer.app')}</h4>
                                            <ul>
                                                <li>
                                                    <i className = 'bx bx-chevron-right' />{' '}
                                                    <a
                                                        href = 'https://apps.apple.com/in/app/meet-hour/id1527001689'
                                                        rel = 'noopener noreferrer'
                                                        target = '_blank'>
                                                        {t('welcomepage.footer.iOSAppDownload')}
                                                    </a>
                                                </li>
                                                <li>
                                                    <i className = 'bx bx-chevron-right' />{' '}
                                                    <a
                                                        href = 'https://play.google.com/store/apps/details?id=go.meethour.io'
                                                        rel = 'noopener noreferrer'
                                                        target = '_blank'>
                                                        {t('welcomepage.footer.androidAppDownload')}
                                                    </a>
                                                </li>

                                                <li>
                                                    <i className = 'bx bx-chevron-right' />
                                                    <a href = '/disclaimer.html'>
                                                        {t('welcomepage.footer.disclaimer')}
                                                    </a>
                                                </li>
                                                <li>
                                                    <i className = 'bx bx-chevron-right' />
                                                    <a href = '/privacy-policy.html'>
                                                        {t('welcomepage.footer.privacyPolicy')}
                                                    </a>
                                                </li>
                                                <li>
                                                    <i className = 'bx bx-chevron-right' />
                                                    <a href = '/terms-conditions.html'>
                                                        {t('welcomepage.footer.termsConditions')}
                                                    </a>
                                                </li>
                                                <li>
                                                    <i className = 'bx bx-chevron-right' />
                                                    <a href = '/FAQ.html'>
                                                        {t('welcomepage.footer.faqs')}
                                                    </a>
                                                </li>
                                                <li>
                                                    <i className = 'bx bx-chevron-right' />{' '}
                                                    <a
                                                        href = 'https://docs.v-empower.com/docs/MeetHour-API '
                                                        target = { '_blank' }>
                                                        {t('welcomepage.footer.apiDocumentation')}
                                                    </a>
                                                </li>
                                                <li>
                                                    <i className = 'bx bx-chevron-right' />{' '}
                                                    <a
                                                        href = 'https://github.com/v-empower/MeetHour-Web-MobileSDKs'
                                                        target = { '_blank' }>
                                                        {t('welcomepage.footer.webMobileSDK')}
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>

                                        <div className = 'col-lg-4 col-md-6 footer-newsletter'>
                                            <img
                                                className = 'img-fluid'
                                                src = 'images/footer-img.png' />
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div className = 'container d-md-flex py-4'>
                                <div className = 'mr-md-auto text-center text-md-left'>
                                    <div className = 'copyright'>
                                        &copy; {t('welcomepage.footer.copyright')}{' '}
                                        <strong>
                                            <span>{t('welcomepage.footer.meethour', { lng: 'en' })}</span>
                                        </strong>
                                        . {t('welcomepage.footer.allRightsReserved')}
                                    </div>
                                </div>
                                <div className = 'social-links text-center text-md-right pt-3 pt-md-0'>
                                    {/* <a
                        className = 'twitter'
                        href = '#' >
                        <Icon
                            className = { '' }
                            size = { 20 }
                            src = { IconTwitter } />
                    </a>
                    <a
                        className = 'facebook'
                        href = '#'>                        <Icon
                            className = { '' }
                            size = { 20 }
                            src = { IconFacebook } /></a>
                    <a
                        className = 'instagram'
                        href = '#'><Icon
                            className = { '' }
                            size = { 20 }
                            src = { IconInstagram } /></a>
                    <a
                        className = 'google-plus'
                        href = '#'><Icon
                            className = { '' }
                            size = { 20 }
                            src = { IconYoutube } /></a>
                    <a
                        className = 'linkedin'
                        href = '#'><Icon
                            className = { '' }
                            size = { 20 }
                            src = { IconLinkedin } /></a> */}
                                    <a
                                        href = 'https://www.facebook.com/meethour/'
                                        rel = 'noopener noreferrer'
                                        target = '_blank'>
                                        <i className = 'bx bxl-facebook' />
                                    </a>
                                    <a
                                        href = 'https://twitter.com/MeetHourApp/'
                                        rel = 'noopener noreferrer'
                                        target = '_blank'>
                                        <svg
                                            height = '14'
                                            style = {{
                                                verticalAlign: 'text-top'
                                            }}
                                            viewBox = '0 0 512 512'
                                            width = '14'
                                            xmlns = 'http://www.w3.org/2000/svg'>
                                            <path
                                                d = 'M389.2 48h70.6L305.6 224.2 487 464H345L233.7 318.6 106.5 464H35.8L200.7 275.5 26.8 48H172.4L272.9 180.9 389.2 48zM364.4 421.8h39.1L151.1 88h-42L364.4 421.8z'
                                                fill = '#ffffff' /></svg>
                                    </a>
                                    <a
                                        href = 'https://www.instagram.com/MeetHourApp/'
                                        rel = 'noopener noreferrer'
                                        target = '_blank'>
                                        <i className = 'bx bxl-instagram' />
                                    </a>
                                    <a
                                        href = 'https://www.linkedin.com/company/meethour/'
                                        rel = 'noopener noreferrer'
                                        target = '_blank'>
                                        <i className = 'bx bxl-linkedin' />
                                    </a>
                                    <a
                                        href = ' https://www.youtube.com/channel/UCbB-py_vQt2lXJ1VMMVIH7w/'
                                        rel = 'noopener noreferrer'
                                        target = '_blank'>
                                        <i className = 'bx bxl-youtube' />
                                    </a>
                                </div>
                            </div>
                        </>
                    )}
                    {interfaceConfig
                        && interfaceConfig?.disablePrejoinFooter && (
                        <div
                            className = 'container'
                            style = { styleWeb.disablePrejoinFooter }>
                            <h3 style = { h3Style }>
                                {interfaceConfig.APP_NAME}
                                {interfaceConfig.SHOW_POWERED_BY
                                        && <span>. Powered by Meet Hour</span>
                                }
                            </h3>
                        </div>
                    )}
                </footer>
            )}
        </React.Fragment>
    );
};
