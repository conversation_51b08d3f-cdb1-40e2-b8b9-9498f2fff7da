/* eslint-disable */
/* global */

import { jitsiLocalStorage } from "@jitsi/js-utils";
import _ from "lodash";
import React from "react";
import MediaQuery from "react-responsive";

import { Avatar } from "../../base/avatar";
import { getURLWithoutParams } from "../../base/connection";
import { isMobileBrowser } from "../../base/environment/utils";
import { translate } from "../../base/i18n";
import { Icon, IconWarning } from "../../base/icons";
import { connect } from "../../base/redux";
import { CalendarList } from "../../calendar-sync";
import { RecentList } from "../../recent-list";

import {
    AbstractWelcomePage,
    _mapStateToProps,
    _mapDispatchToProps,
} from "./AbstractWelcomePage";
import { Header } from "./Header";
import Tabs from "./Tabs";
import { styleWeb } from "./stylesWeb";
import LanguagesDropdown from "../../languages-component/components/LanguagesDropdown";

declare var config: object;
declare var interfaceConfig: object;

/**
 * The pattern used to validate room name.
 * @type {string}
 */
export const ROOM_NAME_VALIDATE_PATTERN_STR = "^[^?&:\u0022\u0027%#]+$";

/**
 * Maximum number of pixels corresponding to a mobile layout.
 * @type {number}
 */
const WINDOW_WIDTH_THRESHOLD = 425;

const styleWelcome = {
    marginLeft: { marginLeft: "17px" },
    width100: { width: "100%" },
    displayNone: { display: "none" },
};

/**
 * The Web container rendering the welcome page.
 *
 * @extends AbstractWelcomePage
 */
class WelcomePage extends AbstractWelcomePage {
    /**
     * Initializes a new WelcomePage instance.
     *
     * @param {Object} props - The read-only properties with which the new
     * instance is to be initialized.
     */
    constructor(props) {
        super(props);

        this.state = {
            ...this.state,
            accessTokn: null,
            generateRoomnames:
                interfaceConfig.GENERATE_ROOMNAMES_ON_WELCOME_PAGE,
            selectedTab: 0,
        };

        /**
         * The HTML Element used as the container for additional content. Used
         * for directly appending the additional content template to the dom.
         *
         * @private
         * @type {HTMLTemplateElement|null}
         */
        this._additionalContentRef = null;

        this._roomInputRef = null;

        /**
         * The HTML Element used as the container for additional toolbar content. Used
         * for directly appending the additional content template to the dom.
         *
         * @private
         * @type {HTMLTemplateElement|null}
         */
        this._additionalToolbarContentRef = null;

        this._additionalCardRef = null;

        /**
         * The template to use as the additional card displayed near the main one.
         *
         * @private
         * @type {HTMLTemplateElement|null}
         */
        this._additionalCardTemplate = document.getElementById(
            "welcome-page-additional-card-template"
        );

        /**
         * The template to use as the main content for the welcome page. If
         * not found then only the welcome page head will display.
         *
         * @private
         * @type {HTMLTemplateElement|null}
         */
        this._additionalContentTemplate = document.getElementById(
            "welcome-page-additional-content-template"
        );

        /**
         * The template to use as the additional content for the welcome page header toolbar.
         * If not found then only the settings icon will be displayed.
         *
         * @private
         * @type {HTMLTemplateElement|null}
         */
        this._additionalToolbarContentTemplate = document.getElementById(
            "settings-toolbar-additional-content-template"
        );

        // Bind event handlers so they are only bound once per instance.
        this._onFormSubmit = this._onFormSubmit.bind(this);
        this._onRoomChange = this._onRoomChange.bind(this);
        this._setAdditionalCardRef = this._setAdditionalCardRef.bind(this);
        this._setAdditionalContentRef =
            this._setAdditionalContentRef.bind(this);
        this._setRoomInputRef = this._setRoomInputRef.bind(this);
        this._setAdditionalToolbarContentRef =
            this._setAdditionalToolbarContentRef.bind(this);
        this._onTabSelected = this._onTabSelected.bind(this);
        this._renderFooter = this._renderFooter.bind(this);
        this._onLogOutClicked = this._onLogOutClicked.bind(this);
        this._onOpenNavbar = this._onOpenNavbar.bind(this);
    }

    /**
     * Implements React's {@link Component#componentDidMount()}. Invoked
     * immediately after this component is mounted.
     *
     * @inheritdoc
     * @returns {void}
     */
    componentDidMount() {
        super.componentDidMount();

        document.body.classList.add("welcome-page");

        if (this._shouldShowAdditionalContent()) {
            this._additionalContentRef.appendChild(
                this._additionalContentTemplate.content.cloneNode(true)
            );
        }

        if (this._shouldShowAdditionalToolbarContent()) {
            this._additionalToolbarContentRef.appendChild(
                this._additionalToolbarContentTemplate.content.cloneNode(true)
            );
        }

        if (this._shouldShowAdditionalCard()) {
            this._additionalCardRef.appendChild(
                this._additionalCardTemplate.content.cloneNode(true)
            );
        }

        // if (!jwt && (!preRegData.success || !isTrueGuest)) {
        //     return window.location.replace('https://localhost:8080');
        // }
        const ele = document.getElementById("preloader");

        // console.log('preRegDatapreRegData',urrr);
        const accessTkn = jitsiLocalStorage.getItem("accessToken");
        const urlwithout = getURLWithoutParams(this.props._locationURL);

        window.history.replaceState(
            history.state,
            (document && document.title) || "",
            urlwithout
        );
        if (ele && _.isNil(accessTkn)) {
            setTimeout(() => {
                ele.style.opacity = "0";
            }, 1000);
        }
        if (this.state.generateRoomnames) {
            this._updateRoomname();
        }
    }

    /**
     * Removes the classname used for custom styling of the welcome page.
     *
     * @inheritdoc
     * @returns {void}
     */
    componentWillUnmount() {
        super.componentWillUnmount();

        document.body.classList.remove("welcome-page");
    }

    /**
     * Replaces the windows location to the depending parameter.
     *
     * @inheritdoc
     * @param {string} param - The name of the class.
     * @returns {void}
     */
    _windowLocationReplace = (param) => window.location.replace(`${param}`);

    _onLogOutClicked = (e) => {
        e.preventDefault();
        const userAccessToken = jitsiLocalStorage.getItem("accessToken");
        // eslint-disable-next-line no-invalid-this
        const { _setuserDetails, _updateSettings, accessToken } = this.props;
        const ele =
            document.getElementById("preloader") ??
            document.getElementById("preloader1");

        if (ele) {
            ele.style.opacity = "100%";
            ele.style.display = "block";
        }
        jitsiLocalStorage.clear();
        window.location.replace(
            `${config.URL_PREFIX}serviceLogin?client_id=${
                config.MH_CLIENT_ID
            }&redirect_uri=${
                window.location.href
            }&device_type=web&response_type=get&access_token=${
                accessToken ?? userAccessToken
                    ? `&access_token=${accessToken ?? userAccessToken}`
                    : ""
            }&logout=true`
        );
        _setuserDetails(undefined);
        _updateSettings({
            displayName: undefined,
            email: undefined,
        });
    };

    /**
     * Replaces the windows location to the depending parameter.
     *
     * @inheritdoc
     * @param {string} param - The name of the class.
     * @returns {void}
     */
    _onOpenNavbar() {
        this.setState({ openNav: !this.state.openNav });
        const menuNav = document.querySelector(".mobile-nav-overly");
        const menuNav1 = document.querySelector(".mobile-nav");
        const meen = document.querySelector(".mobile-nav-toggle i");

        if (meen.classList.contains("icofont-close")) {
            menuNav.style.display = "none";
            document.body.classList.remove("mobile-nav-active");
            meen.classList.remove("icofont-close");
            meen.classList.add("icofont-navigation-menu");

            return;
        }
        if (menuNav) {
            menuNav.style.display = "block";
        }
        if (menuNav1) {
            document.body.classList.add("mobile-nav-active");
        }
        if (meen) {
            meen.classList.remove("icofont-navigation-menu");
            meen.classList.add("icofont-close");
        }
    }

    /**
     * Implements React's {@link Component#render()}.
     *
     * @inheritdoc
     * @returns {ReactElement|null}
     */
    render() {
        const { userDetails, name, accessToken, _room, t } = this.props;
        const userAccessToken = jitsiLocalStorage.getItem("accessToken");

        // const { APP_NAME, DEFAULT_WELCOME_PAGE_LOGO_URL } = interfaceConfig;
        const showAdditionalContent = this._shouldShowAdditionalContent();
        const loginButtonJSx = (
            <>
                <li className="signin-li">
                    <a
                        href={`${config.URL_PREFIX}serviceLogin?client_id=${
                            config.MH_CLIENT_ID
                        }&redirect_uri=${window.location.href}${
                            _room
                                ? "%23interfaceConfig.ENABLE_DESKTOP_DEEPLINK%3Dfalse"
                                : ""
                        }&device_type=web&response_type=get${
                            _room ? `&meeting_id=${_room}` : ""
                        }`}
                    >
                        {t('prejoin.signinsignup')}
                    </a>
                </li>
            </>
        );

        // const showAdditionalToolbarContent = this._shouldShowAdditionalToolbarContent();
        // const showResponsiveText = this._shouldShowResponsiveText();

        let logoutDocument;

        if (userDetails) {
            const userd = userDetails;

            logoutDocument = (
                <li className="drop-down" style={styleWelcome.marginLeft}>
                    {userd && userd?.picture ? (
                        <img
                            className="avatar"
                            src={userd?.picture}
                            style={styleWeb.widthandHeightimg}
                        />
                    ) : (
                        <Avatar
                            className="premeeting-screen-avatar"
                            displayName={name}
                            dynamicColor={true}
                            participantId="local"
                            size={45}
                        />
                    )}

                    <a href="">{userd ? userd?.name : ""}</a>
                    <ul className="drop-down1">
                        <li>
                            <a
                                href={`${
                                    config.URL_PREFIX
                                }serviceLogin?client_id=${
                                    config.MH_CLIENT_ID
                                }&redirect_uri=${
                                    config.URL_PREFIX
                                }customer/dashboard&device_type=web${
                                    accessToken ?? userAccessToken
                                        ? `&access_token=${
                                              accessToken ?? userAccessToken
                                          }`
                                        : ""
                                }`}
                            >
                                <i className="bx bxs-dashboard" />{t('prejoin.dashboard')}
                            </a>
                        </li>
                        <li>
                            <a
                                href={`${
                                    config.URL_PREFIX
                                }serviceLogin?client_id=${
                                    config.MH_CLIENT_ID
                                }&redirect_uri=${
                                    config.URL_PREFIX
                                }customer/profile&device_type=web${
                                    accessToken ?? userAccessToken
                                        ? `&access_token=${
                                              accessToken ?? userAccessToken
                                          }`
                                        : ""
                                }`}
                            >
                                <i className="bx bx-user" /> {t('prejoin.profile')}
                            </a>{" "}
                        </li>
                        <li>
                            <a
                                onClick={this._onLogOutClicked}
                                style={styleWeb.cusrorPointer}
                            >
                                <i className="bx bx-log-out" /> {t('prejoin.logout')}
                            </a>
                        </li>
                    </ul>
                </li>
            );
        } else {
            logoutDocument = loginButtonJSx;
        }

        return (
            <React.Fragment>
                <div id="preloader" style={{ backgroundColor: "#405170" }} />
                <MediaQuery maxWidth={1000}>
                    <button
                        className="mobile-nav-toggle d-lg-none"
                        onClick={this._onOpenNavbar}
                        type="button"
                    >
                        <i className="icofont-navigation-menu" />
                    </button>
                </MediaQuery>
                <div className="front-page">
                    <Header />
                    <section id="hero">
                        <div className="container">
                            <div className="row">
                                <div className="col-lg-7 col-sm-12">
                                    <h1 style={styleWeb.h1w}>
                                    {t('welcomepage.shiftingVirtualMeetToReality')}
                                    </h1>

                                    <h6
                                        className="banner-description"
                                        style={styleWeb.bannerh1}
                                    >
                                        <br /> {t('welcomepage.freeBannerDescription')}
                                    </h6>
                                    {_.isNil(userDetails) ? (
                                        <button
                                            className="btn btn-primary start-btn"
                                            // eslint-disable-next-line react/jsx-no-bind
                                            onClick={this._windowLocationReplace.bind(
                                                this,
                                                `${config.URL_PREFIX}login`
                                            )}
                                            type="submit"
                                        >
                                            {t('welcomepage.tryNowItsFree')}
                                        </button>
                                    ) : (
                                        <button
                                            className="btn btn-primary start-btn"
                                            // eslint-disable-next-line react/jsx-no-bind
                                            onClick={this._windowLocationReplace.bind(
                                                this,
                                                `${config.URL_PREFIX}meetings/create`
                                            )}
                                            type="submit"
                                        >
                                           {t('welcomepage.scheduleAMeeting')}
                                        </button>
                                    )}
                                </div>

                                <div className="col-lg-5 col-sm-12">
                                    <img
                                        alt=""
                                        className="img-fluid"
                                        src="images/new-banner-image.png"
                                    />
                                </div>
                            </div>
                        </div>
                        <svg
                            className="hero-waves"
                            preserveAspectRatio="none"
                            viewBox="0 24 150 28 "
                            xmlns="http://www.w3.org/2000/svg"
                            xmlnsXlink="http://www.w3.org/1999/xlink"
                        >
                            <defs>
                                <path
                                    d="M-160 44c30 0 58-18 88-18s 58 18 88 18 58-18 88-18 58 18 88 18 v44h-352z"
                                    id="wave-path"
                                />
                            </defs>
                            <g className="wave1">
                                <use
                                    fill="rgba(255,255,255, .1)"
                                    x="50"
                                    xlinkHref="#wave-path"
                                    y="3"
                                />
                            </g>
                            <g className="wave2">
                                <use
                                    fill="rgba(255,255,255, .2)"
                                    x="50"
                                    xlinkHref="#wave-path"
                                    y="0"
                                />
                            </g>
                            <g className="wave3">
                                <use
                                    fill="#fff"
                                    x="50"
                                    xlinkHref="#wave-path"
                                    y="9"
                                />
                            </g>
                        </svg>
                        {!isMobileBrowser() && (
                            <MediaQuery minWidth={1000}>
                                <div className="background-holder">
                                    <video
                                        autoPlay="autoplay"
                                        id="video"
                                        loop="loop"
                                        muted={true}
                                        onEnded="this.play()"
                                        playsInline=""
                                        poster="images/video-poster.png?v=26022023"
                                        style={styleWeb.backGroundVideo}
                                    >
                                        <source
                                            src="images/meet-hour-video.mp4?v=26022023"
                                            type="video/mp4"
                                        />
                                        <source
                                            src="images/meet-hour-video.webm?v=26022023"
                                            type="video/webm"
                                        />
                                        <source
                                            src="images/meet-hour-video.ogv?v=26022023"
                                            type="video/ogv"
                                        />
                                    </video>
                                </div>
                            </MediaQuery>
                        )}
                    </section>
                </div>

                {showAdditionalContent ? (
                    <div
                        className="welcome-page-content"
                        ref={this._setAdditionalContentRef}
                    />
                ) : null}

                <nav
                    className="mobile-nav d-lg-none"
                    style={styleWelcome.width100}
                >
                    <ul className="float-left-desktop">
                        <li>
                            <a href="#features"> {t('welcomepage.header.features')}</a>
                        </li>
                        <li className="drop-down">
                            <a href=""> {t('welcomepage.header.solutions')}</a>
                            <ul className="drop-down1">
                                <li>
                                    <a href="/explore/hospitals.html">
                                        {t('welcomepage.forHospitals')}
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li className="drop-down">
                            <a href="#pricing">{t('welcomepage.header.pricing')}</a>
                            <ul className="drop-down1">
                                <li>
                                    <a
                                        href="https://meethour.io/#pricing"
                                    >
                                        {t('welcomepage.freePlan')}
                                    </a>
                                    <a
                                        href="https://repo.meethour.io/landing/pro.html?location=meethour_homepage_pricing_menu"
                                        target="_blank"
                                    >
                                        {t('welcomepage.proPlan')}
                                    </a>
                                    <a
                                        href="https://repo.meethour.io/landing/developer.html?location=meethour_homepage_pricing_menu"
                                        target="_blank"
                                    >
                                        {t('welcomepage.developerPlan')}
                                    </a>
                                    <a
                                        href="https://repo.meethour.io/landing/enterprise.html?location=meethour_homepage_pricing_menu"
                                        target="_blank"
                                    >
                                        {t('welcomepage.enterpriseSelfHostPlan')}
                                    </a>
                                </li>
                            </ul>
                        </li>
                    </ul>
                    <ul className="float-right-desktop">
                    <li
                                            style = { styleWeb.languageContainer }>
                                                <LanguagesDropdown />
                                        </li>
                        <li className='d-flex align-items-center' style={styleWeb.headerliRight}>
                            <a href="/joinmeeting">{t('welcomepage.header.joinAMeeting')}</a>
                        </li>
                        {logoutDocument}
                    </ul>
                </nav>
                <div
                    className="mobile-nav-overly"
                    style={styleWelcome.displayNone}
                />
            </React.Fragment>
        );
    }

    /**
     * Renders the insecure room name warning.
     *
     * @inheritdoc
     */
    _doRenderInsecureRoomNameWarning() {
        return (
            <div className="insecure-room-name-warning">
                <Icon src={IconWarning} />
                <span>{this.props.t("security.insecureRoomNameWarning")}</span>
            </div>
        );
    }

    /**
     * Prevents submission of the form and delegates join logic.
     *
     * @param {Event} event - The HTML Event which details the form submission.
     * @private
     * @returns {void}
     */
    _onFormSubmit(event) {
        event.preventDefault();

        if (!this._roomInputRef || this._roomInputRef.reportValidity()) {
            this._onJoin();
        }
    }

    /**
     * Overrides the super to account for the differences in the argument types
     * provided by HTML and React Native text inputs.
     *
     * @inheritdoc
     * @override
     * @param {Event} event - The (HTML) Event which details the change such as
     * the EventTarget.
     * @protected
     */
    _onRoomChange(event) {
        super._onRoomChange(event.target.value);
    }

    /**
     * Callback invoked when the desired tab to display should be changed.
     *
     * @param {number} tabIndex - The index of the tab within the array of
     * displayed tabs.
     * @private
     * @returns {void}
     */
    _onTabSelected(tabIndex) {
        this.setState({ selectedTab: tabIndex });
    }

    /**
     * Renders the footer.
     *
     * @returns {ReactElement}
     */
    _renderFooter() {
        const { t } = this.props;
        const {
            MOBILE_DOWNLOAD_LINK_ANDROID,
            MOBILE_DOWNLOAD_LINK_F_DROID,
            MOBILE_DOWNLOAD_LINK_IOS,
        } = interfaceConfig;

        return (
            <footer className="welcome-footer">
                <div className="welcome-footer-centered">
                    <div className="welcome-footer-padded">
                        <div className="welcome-footer-row-block welcome-footer--row-1">
                            <div className="welcome-footer-row-1-text">
                                {t("welcomepage.jitsiOnMobile")}
                            </div>
                            <a
                                className="welcome-badge"
                                href={MOBILE_DOWNLOAD_LINK_IOS}
                            >
                                <img src="./images/app-store-badge.png" />
                            </a>
                            <a
                                className="welcome-badge"
                                href={MOBILE_DOWNLOAD_LINK_ANDROID}
                            >
                                <img src="./images/google-play-badge.png" />
                            </a>
                            <a
                                className="welcome-badge"
                                href={MOBILE_DOWNLOAD_LINK_F_DROID}
                            >
                                <img src="./images/f-droid-badge.png" />
                            </a>
                        </div>
                    </div>
                </div>
            </footer>
        );
    }

    /**
     * Renders tabs to show previous meetings and upcoming calendar events. The
     * tabs are purposefully hidden on mobile browsers.
     *
     * @returns {ReactElement|null}
     */
    _renderTabs() {
        if (isMobileBrowser()) {
            return null;
        }

        const { _calendarEnabled, _recentListEnabled, t } = this.props;

        const tabs = [];

        if (_calendarEnabled) {
            tabs.push({
                label: t("welcomepage.calendar"),
                content: <CalendarList />,
            });
        }

        if (_recentListEnabled) {
            tabs.push({
                label: t("welcomepage.recentList"),
                content: <RecentList />,
            });
        }

        if (tabs.length === 0) {
            return null;
        }

        return (
            <Tabs
                onSelect={this._onTabSelected}
                selected={this.state.selectedTab}
                tabs={tabs}
            />
        );
    }

    /**
     * Sets the internal reference to the HTMLDivElement used to hold the
     * additional card shown near the tabs card.
     *
     * @param {HTMLDivElement} el - The HTMLElement for the div that is the root
     * of the welcome page content.
     * @private
     * @returns {void}
     */
    _setAdditionalCardRef(el) {
        this._additionalCardRef = el;
    }

    /**
     * Sets the internal reference to the HTMLDivElement used to hold the
     * welcome page content.
     *
     * @param {HTMLDivElement} el - The HTMLElement for the div that is the root
     * of the welcome page content.
     * @private
     * @returns {void}
     */
    _setAdditionalContentRef(el) {
        this._additionalContentRef = el;
    }

    /**
     * Sets the internal reference to the HTMLDivElement used to hold the
     * toolbar additional content.
     *
     * @param {HTMLDivElement} el - The HTMLElement for the div that is the root
     * of the additional toolbar content.
     * @private
     * @returns {void}
     */
    _setAdditionalToolbarContentRef(el) {
        this._additionalToolbarContentRef = el;
    }

    /**
     * Sets the internal reference to the HTMLInputElement used to hold the
     * welcome page input room element.
     *
     * @param {HTMLInputElement} el - The HTMLElement for the input of the room name on the welcome page.
     * @private
     * @returns {void}
     */
    _setRoomInputRef(el) {
        this._roomInputRef = el;
    }

    /**
     * Returns whether or not an additional card should be displayed near the tabs.
     *
     * @private
     * @returns {boolean}
     */
    _shouldShowAdditionalCard() {
        return (
            interfaceConfig.DISPLAY_WELCOME_PAGE_ADDITIONAL_CARD &&
            this._additionalCardTemplate &&
            this._additionalCardTemplate.content &&
            this._additionalCardTemplate.innerHTML.trim()
        );
    }

    /**
     * Returns whether or not additional content should be displayed below
     * the welcome page's header for entering a room name.
     *
     * @private
     * @returns {boolean}
     */
    _shouldShowAdditionalContent() {
        return (
            interfaceConfig.DISPLAY_WELCOME_PAGE_CONTENT &&
            this._additionalContentTemplate &&
            this._additionalContentTemplate.content &&
            this._additionalContentTemplate.innerHTML.trim()
        );
    }

    /**
     * Returns whether or not additional content should be displayed inside
     * the header toolbar.
     *
     * @private
     * @returns {boolean}
     */
    _shouldShowAdditionalToolbarContent() {
        return (
            interfaceConfig.DISPLAY_WELCOME_PAGE_TOOLBAR_ADDITIONAL_CONTENT &&
            this._additionalToolbarContentTemplate &&
            this._additionalToolbarContentTemplate.content &&
            this._additionalToolbarContentTemplate.innerHTML.trim()
        );
    }

    /**
     * Returns whether or not the screen has a size smaller than a custom margin
     * and therefore display different text in the go button.
     *
     * @private
     * @returns {boolean}
     */
    _shouldShowResponsiveText() {
        const { innerWidth } = window;

        return innerWidth <= WINDOW_WIDTH_THRESHOLD;
    }
}

export default translate(
    connect(_mapStateToProps, _mapDispatchToProps)(WelcomePage)
);
