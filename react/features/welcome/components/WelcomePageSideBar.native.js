// @flow

import _ from 'lodash';
import React, { Component } from 'react';
import { <PERSON><PERSON>reaView, Sc<PERSON>View, Text } from 'react-native';

import { Avatar } from '../../base/avatar';
import { IconChat, IconHelp, IconSettings, IconShareVideo } from '../../base/icons';
import { setActiveModalId } from '../../base/modal';
import { setAccessTokenNative } from '../../base/mtApi';
import {
    getLocalParticipant,
    getParticipantDisplayName
} from '../../base/participants';
import {
    <PERSON><PERSON>,
    SlidingView
} from '../../base/react';
import { connect } from '../../base/redux';
import { updateSettings } from '../../base/settings';
import { HELP_VIEW_MODAL_ID } from '../../help';
import { LIVE_CHAT_MODAL_ID } from '../../live-chat';
import { SETTINGS_VIEW_ID } from '../../settings';
import { YOUTUBE_HELP_TUTORIAL_VIEW_MODAL_ID } from '../../youtube-help-tutorials';
import { setSideBarVisible } from '../actions';

import SideBarItem from './SideBarItem';
import styles, { SIDEBAR_AVATAR_SIZE } from './styles';

/**
 * The URL at which the privacy policy is available to the user.
 */
// const PRIVACY_URL = 'https://meethour.io/privacy-policy.html?appview=1';

/**
 * The URL at which the terms (of service/use) are available to the user.
 */
// const TERMS_URL = 'https://meethour.io/terms-conditions.html?appview=1';

type Props = {

    /**
     * Redux dispatch action.
     */
    dispatch: Function,

    /**
     * Display name of the local participant.
     */
    _displayName: ?string,

    /**
     * ID of the local participant.
     */
    _localParticipantId: ?string,

    /**
     * Sets the side bar visible or hidden.
     */
    _visible: boolean,
    accessToken: boolean
};

/**
 * A component rendering a welcome page sidebar.
 */
class WelcomePageSideBar extends Component<Props> {
    /**
     * Constructs a new SideBar instance.
     *
     * @inheritdoc
     */
    constructor(props: Props) {
        super(props);

        // Bind event handlers so they are only bound once per instance.
        this._onHideSideBar = this._onHideSideBar.bind(this);
        this._onOpenHelpPage = this._onOpenHelpPage.bind(this);
        this._onOpenSettings = this._onOpenSettings.bind(this);
        this._onOpenLiveChat = this._onOpenLiveChat.bind(this);
        this._onLogout = this._onLogout.bind(this);
        this._onOpenYouTubeHelpPage = this._onOpenYouTubeHelpPage.bind(this);
    }

    /**
     * Implements React's {@link Component#render()}, renders the sidebar.
     *
     * @inheritdoc
     * @returns {ReactElement}
     */
    render() {
        return (
            <SlidingView
                onHide = { this._onHideSideBar }
                position = 'left'
                show = { this.props._visible }
                style = { styles.sideBar } >
                <Header style = { styles.sideBarHeader }>
                    <Avatar
                        participantId = { this.props._localParticipantId }
                        size = { SIDEBAR_AVATAR_SIZE } />
                    <Text style = { styles.displayName }>
                        { this.props._displayName }
                    </Text>
                </Header>
                <SafeAreaView style = { styles.sideBarBody }>
                    <ScrollView
                        style = { styles.itemContainer }>
                        {/* <SideBarItem
                            icon = { IconInfo }
                            label = 'welcomepage.terms'
                            url = { TERMS_URL } />
                        <SideBarItem
                            icon = { IconInfo }
                            label = 'welcomepage.privacy'
                            url = { PRIVACY_URL } /> */}
                        <SideBarItem
                            icon = { IconShareVideo }
                            label = 'welcomepage.youtubeHelpTutorial'
                            onPress = { this._onOpenYouTubeHelpPage } />
                        <SideBarItem
                            icon = { IconChat }
                            label = 'liveChatView.header'
                            onPress = { this._onOpenLiveChat } />
                        <SideBarItem
                            icon = { IconHelp }
                            label = 'welcomepage.getHelp'
                            onPress = { this._onOpenHelpPage } />
                        <SideBarItem
                            icon = { IconSettings }
                            label = 'settings.title'
                            onPress = { this._onOpenSettings } />
                        {!_.isEmpty(this.props.accessToken) && <SideBarItem
                            icon = { IconSettings }
                            label = 'Logout'
                            onPress = { this._onLogout } />}
                    </ScrollView>
                </SafeAreaView>
            </SlidingView>
        );
    }

    _onOpenLiveChat: () => void;

    /**
     * Shows the {@link LiveChatView}.
     *
     * @returns {void}
     */
    _onOpenLiveChat() {
        const { dispatch } = this.props;

        dispatch(setSideBarVisible(false));
        dispatch(setActiveModalId(LIVE_CHAT_MODAL_ID));
    }

    _onLogout: () => void;

    /**
     * Shows the {@link LiveChatView}.
     *
     * @returns {void}
     */
    async _onLogout() {
        this.props.dispatch(setAccessTokenNative(null));
        this.props.dispatch(updateSettings({
            accessToken: null
        }));

        return;
    }


    _onHideSideBar: () => void;

    /**
     * Invoked when the sidebar has closed itself (e.g. Overlay pressed).
     *
     * @private
     * @returns {void}
     */
    _onHideSideBar() {
        this.props.dispatch(setSideBarVisible(false));
    }

    _onOpenHelpPage: () => void;

    /**
     * Shows the {@link HelpView}.
     *
     * @returns {void}
     */
    _onOpenHelpPage() {
        const { dispatch } = this.props;

        dispatch(setSideBarVisible(false));
        dispatch(setActiveModalId(HELP_VIEW_MODAL_ID));
    }

    _onOpenYouTubeHelpPage: () => void;

    /**
     * Shows the {@link YouTubeHelpTutorialView}.
     *
     * @returns {void}
     */
    _onOpenYouTubeHelpPage() {
        const { dispatch } = this.props;

        dispatch(setSideBarVisible(false));
        dispatch(setActiveModalId(YOUTUBE_HELP_TUTORIAL_VIEW_MODAL_ID));
    }

    _onOpenSettings: () => void;

    /**
     * Shows the {@link SettingsView}.
     *
     * @private
     * @returns {void}
     */
    _onOpenSettings() {
        const { dispatch } = this.props;

        dispatch(setSideBarVisible(false));
        dispatch(setActiveModalId(SETTINGS_VIEW_ID));
    }
}

/**
 * Maps (parts of) the redux state to the React {@code Component} props.
 *
 * @param {Object} state - The redux state.
 * @protected
 * @returns {Props}
 */
function _mapStateToProps(state: Object) {
    const _localParticipant = getLocalParticipant(state);
    const _localParticipantId = _localParticipant?.id;
    const _displayName = _localParticipant && getParticipantDisplayName(state, _localParticipantId);
    const { accessToken } = state['features/base/settings'];

    return {
        _displayName,
        _localParticipantId,
        _visible: state['features/welcome'].sideBarVisible,
        accessToken
    };
}

export default connect(_mapStateToProps)(WelcomePageSideBar);
