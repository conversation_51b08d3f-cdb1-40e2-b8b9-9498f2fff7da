/* eslint-disable */
import { jitsiLocalStorage } from "@jitsi/js-utils";
import $ from "jquery";
import { sha256 } from "js-sha256";
import _ from "lodash";
import moment from "moment";
import React, { useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import MediaQuery from "react-responsive";

import { Avatar } from "../../base/avatar";
import { apiKEY } from "../../base/config/constants";
import { JWT_STORAGE, SET_ACCESS_TOKEN } from "../../base/jwt/actionTypes";
import { fetchApi, setJWTforGuest, setuserDetails } from "../../base/mtApi";
import { Watermark } from "../../base/react";
import { getDisplayName, updateSettings } from "../../base/settings";
import {
  getBackendSafeRoomName,
  parseURLParams,
  sliceLengthyCharacters,
} from "../../base/util";
import { isDomainWeborNative } from "../../base/util/checkOS";
import "metismenujs/style";

import { styleWeb } from "./stylesWeb";
import {
  MobileMenuCloseIcon,
  MyCalyIcon,
  VideoIcon,
} from "../../../../static/assets/vendor/svgIcons/svgIcons";
import MetisMenu from "metismenujs";
import { useTranslation } from "react-i18next";
import LanguagesDropdown from "../../languages-component/components/LanguagesDropdown";

declare var config: object;

export const Header = (props) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const [userd, setUserd] = useState(undefined);
  const [rmoveImg, setrmoveImg] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const menuRef = useRef(null);
  const [isToggled, setToggled] = useState(false);
  const toggleTrueFalse = () => setToggled(!isToggled);

  const [loginButton, setLoginButton] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);

  const { interfaceConfig } = useSelector(
    (state) => state["features/base/config"]
  );
  const state1 = useSelector((state) => state["features/base/jwt"]);
  const name = useSelector((state) => getDisplayName(state));
  const { userDetails, pregData } = useSelector(
    (state) => state["features/base/mtApi"]
  );

  const userAccessToken = jitsiLocalStorage.getItem("accessToken");
  const roomName = getBackendSafeRoomName(
    useSelector((state) => state["features/base/conference"].room)
  );
  const { locationURL } = useSelector(
    (state) => state["features/base/connection"]
  );
  const urlPathName = locationURL?.pathname?.length;

  // menu code start

  //  let mobilebrower =  isMobileBrowser();
  //  console.log(mobilebrower,"which browser");

  useEffect(() => {
    // Function to close the menu when clicking outside
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    // Attach the event listener when the menu is open
    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      // Remove the event listener when the menu is closed
      document.removeEventListener("mousedown", handleClickOutside);
    }

    if (document.querySelector("#metismenu")) {
      new MetisMenu("#metismenu");
    }

    // Cleanup when the component unmounts
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen]);

  const testMouseOut = () => {
    setrmoveImg(false);
  };

  const testMouseOver = () => {
    setrmoveImg(true);
  };

  //   menu code end

  useEffect(() => {
    // console.log('userAccessToken ==>', userAccessToken, 'state1 ==>', state1.accessToken, 'userDetails ==>', userDetails);
    let accesTokn = state1.accessToken ?? userAccessToken;
    const urlTest = locationURL?.pathname?.length;
    if (urlTest <= 1) {
      document.body.style.overflowY = "hidden";
    }
    // if(urlTest >= 1){
    //     const ele = document.getElementById('preloader') ?? document.getElementById('preloader1');

    //     if (ele) {

    //         setTimeout(() => {
    //             ele.style.opacity = '0';
    //             document.body.style.overflowY = 'scroll';
    //             setTimeout(() => {
    //                 ele.style.display = 'none';
    //             }, 1200);
    //         }, 500);
    //     }
    // }
    const urrr = parseURLParams(locationURL, true, "search");
    if (
      urrr?.logout &&
      urrr?.client_id &&
      urrr?.client_id === config?.MH_CLIENT_ID
    ) {
      dispatch(setuserDetails(undefined));
      accesTokn = undefined;
      jitsiLocalStorage.clear();
      const ele =
        document.getElementById("preloader") ??
        document.getElementById("preloader1");

      if (ele) {
        setTimeout(() => {
          ele.style.opacity = "0";
          document.body.style.overflowY = "scroll";
          setTimeout(() => {
            ele.style.display = "none";
          }, 2000);
        }, 1500);
      }
    }

    if (
      (_.isNil(userDetails) && pregData && accesTokn) ||
      Boolean(_.isNil(userDetails) && urlTest <= 1 && accesTokn)
    ) {
      const moement = moment().seconds(0).milliseconds(0).format("X");

      const mesh256 = sha256(`${apiKEY}:MH:${moement}`);

      const preRegObject = JSON.stringify({
        client_id: config.MH_CLIENT_ID,
        credentials: mesh256,
        ...isDomainWeborNative,
        meeting_id: roomName,
      });

      fetchApi(
        "customer/user_details",
        "post",
        {
          "Content-Type": "application/json",
          Authorization: `Bearer ${state1.accessToken ?? userAccessToken}`,
        },
        preRegObject
      )
        .then((response) => {
          const isTrueReg: boolean = Boolean(pregData?.is_pre_registration);
          // eslint-disable-next-line camelcase
          // const isTrueGuest: boolean = Boolean(preRegData?.allow_guest === 1);
          const { jwt = "" } = response?.data ?? {};

          // Also Skipping the pre-registration page redirect for Recorder

          if (
            isTrueReg &&
            !state1.jwt &&
            !state1.jwtData &&
            _.isEmpty(jwt) &&
            !config?.iAmRecorder === true
          ) {
            return window.location.replace(
              `${
                config?.brandedPreRegistrationURL
                  ? config?.brandedPreRegistrationURL
                  : pregData.registration_url
              }`
            );
          }
          setUserd(response.data);
          const namess = response?.data?.name;
          const emailss = response?.data?.email;
          const pict = response?.data?.picture;

          dispatch(
            updateSettings({
              avatarURL: pict,
              displayName: namess,
              email: emailss,
            })
          );

          if (response.success) {
            setLoginButton(true);
            dispatch({
              type: SET_ACCESS_TOKEN,
              accessToken: state1.accessToken ?? userAccessToken,
            });
            const getJwt = response.data?.jwt;

            if (!_.isEmpty(getJwt) && !state1?.jwt) {
              dispatch({
                type: JWT_STORAGE,
                jwtData: getJwt,
              });
            }
            if (
              _.isEmpty(getJwt) &&
              !state1?.jwt &&
              !state1.jwtData &&
              urlTest >= 1 &&
              interfaceConfig?.applyMeetingSettings === true
            ) {
              dispatch(setJWTforGuest());
            }
          } else {
            jitsiLocalStorage.removeItem("accessToken");
          }
          const ele =
            document.getElementById("preloader") ??
            document.getElementById("preloader1");

          if (ele) {
            setTimeout(() => {
              ele.style.opacity = "0";
              document.body.style.overflowY = "scroll";
              setTimeout(() => {
                ele.style.display = "none";
              }, 2000);
            }, 1500);
          }

          dispatch(setuserDetails(response.data));
        })
        .catch((e) => {
          console.log("errorResponse1234", e);
          const ele =
            document.getElementById("preloader") ??
            document.getElementById("preloader1");
          if (ele) {
            setTimeout(() => {
              ele.style.opacity = "0";
              document.body.style.overflowY = "scroll";
              setTimeout(() => {
                ele.style.display = "none";
              }, 2000);
            }, 1500);
          }
        });
    }

    const isTrueReg: boolean = Boolean(pregData?.is_pre_registration);

    // // const isTrueGuest: boolean = Boolean(preRegData?.allow_guest === 1);

    // Also Skipping the pre-registration page redirect for Recorder
    if (
      isTrueReg &&
      !state1.jwt &&
      !state1.jwtData &&
      !accesTokn &&
      !config?.iAmRecorder === true &&
      parseURLParams(locationURL)?.mt === undefined
    ) {
      return window.location.href(`${pregData.registration_url}`);
    }

    if (!accesTokn && loginButton === false) {
      const ele =
        document.getElementById("preloader") ??
        document.getElementById("preloader1");

      if (ele) {
        setTimeout(() => {
          ele.style.opacity = "0";
          document.body.style.overflowY = "scroll";
          setTimeout(() => {
            ele.style.display = "none";
          }, 2500);
        }, 2000);
      }
    }

    if (userDetails) {
      setLoginButton(true);
    }
  }, [pregData?.success]);

  const loginButtonJSx = (
    <>
      <li className="signin-li">
        <a
          href={`${config.URL_PREFIX}serviceLogin?client_id=${
            config.MH_CLIENT_ID
          }&redirect_uri=${window.location.href}${
            roomName ? "%23interfaceConfig.ENABLE_DESKTOP_DEEPLINK%3Dfalse" : ""
          }&device_type=web&response_type=get${
            roomName ? `&meeting_id=${roomName}` : ""
          }`}
        >
          {t("prejoin.signinsignup")}
        </a>
      </li>
    </>
  );

  // useEffect(() => {
  //     setUserDP(false);

  //     if (userd && userd.picture && userd.picture.match(/\.(jpg|jpeg|gif|png)$/)) {
  //         setUserDP(true);
  //     }
  // }, [ userd ]);

  useEffect(() => {
    $(document).on("click", ".mobile-nav .drop-down > a", function (e) {
      e.preventDefault();
      // eslint-disable-next-line no-invalid-this
      $(this).next().slideToggle(300);
      // eslint-disable-next-line no-invalid-this
      $(this).parent().toggleClass("active");
    });
  }, []);

  /**
   * Event used to logout the user from the app.
   *
   * @param {event} e - Event object..
   * @private
   * @returns {void}
   */
  function logOutClicked(e) {
    e.preventDefault();
    const ele =
      document.getElementById("preloader") ??
      document.getElementById("preloader1");

    if (ele) {
      ele.style.opacity = "100%";
      ele.style.display = "block";
    }
    jitsiLocalStorage.clear();
    window.location.replace(
      `${config.URL_PREFIX}serviceLogin?client_id=${
        config.MH_CLIENT_ID
      }&redirect_uri=${
        window.location.href
      }&device_type=web&response_type=get&access_token=${
        state1.accessToken ?? userAccessToken
          ? `&access_token=${state1.userAccessToken ?? userAccessToken}`
          : ""
      }&logout=true`
    );
    dispatch(setuserDetails(undefined));
    dispatch(
      updateSettings({
        displayName: undefined,
        email: undefined,
      })
    );
  }
  let logoutDocument;

  if (loginButton) {
    logoutDocument = (
      <li className="drop-down d-flex">
        {(userd && userd?.picture) || (userDetails && userDetails.picture) ? (
          <img
            className="avatar"
            src={userd?.picture ?? userDetails?.picture}
            style={styleWeb.widthandHeight}
          />
        ) : (
          <Avatar
            className="premeeting-screen-avatar"
            displayName={name}
            dynamicColor={true}
            participantId="local"
            size={45}
          />
        )}

        <a>
          {sliceLengthyCharacters({
            char: userd?.name ?? userDetails.name ?? "",
            charLength: 25,
            shouldBeDotted: true,
          })}
        </a>
        <ul className="drop-down1">
          <li>
            <a
              href={`${config.URL_PREFIX}serviceLogin?client_id=${
                config.MH_CLIENT_ID
              }&redirect_uri=${
                config.URL_PREFIX
              }customer/dashboard&device_type=web${
                state1.accessToken ?? userAccessToken
                  ? `&access_token=${state1.userAccessToken ?? userAccessToken}`
                  : ""
              }`}
            >
              <i className="bx bxs-dashboard" /> {t("prejoin.dashboard")}
            </a>
          </li>
          <li>
            <a
              href={`${config.URL_PREFIX}serviceLogin?client_id=${
                config.MH_CLIENT_ID
              }&redirect_uri=${
                config.URL_PREFIX
              }customer/profile&device_type=web${
                state1.accessToken ?? userAccessToken
                  ? `&access_token=${state1.accessToken ?? userAccessToken}`
                  : ""
              }`}
            >
              <i className="bx bx-user" /> {t("prejoin.profile")}
            </a>{" "}
          </li>
          <li>
            <a onClick={logOutClicked} style={styleWeb.cusrorPointer}>
              <i className="bx bx-log-out" /> {t("toolbar.logout")}
            </a>
          </li>
        </ul>
      </li>
    );
  } else if (props.desktopDeepLink) {
    logoutDocument = "";
  } else {
    logoutDocument = loginButtonJSx;
  }

  const handleSubmenuToggle = (e) => {
    e.preventDefault();

    const parentLi = e.currentTarget.closest("li");

    if (parentLi) {
      parentLi.classList.toggle("mm-active");
      const submenu = parentLi.querySelector(".submenu");
      if (submenu) {
        submenu.classList.toggle("mm-collapse");
        submenu.classList.toggle("mm-show");
      }
    }
  };

  return (
    <React.Fragment>
      {interfaceConfig &&
        interfaceConfig.SHOW_MEET_HOUR_WATERMARK &&
        !interfaceConfig.disablePrejoinHeader &&
        (config?.MEGAMENU_HEADER === true ? (
          <header className={"fixed-top align-items-center"} id="header-nextjs">
            <div
              className="container d-flex align-items-center"
              style={{ marginTop: "10px" }}
            >
              <div className="logo mr-auto width300">
                <Watermark />
                {config?.MEGAMENU_HEADER === true ? (
                  <div className="sidebar__menu d-flex justify-content-end d-lg-none">
                    <div
                      className="sidebar-toggle-btn sidebar-toggle-btn-5"
                      id="sidebar-toggle"
                      onClick={toggleTrueFalse}
                    >
                      <span className="line"></span>
                      <span className="line"></span>
                      <span className="line"></span>
                    </div>
                  </div>
                ) : (
                  ""
                )}
                {isToggled ? (
                  <div
                    className={`sidebar__area ${
                      !isToggled ? "" : "sidebar-opened"
                    }`}
                  >
                    <div className="sidebar__wrapper">
                      <div className="sidebar__close">
                        <button
                          className="sidebar__close-btn"
                          id="sidebar__close-btn"
                          onClick={toggleTrueFalse}
                        >
                          <span>
                            <MobileMenuCloseIcon />
                          </span>
                          <span>{t("chromeExtensionBanner:close")}</span>
                        </button>
                      </div>
                      <div className="sidebar__content">
                        <div className="logo mb-40">

                          <div className="d-flex">
                          <a href="/">
                            <img
                              alt="logo"
                              height={30}
                              src="/img/logo/logo.png"
                              width={160}
                            />
                          </a>

                          <LanguagesDropdown isMobileView={true} />
                          </div>
                        </div>
                        <div className={"mobile-menu mean-container"}>
                          <nav className="mean-nav">
                            <ul className="metismenu text-muted" id="metismenu">


                            <li>
                                {loginButton === true ? (
                                  <>
                                    <li className="drop-down d-flex">
                                      {userd?.picture || userDetails?.picture ? (
                                        <img
                                          className="avatar"
                                          src={userd?.picture ?? userDetails?.picture}
                                          style={{ width: "45px", height: "42px" }}
                                          alt="User Avatar"
                                        />
                                      ) : (
                                        <Avatar
                                          className="premeeting-screen-avatar"
                                          displayName={name}
                                          dynamicColor={true}
                                          participantId="local"
                                          size={45}
                                        />
                                      )}

                                      <a onClick={() => setShowDropdown(!showDropdown)}>
                                        {sliceLengthyCharacters({
                                          char: userd?.name ?? userDetails?.name ?? "",
                                          charLength: 25,
                                          shouldBeDotted: true,
                                        })}
                                        
                                      </a>

                                      <ul className={`drop-down1 ${showDropdown ? "show" : "hide"}`}>
                                        <li>

                                          <a
                                            href={`${config.URL_PREFIX}serviceLogin?client_id=${
                                              config.MH_CLIENT_ID
                                            }&redirect_uri=${config.URL_PREFIX}customer/dashboard&device_type=web${
                                              state1.accessToken ?? userAccessToken
                                                ? `&access_token=${state1.accessToken ?? userAccessToken}`
                                                : ""
                                            }`}
                                            
                                          >
                                            <i className="bx bxs-dashboard"></i> {t("prejoin.dashboard")}
                                          </a>
                                        </li>

                                        <li>
                                          <a
                                            href={`${config.URL_PREFIX}serviceLogin?client_id=${
                                              config.MH_CLIENT_ID
                                            }&redirect_uri=${config.URL_PREFIX}customer/profile&device_type=web${
                                              state1.accessToken ?? userAccessToken
                                                ? `&access_token=${state1.accessToken ?? userAccessToken}`
                                                : ""
                                            }`}
                                          >
                                            <i className="bx bx-user"></i> {t("prejoin.profile")}
                                          </a>
                                        </li>

                                        <li>
                                          <a style={{ cursor: "pointer" }} onClick={logOutClicked}>
                                            <i className="bx bx-log-out"></i> {t("toolbar.logout")}
                                          </a>
                                        </li>
                                      </ul>
                                    </li>
                                  </>
                                ) : (
                                  <a
                                    href={`${
                                      config.URL_PREFIX
                                    }serviceLogin?client_id=${
                                      config.MH_CLIENT_ID
                                    }&redirect_uri=${
                                      window.location.href
                                    }%23interfaceConfig.ENABLE_DESKTOP_DEEPLINK%3Dfalse&device_type=web&response_type=get${
                                      roomName ? `&meeting_id=${roomName}` : ""
                                    }`}
                                  >
                                    {t("prejoin.signinsignup")}
                                  </a>
                                )}
                              </li>

                              <li>
                                <a href="/">{t("welcomepage.footer.home")}</a>
                              </li>

                              <li>
                                <a
                                  className="has-arrow"
                                  href="/products"
                                  onClick={handleSubmenuToggle}
                                >
                                  {t("welcomepage.header.products")}
                                </a>
                                <ul className="submenu mm-collapse">
                                  <li className="ml-20">
                                    <a
                                      className="has-arrow"
                                      href="products/video-conference"
                                      onClick={handleSubmenuToggle}
                                    >
                                      {t("welcomepage.header.videoConference")}
                                    </a>
                                    <ul className="submenu mm-collapse">
                                      <li>
                                        <a href="/products/video-conference/free">
                                          {t("welcomepage.header.free", {
                                            lng: "en",
                                          })}
                                        </a>
                                      </li>
                                      <li>
                                        <a href="/products/video-conference/pro">
                                          {t("welcomepage.header.pro", {
                                            lng: "en",
                                          })}
                                        </a>
                                      </li>
                                      <li>
                                        <a href="/products/video-conference/developer">
                                          {t("welcomepage.header.developer", {
                                            lng: "en",
                                          })}
                                        </a>
                                      </li>
                                      <li>
                                        <a href="/products/video-conference/enterprise">
                                          {t("welcomepage.header.enterprise", {
                                            lng: "en",
                                          })}
                                        </a>
                                      </li>
                                      <li>
                                        <a href="/products/video-conference/enterprise-selfhost">
                                          {t(
                                            "welcomepage.header.enterpriseSelfHost",
                                            { lng: "en" }
                                          )}
                                        </a>
                                      </li>
                                      <li>
                                        <a href="/products/video-conference/features">
                                          {t("welcomepage.header.features")}
                                        </a>
                                      </li>
                                    </ul>{" "}
                                  </li>

                                  <li className="ml-20">
                                    <a
                                      href="https://mycaly.io/"
                                      target="_blank"
                                    >
                                      {t("welcomepage.header.myCaly", {
                                        lng: "en",
                                      })}
                                    </a>{" "}
                                  </li>
                                </ul>
                              </li>
                              <li>
                                <a
                                  className="has-arrow"
                                  href="/solutions"
                                  onClick={handleSubmenuToggle}
                                >
                                  {t("welcomepage.header.solutions")}
                                </a>
                                <ul className="submenu mm-collapse">
                                  <li className="ml-20">
                                    <a
                                      className="has-arrow"
                                      href="/solutions/usecases"
                                      onClick={handleSubmenuToggle}
                                    >
                                      {t("welcomepage.header.useCases")}
                                    </a>
                                    <ul className="submenu mm-collapse">
                                      <li>
                                        <a href="/solutions/usecases/videoconferencing">
                                          {t(
                                            "welcomepage.header.videoConferencing"
                                          )}
                                        </a>
                                      </li>
                                      <li>
                                        <a href="/solutions/usecases/livestreaming">
                                          {t(
                                            "welcomepage.header.liveStreaming"
                                          )}
                                        </a>
                                      </li>
                                      <li>
                                        <a href="/solutions/usecases/virtualclassrooms">
                                          {t(
                                            "welcomepage.header.liveStreaming"
                                          )}
                                        </a>
                                      </li>
                                      <li>
                                        <a href="/solutions/usecases/virtualevents">
                                          {t(
                                            "welcomepage.header.virtualEvents"
                                          )}
                                        </a>
                                      </li>
                                      <li>
                                        <a href="/solutions/usecases/ekyc">
                                          {t("welcomepage.header.videoKYC")}
                                        </a>
                                      </li>
                                      <li>
                                        <a href="/solutions/usecases/webinars">
                                          {t("welcomepage.header.webinars")}
                                        </a>
                                      </li>
                                      <li>
                                        <a href="/solutions/usecases/fundraising">
                                          {t(
                                            "welcomepage.header.fundraisingDonateOnline"
                                          )}
                                        </a>
                                      </li>{" "}
                                    </ul>{" "}
                                  </li>

                                  <li className="ml-20">
                                    <a
                                      className="has-arrow"
                                      href="/solutions/industries"
                                      onClick={handleSubmenuToggle}
                                    >
                                      {t("welcomepage.header.industries")}
                                    </a>
                                    <ul className="submenu mm-collapse">
                                      <li>
                                        <a href="/solutions/industries/edtech">
                                          {t("welcomepage.header.edTech")}
                                        </a>
                                      </li>{" "}
                                      <li>
                                        <a href="/solutions/industries/fitness">
                                          {t("welcomepage.header.fitness")}
                                        </a>
                                      </li>{" "}
                                      <li>
                                        <a href="/solutions/industries/healthcare">
                                          {t("welcomepage.header.telehealth")}
                                        </a>
                                      </li>
                                    </ul>{" "}
                                  </li>
                                </ul>
                              </li>

                              <li>
                                <a
                                  className="has-arrow"
                                  href="/developers"
                                  onClick={handleSubmenuToggle}
                                >
                                  {t("welcomepage.header.developers")}
                                </a>
                                <ul className="submenu mm-collapse">
                                  <li className="ml-20">
                                    <a
                                      className="has-arrow"
                                      onClick={handleSubmenuToggle}
                                    >
                                      {t("welcomepage.header.getStarted")}
                                    </a>
                                    <ul className="submenu mm-collapse">
                                      <li>
                                        <a href="/developers/sdks">
                                          {t("welcomepage.header.preBuiltSDKs")}
                                        </a>
                                      </li>
                                      <li>
                                        <a
                                          href="https://docs.v-empower.com/docs/MeetHour-API"
                                          target="_blank"
                                        >
                                          {t(
                                            "welcomepage.header.documentation"
                                          )}
                                        </a>
                                      </li>
                                    </ul>{" "}
                                  </li>

                                  <li className="ml-20">
                                    <a
                                      className="has-arrow"
                                      onClick={handleSubmenuToggle}
                                    >
                                      {t("welcomepage.header.resources")}
                                    </a>
                                    <ul className="submenu mm-collapse">
                                      <li>
                                        <a href="/blog">
                                          {t("welcomepage.header.blog")}
                                        </a>
                                      </li>{" "}
                                      <li>
                                        <a href="/developers/resources/api-status">
                                          {t("welcomepage.header.apiStatus")}
                                        </a>
                                      </li>{" "}
                                      <li>
                                        <a
                                          href="http://kb.meethour.io/category/how-to"
                                          target="_blank"
                                        >
                                          {t(
                                            "welcomepage.header.knowledgeBase"
                                          )}
                                        </a>
                                      </li>
                                    </ul>{" "}
                                  </li>
                                </ul>
                              </li>

                              <li>
                                <a
                                  className="has-arrow"
                                  href="/products/video-conference/pricing"
                                  onClick={handleSubmenuToggle}
                                >
                                  {t("welcomepage.pricing")}
                                </a>
                                <ul className="submenu mm-collapse">
                                  <li>
                                    <a href="/products/video-conference/pricing">
                                      {t("welcomepage.header.videoConference")}
                                    </a>{" "}
                                  </li>
                                  <li>
                                    <a
                                      href="https://mycaly.io/pricing"
                                      target="_blank"
                                    >
                                      {t("welcomepage.header.myCaly", {
                                        lng: "en",
                                      })}
                                    </a>{" "}
                                  </li>
                                </ul>
                              </li>
  */}

                              <li>
                                <a href="/joinmeeting">
                                  {t("welcomepage.header.joinAMeeting")}
                                </a>
                              </li>

                              <li>
                                <a
                                  className="has-arrow"
                                  href="http://kb.meethour.io/category/how-to"
                                  onClick={handleSubmenuToggle}
                                >
                                  {t("welcomepage.header.help")}
                                </a>
                                <ul className="submenu mm-collapse">
                                  <li>
                                    <a href="mailto:<EMAIL>">
                                      {" "}
                                      <i className="icon_mail"></i>{" "}
                                      {t("welcomepage.header.eMail")}
                                    </a>{" "}
                                  </li>
                                  <li>
                                    <a
                                      href="https://meethour.tawk.help/"
                                      target="_blank"
                                    >
                                      {" "}
                                      <i className="icon_question_alt2"></i>{" "}
                                      {t("welcomepage.header.helpDesk")}{" "}
                                    </a>
                                  </li>

                                  <li>
                                    <a href="tel:+124-547-689">
                                      {" "}
                                      <i className="icon_phone"></i> +1 (312)
                                      800-9410
                                    </a>
                                  </li>
                                  <li>
                                    <a href="/schedule-a-demo">
                                      {" "}
                                      <i className="icon_info"></i>{" "}
                                      {t("welcomepage.header.scheduleADemo")}
                                    </a>
                                  </li>
                                </ul>
                              </li>

                            </ul>
                          </nav>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <></>
                )}
              </div>

              {interfaceConfig && !interfaceConfig.disablePrejoinHeader && (
                <>
                  {/* <MediaQuery maxWidth = { 1000 }>
                                    <button
                                        className = 'mobile-nav-toggle d-lg-none'
                                        onClick = { openNavbar }
                                        type = 'button'><i className = 'icofont-navigation-menu' /></button>
                                </MediaQuery> */}
                  <MediaQuery minWidth={1000}>
                    <nav
                      className="nav-menu d-none d-lg-flex desktop-width navbar navbar-expand-lg navbar-light nav-menu-nextjs"
                      style={styleWeb.width100}
                    >
                      <div className="container-fluid">
                        <button
                          aria-controls="navbarExampleOnHover"
                          aria-expanded="false"
                          aria-label="Toggle navigation"
                          className="navbar-toggler px-0"
                          data-mdb-target="#navbarExampleOnHover"
                          data-mdb-toggle="collapse"
                          type="button"
                        >
                          <i className="fas fa-bars"></i>
                        </button>

                        <div
                          className="collapse navbar-collapse justify-content-between"
                          id="navbarExampleOnHover"
                        >
                          <ul className="navbar-nav ps-lg-0">
                            <li className="nav-item dropdown dropdown-hover position-static">
                              <a
                                aria-expanded="false"
                                className="nav-a dropdown-toggle"
                                data-mdb-toggle="dropdown"
                                href="/products"
                                id="navbarDropdown"
                              >
                                {t("welcomepage.header.products")}
                              </a>
                              <div
                                aria-labelledby="navbarDropdown"
                                className="dropdown-menu w-100 mt-0"
                                style={{
                                  padding: "0px 25px 0px 5px",
                                }}
                              >
                                <div
                                  className="container"
                                  style={{
                                    background: "#415d86",
                                    // padding:"20px",
                                    // chnage if need spacing
                                    //padding: "0px 24px"
                                  }}
                                >
                                  <div className="row my-4">
                                    <div
                                      className="col-md-6 col-lg-6 mb-3 mb-lg-0"
                                      style={{
                                        padding: "0px 12px",
                                      }}
                                    >
                                      <div
                                        className="list-group list-group-flush"
                                        style={{
                                          background: "#415d86",
                                        }}
                                      >
                                        <a
                                          className="list-group-item list-group-item-action list-active"
                                          href={"/products/video-conference"}
                                          onMouseOver={testMouseOver}
                                        >
                                          <div
                                            style={{
                                              display: "flex",
                                              alignItems: "center",
                                            }}
                                          >
                                            <VideoIcon />
                                            <span
                                              style={{
                                                marginLeft: "15px",
                                              }}
                                            >
                                              {t(
                                                "welcomepage.header.videoConference"
                                              )}
                                            </span>
                                          </div>

                                          <span className="submenu-sub-text">
                                            {t(
                                              "welcomepage.header.hdQualityVideoConferenceApp"
                                            )}
                                          </span>
                                        </a>

                                        <a
                                          className="list-group-item list-group-item-action list-active"
                                          href={"https://mycaly.io/"}
                                          onMouseOver={testMouseOut}
                                          target="_blank"
                                        >
                                          <div
                                            style={{
                                              display: "flex",
                                              alignItems: "center",
                                            }}
                                          >
                                            <MyCalyIcon />
                                            <span
                                              style={{
                                                marginLeft: "15px",
                                              }}
                                            >
                                              {t("welcomepage.header.myCaly", {
                                                lng: "en",
                                              })}
                                            </span>
                                          </div>

                                          <span className="submenu-sub-text">
                                            {t(
                                              "welcomepage.header.appointmentSchedulingVideoConference"
                                            )}
                                          </span>
                                        </a>
                                      </div>
                                    </div>

                                    <div className="col-md-6 col-lg-6 mb-3 mb-lg-0">
                                      {rmoveImg === false ? (
                                        <img
                                          alt="Meet Hour Video Conference"
                                          src="images/product-thumb.png"
                                          width={300}
                                        />
                                      ) : (
                                        <div
                                          className="list-group list-group-flush"
                                          style={{
                                            padding: "0px 20px",
                                          }}
                                        >
                                          <h5>
                                            {t(
                                              "welcomepage.header.videoConferencePlans"
                                            )}
                                          </h5>
                                          <a
                                            className="list-group-item list-group-item-action list-active"
                                            href={
                                              "/products/video-conference/free"
                                            }
                                          >
                                            <div
                                              style={{
                                                display: "flex",
                                                alignItems: "end",
                                              }}
                                            >
                                              <img
                                                src="images/free.svg"
                                                style={{
                                                  height: "26px",
                                                  width: "26px",
                                                  border: "none",
                                                  borderRadius: "unset",
                                                }}
                                              />

                                              <span>
                                                {t("welcomepage.header.free", {
                                                  lng: "en",
                                                })}
                                              </span>
                                            </div>

                                            <span className="submenu-sub-text">
                                              {t(
                                                "welcomepage.header.noTimeLimitGroupCalls"
                                              )}
                                            </span>
                                          </a>
                                          <a
                                            className="list-group-item list-group-item-action list-active"
                                            href={
                                              "/products/video-conference/pro"
                                            }
                                          >
                                            <div
                                              style={{
                                                display: "flex",
                                                alignItems: "end",
                                              }}
                                            >
                                              <img
                                                src="images/pro.svg"
                                                style={{
                                                  height: "26px",
                                                  width: "26px",
                                                  border: "none",
                                                  borderRadius: "unset",
                                                }}
                                              />
                                              <span>
                                                {t("welcomepage.header.pro", {
                                                  lng: "en",
                                                })}
                                              </span>
                                            </div>

                                            <span className="submenu-sub-text">
                                              {t(
                                                "welcomepage.header.noAdsRecordingLiveStreaming"
                                              )}
                                            </span>
                                          </a>
                                          <a
                                            className="list-group-item list-group-item-action list-active"
                                            href={
                                              "/products/video-conference/developer"
                                            }
                                          >
                                            <div
                                              style={{
                                                display: "flex",
                                                alignItems: "end",
                                              }}
                                            >
                                              <img
                                                src="images/developer.svg"
                                                style={{
                                                  height: "26px",
                                                  width: "26px",
                                                  border: "none",
                                                  borderRadius: "unset",
                                                }}
                                              />
                                              <span>
                                                {t(
                                                  "welcomepage.header.developer",
                                                  { lng: "en" }
                                                )}
                                              </span>
                                            </div>

                                            <span className="submenu-sub-text">
                                              {t(
                                                "welcomepage.header.integrateVideoCallWithinYourWebsiteApp"
                                              )}
                                            </span>
                                          </a>

                                          <a
                                            className="list-group-item list-group-item-action list-active"
                                            href={
                                              "/products/video-conference/enterprise"
                                            }
                                          >
                                            <div
                                              style={{
                                                display: "flex",
                                                alignItems: "end",
                                              }}
                                            >
                                              <img
                                                src="images/enterprise.svg"
                                                style={{
                                                  height: "26px",
                                                  width: "26px",
                                                  border: "none",
                                                  borderRadius: "unset",
                                                }}
                                              />
                                              <span>
                                                {t(
                                                  "welcomepage.header.enterprise",
                                                  { lng: "en" }
                                                )}
                                              </span>
                                            </div>

                                            <span className="submenu-sub-text">
                                              {t(
                                                "welcomepage.header.customIntegrationDedicatedSupport"
                                              )}
                                            </span>
                                          </a>

                                          <a
                                            className="list-group-item list-group-item-action list-active"
                                            href={
                                              "/products/video-conference/enterprise-selfhost"
                                            }
                                          >
                                            <div
                                              style={{
                                                display: "flex",
                                                alignItems: "end",
                                              }}
                                            >
                                              <img
                                                src="images/enterprise-selfhost.svg"
                                                style={{
                                                  height: "26px",
                                                  width: "26px",
                                                  border: "none",
                                                  borderRadius: "unset",
                                                }}
                                              />
                                              <span>
                                                {t(
                                                  "welcomepage.header.enterpriseSelfHost"
                                                )}
                                              </span>
                                            </div>

                                            <span className="submenu-sub-text">
                                              {t(
                                                "welcomepage.header.hostVideoConferenceOnYourServers"
                                              )}
                                            </span>
                                          </a>
                                          <h5 className="mt-20"></h5>
                                          <a
                                            className="list-group-item list-group-item-action list-active"
                                            href={
                                              "/products/video-conference/features"
                                            }
                                          >
                                            <div
                                              style={{
                                                display: "flex",
                                                alignItems: "end",
                                              }}
                                            >
                                              <img
                                                src="images/features.svg"
                                                style={{
                                                  height: "26px",
                                                  width: "26px",
                                                  border: "none",
                                                  borderRadius: "unset",
                                                }}
                                              />
                                              <span>
                                                {t(
                                                  "welcomepage.header.features"
                                                )}
                                              </span>
                                            </div>

                                            <span className="submenu-sub-text">
                                              {t(
                                                "welcomepage.header.simplifiedAPIReferences"
                                              )}
                                            </span>
                                          </a>
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </li>

                            <li className="nav-item dropdown dropdown-hover position-static">
                              <a
                                aria-expanded="false"
                                className="nav-a dropdown-toggle"
                                data-mdb-toggle="dropdown"
                                href="/solutions"
                                id="navbarDropdown"
                                role="button"
                              >
                                {t("welcomepage.header.solutions")}
                              </a>
                              <div
                                aria-labelledby="navbarDropdown"
                                className="dropdown-menu w-100 mt-0"
                              >
                                <div className="container">
                                  <div
                                    className="row my-4"
                                    style={{
                                      padding: "20px",
                                    }}
                                  >
                                    <div className="col-md-6 col-lg-6 mb-3 mb-lg-0">
                                      <div
                                        className="list-group list-group-flush"
                                        style={{
                                          paddingRight: "15px",
                                        }}
                                      >
                                        <h5>
                                          {t("welcomepage.header.useCases")}
                                        </h5>

                                        <a
                                          className="list-group-item list-group-item-action list-active"
                                          href={
                                            "/solutions/usecases/videoconferencing"
                                          }
                                        >
                                          <div
                                            style={{
                                              display: "flex",
                                              alignItems: "center",
                                            }}
                                          >
                                            <img
                                              src="images/vide-conference.svg"
                                              style={{
                                                height: "26px",
                                                width: "26px",
                                                border: "none",
                                                borderRadius: "unset",
                                              }}
                                            />
                                            <span>
                                              {t(
                                                "welcomepage.header.videoConferencing"
                                              )}
                                            </span>
                                          </div>

                                          <span className="submenu-sub-text">
                                            {t(
                                              "welcomepage.header.customTailoredVideoMeetings"
                                            )}
                                          </span>
                                        </a>
                                        <a
                                          className="list-group-item list-group-item-action list-active"
                                          href={
                                            "/solutions/usecases/livestreaming"
                                          }
                                        >
                                          <div
                                            style={{
                                              display: "flex",
                                              alignItems: "end",
                                            }}
                                          >
                                            <img
                                              src="images/live-stream.svg"
                                              style={{
                                                height: "26px",
                                                width: "26px",
                                                border: "none",
                                                borderRadius: "unset",
                                              }}
                                            />
                                            <span>
                                              {t(
                                                "welcomepage.header.liveStreaming"
                                              )}
                                            </span>
                                          </div>

                                          <span className="submenu-sub-text">
                                            {t(
                                              "welcomepage.header.highQualityLiveEventStreaming"
                                            )}
                                          </span>
                                        </a>
                                        <a
                                          className="list-group-item list-group-item-action list-active"
                                          href="/solutions/usecases/virtualclassrooms"
                                        >
                                          <div
                                            style={{
                                              display: "flex",
                                              alignItems: "end",
                                            }}
                                          >
                                            <img
                                              src="images/virtual-classroms.svg"
                                              style={{
                                                height: "26px",
                                                width: "26px",
                                                border: "none",
                                                borderRadius: "unset",
                                              }}
                                            />
                                            <span>
                                              {t(
                                                "welcomepage.header.virtualClassrooms"
                                              )}
                                            </span>
                                          </div>

                                          <span className="submenu-sub-text">
                                            {t(
                                              "welcomepage.header.interactiveVirtualLearningSolutions"
                                            )}
                                          </span>
                                        </a>

                                        <a
                                          className="list-group-item list-group-item-action list-active"
                                          href={
                                            "/solutions/usecases/virtualevents"
                                          }
                                        >
                                          <div
                                            style={{
                                              display: "flex",
                                              alignItems: "end",
                                            }}
                                          >
                                            <img
                                              src="images/virtual-events.svg"
                                              style={{
                                                height: "26px",
                                                width: "26px",
                                                border: "none",
                                                borderRadius: "unset",
                                              }}
                                            />
                                            <span>
                                              {t(
                                                "welcomepage.header.virtualEvents"
                                              )}
                                            </span>
                                          </div>

                                          <span className="submenu-sub-text">
                                            {t(
                                              "welcomepage.header.engagingVirtualEventExperiences"
                                            )}
                                          </span>
                                        </a>
                                        <a
                                          className="list-group-item list-group-item-action list-active"
                                          href={"/solutions/usecases/ekyc"}
                                        >
                                          <div
                                            style={{
                                              display: "flex",
                                              alignItems: "end",
                                            }}
                                          >
                                            <img
                                              src="images/ekyc.svg"
                                              style={{
                                                height: "26px",
                                                width: "26px",
                                                border: "none",
                                                borderRadius: "unset",
                                              }}
                                            />
                                            <span>
                                              {t("welcomepage.header.videoKYC")}
                                            </span>
                                          </div>

                                          <span className="submenu-sub-text">
                                            {t(
                                              "welcomepage.header.smoothVideoOnboardingExperience"
                                            )}
                                          </span>
                                        </a>

                                        <a
                                          className="list-group-item list-group-item-action list-active"
                                          href={"/solutions/usecases/webinars"}
                                        >
                                          <div
                                            style={{
                                              display: "flex",
                                              alignItems: "end",
                                            }}
                                          >
                                            <img
                                              src="images/webinars.svg"
                                              style={{
                                                height: "26px",
                                                width: "26px",
                                                border: "none",
                                                borderRadius: "unset",
                                              }}
                                            />
                                            <span>
                                              {t("welcomepage.header.webinars")}
                                            </span>
                                          </div>

                                          <span className="submenu-sub-text">
                                            {t(
                                              "welcomepage.header.webinarSessionsWithIndustryLeaders"
                                            )}
                                          </span>
                                        </a>

                                        <a
                                          className="list-group-item list-group-item-action list-active"
                                          href={
                                            "/solutions/usecases/fundraising"
                                          }
                                        >
                                          <div
                                            style={{
                                              display: "flex",
                                              alignItems: "end",
                                            }}
                                          >
                                            <img
                                              src="images/fundraising.svg"
                                              style={{
                                                height: "26px",
                                                width: "26px",
                                                border: "none",
                                                borderRadius: "unset",
                                              }}
                                            />
                                            <span>
                                              {t(
                                                "welcomepage.header.fundraisingDonateOnline"
                                              )}
                                            </span>
                                          </div>

                                          <span className="submenu-sub-text">
                                            {t(
                                              "welcomepage.header.fundraiseEffortlesslyWithinVideoConferences"
                                            )}
                                          </span>
                                        </a>
                                      </div>
                                    </div>
                                    <div className="col-md-6 col-lg-6 mb-3 mb-lg-0">
                                      <div className="list-group list-group-flush">
                                        <h5>
                                          {t("welcomepage.header.industries")}
                                        </h5>

                                        <a
                                          className="list-group-item list-group-item-action list-active"
                                          href={"/solutions/industries/edtech"}
                                        >
                                          <div
                                            style={{
                                              display: "flex",
                                              alignItems: "baseline",
                                            }}
                                          >
                                            <img
                                              src="images/edtech.svg"
                                              style={{
                                                height: "26px",
                                                width: "26px",
                                                border: "none",
                                                borderRadius: "unset",
                                              }}
                                            />{" "}
                                            <span>
                                              {t("welcomepage.header.edTech")}
                                            </span>
                                          </div>

                                          <span className="submenu-sub-text">
                                            {t(
                                              "welcomepage.header.engagingOnlineLearningForEducators"
                                            )}
                                          </span>
                                        </a>
                                        <a
                                          className="list-group-item list-group-item-action list-active"
                                          href="/solutions/industries/fitness"
                                        >
                                          <div
                                            style={{
                                              display: "flex",
                                              alignItems: "baseline",
                                            }}
                                          >
                                            <img
                                              src="images/fitness.svg"
                                              style={{
                                                height: "26px",
                                                width: "26px",
                                                border: "none",
                                                borderRadius: "unset",
                                              }}
                                            />
                                            <span>
                                              {t("welcomepage.header.fitness")}
                                            </span>
                                          </div>

                                          <span className="submenu-sub-text">
                                            {t(
                                              "welcomepage.header.virtualSolutionForHomeFitness"
                                            )}
                                          </span>
                                        </a>
                                        <a
                                          className="list-group-item list-group-item-action list-active"
                                          href={
                                            "/solutions/industries/healthcare"
                                          }
                                        >
                                          <div
                                            style={{
                                              display: "flex",
                                              alignItems: "baseline",
                                            }}
                                          >
                                            <img
                                              src="images/telehealth.svg"
                                              style={{
                                                height: "26px",
                                                width: "26px",
                                                border: "none",
                                                borderRadius: "unset",
                                              }}
                                            />
                                            <span>
                                              {t(
                                                "welcomepage.header.telehealth"
                                              )}
                                            </span>
                                          </div>

                                          <span className="submenu-sub-text">
                                            {t(
                                              "welcomepage.header.tailoredSolutionsForYourHealthcareNeeds"
                                            )}
                                          </span>
                                        </a>
                                        <img
                                          alt="solutions-header-thumb"
                                          src="images/solutions-header-thumb.png"
                                          width={300}
                                        />
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </li>

                            <li className="nav-item dropdown dropdown-hover position-static">
                              <a
                                aria-expanded="false"
                                className="nav-a dropdown-toggle"
                                data-mdb-toggle="dropdown"
                                href="/developers"
                                id="navbarDropdown"
                                role="button"
                              >
                                {t("welcomepage.header.developers")}
                              </a>
                              <div
                                aria-labelledby="navbarDropdown"
                                className="dropdown-menu w-100 mt-0"
                              >
                                <div className="container">
                                  <div
                                    className="row my-4"
                                    style={{
                                      padding: "20px",
                                    }}
                                  >
                                    <div className="col-md-6 col-lg-6 mb-3 mb-lg-0">
                                      <div className="list-group list-group-flush">
                                        <h5>
                                          {" "}
                                          {t("welcomepage.header.getStarted")}
                                        </h5>
                                        <a
                                          className="list-group-item list-group-item-action list-active"
                                          href={"/developers/sdks"}
                                        >
                                          <div
                                            style={{
                                              display: "flex",
                                              alignItems: "end",
                                            }}
                                          >
                                            <img
                                              src="images/sdks.svg"
                                              style={{
                                                height: "26px",
                                                width: "26px",
                                                border: "none",
                                                borderRadius: "unset",
                                              }}
                                            />
                                            <span>
                                              {" "}
                                              {t(
                                                "welcomepage.header.preBuiltSDKs"
                                              )}
                                            </span>
                                          </div>

                                          <span className="submenu-sub-text">
                                            {t(
                                              "welcomepage.header.accelerateDevelopmentWithPrebuiltSDKs"
                                            )}
                                          </span>
                                        </a>
                                        <a
                                          className="list-group-item list-group-item-action list-active"
                                          href="https://docs.v-empower.com/docs/MeetHour-API"
                                          target="_blank"
                                        >
                                          <div
                                            style={{
                                              display: "flex",
                                              alignItems: "end",
                                            }}
                                          >
                                            <img
                                              src="images/documentation.svg"
                                              style={{
                                                height: "26px",
                                                width: "26px",
                                                border: "none",
                                                borderRadius: "unset",
                                              }}
                                            />
                                            <span>
                                              {" "}
                                              {t(
                                                "welcomepage.header.documentation"
                                              )}
                                            </span>
                                          </div>
                                          <span className="submenu-sub-text">
                                            {t(
                                              "welcomepage.header.simplifiedAPIReferences"
                                            )}
                                          </span>
                                        </a>{" "}
                                        <img
                                          alt="developers-api-documentation"
                                          height={216}
                                          src="images/video-conference3.jpg"
                                          width={300}
                                        />
                                      </div>
                                    </div>
                                    <div
                                      className="col-md-6 col-lg-6 mb-3 mb-lg-0"
                                      style={{
                                        paddingLeft: "30px",
                                      }}
                                    >
                                      <div className="list-group list-group-flush">
                                        <h5>
                                          {" "}
                                          {t("welcomepage.header.resources")}
                                        </h5>

                                        <a
                                          className="list-group-item list-group-item-action list-active"
                                          href={"/blog"}
                                        >
                                          <div
                                            style={{
                                              display: "flex",
                                              alignItems: "end",
                                            }}
                                          >
                                            <img
                                              src="images/blog.svg"
                                              style={{
                                                height: "26px",
                                                width: "26px",
                                                border: "none",
                                                borderRadius: "unset",
                                              }}
                                            />
                                            <span>
                                              {" "}
                                              {t("welcomepage.header.blog")}
                                            </span>
                                          </div>

                                          <span className="submenu-sub-text">
                                            {t(
                                              "welcomepage.header.stayuptodateWithOurBlog"
                                            )}
                                          </span>
                                        </a>
                                        <a
                                          className="list-group-item list-group-item-action list-active"
                                          href={
                                            "/developers/resources/api-status"
                                          }
                                        >
                                          <div
                                            style={{
                                              display: "flex",
                                              alignItems: "end",
                                            }}
                                          >
                                            <img
                                              src="images/api-status.svg"
                                              style={{
                                                height: "26px",
                                                width: "26px",
                                                border: "none",
                                                borderRadius: "unset",
                                              }}
                                            />
                                            <span>
                                              {" "}
                                              {t(
                                                "welcomepage.header.apiStatus"
                                              )}
                                            </span>
                                          </div>

                                          <span className="submenu-sub-text">
                                            {t(
                                              "welcomepage.header.systemHealthStatusandUpdates"
                                            )}
                                          </span>
                                        </a>

                                        <a
                                          className="list-group-item list-group-item-action list-active"
                                          href={
                                            "http://kb.meethour.io/category/how-to"
                                          }
                                          target="_Blank"
                                        >
                                          <div
                                            style={{
                                              display: "flex",
                                              alignItems: "end",
                                            }}
                                          >
                                            <img
                                              src="images/knowledge-base.svg"
                                              style={{
                                                height: "26px",
                                                width: "26px",
                                                border: "none",
                                                borderRadius: "unset",
                                              }}
                                            />
                                            <span>
                                              {" "}
                                              {t(
                                                "welcomepage.header.knowledgeBase"
                                              )}
                                            </span>
                                          </div>{" "}
                                          <span className="submenu-sub-text">
                                            {t(
                                              "welcomepage.header.systemHealthStatusandUpdates"
                                            )}
                                          </span>
                                        </a>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </li>

                            <li className="nav-item dropdown dropdown-hover position-static">
                              <a
                                aria-expanded="false"
                                className="nav-a dropdown-toggle"
                                data-mdb-toggle="dropdown"
                                href="/products/video-conference/pricing"
                                id="navbarDropdown"
                              >
                                {t("welcomepage.header.pricing")}
                              </a>
                              <div
                                aria-labelledby="navbarDropdown"
                                className="dropdown-menu mt-0"
                                style={{
                                  padding: "8px 0px",
                                }}
                              >
                                <div
                                  className="container"
                                  style={{
                                    padding: "0px 24px",
                                  }}
                                >
                                  <div className="row my-4">
                                    <div className="col-md-12 col-lg-12 mb-3 mb-lg-0">
                                      <div className="list-group list-group-flush">
                                        <a
                                          className="list-group-item list-group-item-action list-active"
                                          href={
                                            "/products/video-conference/pricing"
                                          }
                                        >
                                          <div
                                            style={{
                                              display: "flex",
                                              alignItems: "center",
                                            }}
                                          >
                                            <img
                                              src="images/meethour.svg"
                                              style={{
                                                height: "26px",
                                                width: "26px",
                                                border: "none",
                                                borderRadius: "unset",
                                              }}
                                            />
                                            <span>
                                              {" "}
                                              {t(
                                                "welcomepage.header.videoConference"
                                              )}
                                            </span>
                                          </div>

                                          <span className="submenu-sub-text">
                                            {t(
                                              "welcomepage.header.videoConferencePricing"
                                            )}
                                          </span>
                                        </a>

                                        <a
                                          className="list-group-item list-group-item-action list-active"
                                          href={"https://mycaly.io/pricing"}
                                          target="_blank"
                                        >
                                          <div
                                            style={{
                                              display: "flex",
                                              alignItems: "center",
                                            }}
                                          >
                                            <img
                                              src="images/mycaly.svg"
                                              style={{
                                                height: "26px",
                                                width: "26px",
                                                border: "none",
                                                borderRadius: "unset",
                                              }}
                                            />
                                            <span>
                                              {" "}
                                              {t("welcomepage.header.myCaly", {
                                                lng: "en",
                                              })}
                                            </span>
                                          </div>

                                          <span className="submenu-sub-text">
                                            {t(
                                              "welcomepage.header.myCalyPricing"
                                            )}
                                          </span>
                                        </a>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </li>
                          </ul>

                          <ul className="float-right-desktop d-flex">
                            <li style={styleWeb.languageContainer}>
                              <LanguagesDropdown />
                            </li>
                            <li
                              className="d-flex align-items-center"
                              style={styleWeb.headerliRight}
                            >
                              <a href="/joinmeeting">
                                {" "}
                                {t("welcomepage.header.joinAMeeting")}
                              </a>
                            </li>
                            {logoutDocument}
                          </ul>
                        </div>
                      </div>

                      {/* <ul className="float-right-desktop">
                                                    <li
                                                        style={
                                                            styleWeb.headerliRight
                                                        }
                                                    >
                                                        <a href="/joinmeeting">
                                                            Join a Meeting
                                                        </a>
                                                    </li>
                                                    {logoutDocument}
                                                </ul> */}
                    </nav>
                  </MediaQuery>
                </>
              )}
            </div>
          </header>
        ) : (
          <header className={"fixed-top d-flex align-items-center"} id="header">
            <div className="container d-flex align-items-center">
              <div className="logo mr-auto" style={styleWeb.width300}>
                <Watermark />
              </div>

              {interfaceConfig && !interfaceConfig.disablePrejoinHeader && (
                <>
                  {/* <MediaQuery maxWidth = { 1000 }>
                                    <button
                                        className = 'mobile-nav-toggle d-lg-none'
                                        onClick = { openNavbar }
                                        type = 'button'><i className = 'icofont-navigation-menu' /></button>
                                </MediaQuery> */}
                  <MediaQuery minWidth={1000}>
                    <nav
                      className="nav-menu d-none d-lg-block desktop-width "
                      style={styleWeb.width100}
                    >
                      <ul className="float-left-desktop">
                        <li>
                          <a href="#features">
                            {" "}
                            {t("welcomepage.header.features")}
                          </a>
                        </li>
                        <li className="drop-down">
                          <a href="">{t("welcomepage.header.solutions")}</a>
                          <ul className="drop-down1">
                            <li>
                              <a href="/explore/hospitals.html">
                                {t("welcomepage.forHospitals")}
                              </a>
                            </li>
                            {/* <li><a href = '/enterprise'>For Enterprises</a>

                                                    </li>
                                                    <li><a href = '/universities'>For Universities</a></li>
                                                    <li><a href = '/raisingfunds'>For Raising Funds</a></li>
                                                    <li><a href = '/virtualclasses'>For Virtual Classes</a></li>
                                                    <li><a href = '/mainstreammedia'>For Mainstream Media</a></li> */}
                          </ul>
                        </li>
                        <li className="drop-down">
                          <a href="#pricing">
                            {t("welcomepage.header.pricing")}
                          </a>
                          <ul className="drop-down1">
                            <li>
                              <a href="https://meethour.io/#pricing">
                                {t("welcomepage.freePlan", { lng: "en" })}
                              </a>
                              <a
                                href="https://repo.meethour.io/landing/pro.html?location=meethour_homepage_pricing_menu"
                                target="_blank"
                              >
                                {t("welcomepage.proPlan", { lng: "en" })}
                              </a>
                              <a
                                href="https://repo.meethour.io/landing/developer.html?location=meethour_homepage_pricing_menu"
                                target="_blank"
                              >
                                {t("welcomepage.developerPlan", { lng: "en" })}
                              </a>
                              <a
                                href="https://repo.meethour.io/landing/enterprise.html?location=meethour_homepage_pricing_menu"
                                target="_blank"
                              >
                                {t("welcomepage.enterpriseSelfHostPlan", {
                                  lng: "en",
                                })}
                              </a>
                            </li>
                          </ul>
                        </li>
                      </ul>
                      <ul className="float-right-desktop"></ul>
                      <li style={styleWeb.languageContainer}>
                        <LanguagesDropdown />
                      </li>
                      <li
                        className="d-flex align-items-center"
                        style={styleWeb.headerliRight}
                      >
                        <a href="/joinmeeting">
                          {t("welcomepage.header.joinAMeeting")}
                        </a>
                      </li>
                      {logoutDocument}
                    </nav>
                  </MediaQuery>
                </>
              )}
            </div>
          </header>
        ))}
    </React.Fragment>
  );
};
