/* @flow */

import React, { Component } from 'react';

import { _openDesktopApp } from '../../../../deep-linking/openDesktopApp';
import { translate } from '../../../i18n';
import { connect } from '../../../redux';

declare var interfaceConfig: Object;

/**
 * The CSS style of the element with CSS class {@code rightwatermark}.
 *
 * @private
 */
const _RIGHT_WATERMARK_STYLE = {
    backgroundImage: 'url(images/watermark.png)'
};

/**
 * The type of the React {@code Component} props of {@link Watermarks}.
 */
type Props = {

    /**
     * The user selected url used to navigate to on logo click.
     */
    _customLogoLink: string,

    /**
     * The url of the user selected logo.
     */
    _customLogoUrl: string,

    /**
     * Whether or not the current user is logged in through a JWT.
     */
    _isGuest: boolean,

    /**
     * Flag used to signal that the logo can be displayed.
     * It becomes true after the user customization options are fetched.
     */
    _readyToDisplayMHWatermark: boolean,

    /**
     * Returns true if welcome page is visible at the moment.
     */
    _welcomePageIsVisible: boolean,

    /**
     * Invoked to obtain translated strings.
     */
    t: Function,

    /**
     * Jwt.
     */
     _jwt: String,

    /**
    * State.
    */
    state: ?Object
};

/**
 * The type of the React {@code Component} state of {@link Watermarks}.
 */
type State = {

    /**
     * The url to open when clicking the brand watermark.
     */
    brandWatermarkLink: string,

    /**
     * The url to open when clicking the Meet Hour watermark.
     */
    MHWatermarkLink: string,

    /**
     * Whether or not the brand watermark should be displayed.
     */
    showBrandWatermark: boolean,

    /**
     * Whether or not the Meet Hour watermark should be displayed.
     */
    showMHWatermark: boolean,

    /**
     * Whether or not the Meet Hour watermark should be displayed for users not
     * logged in through a JWT.
     */
    showMHWatermarkForGuests: boolean,

    /**
     * Whether or not the show the "powered by Meet Hour.org" link.
     */
    showPoweredBy: boolean,

    /**
     * Default Logo url.
     */
     defaulLogoUrl: string,
};

/**
 * A Web Component which renders watermarks such as Jits, brand, powered by,
 * etc.
 */
class Watermark extends Component<Props, State> {
    /**
     * Initializes a new Watermarks instance.
     *
     * @param {Object} props - The read-only properties with which the new
     * instance is to be initialized.
     */
    constructor(props: Props) {
        super(props);

        let showBrandWatermark;
        let showMHWatermark;
        let showMHWatermarkForGuests;

        if (interfaceConfig.filmStripOnly) {
            showBrandWatermark = false;
            showMHWatermark = false;
            showMHWatermarkForGuests = false;
        } else {
            showBrandWatermark = interfaceConfig.SHOW_BRAND_WATERMARK;
            showMHWatermark = interfaceConfig.SHOW_MEET_HOUR_WATERMARK;
            showMHWatermarkForGuests
                = interfaceConfig.SHOW_WATERMARK_FOR_GUESTS;
        }

        this.state = {
            brandWatermarkLink: showBrandWatermark
                ? interfaceConfig.BRAND_WATERMARK_LINK
                : '',
            MHWatermarkLink:
                showMHWatermark || showMHWatermarkForGuests
                    ? interfaceConfig.MEET_HOUR_WATERMARK_LINK
                    : '',
            showBrandWatermark,
            showMHWatermark,
            showMHWatermarkForGuests,
            showPoweredBy: false,
            defaulLogoUrl: interfaceConfig.DEFAULT_LOGO_URL
        };

    }

    /**
     * Implements React's {@link Component#render()}.
     *
     * @inheritdoc
     * @returns {ReactElement}
     */
    render() {
        return (
            <div>
                {this._renderMHWatermark()}
                {this._renderBrandWatermark()}
                {this._renderPoweredBy()}
            </div>
        );
    }

    /**
     * Returns true if the watermark is ready to be displayed.
     *
     * @private
     * @returns {boolean}
     */
    async _canDisplayMHWatermark() {
        const { showMHWatermark, showMHWatermarkForGuests } = this.state;
        const {
            _isGuest,
            _readyToDisplayMHWatermark,
            _welcomePageIsVisible,
            state
        } = this.props;

        const isDesktopApp = await _openDesktopApp(state);

        return (
            (_readyToDisplayMHWatermark
                && (showMHWatermark
                    || (_isGuest && showMHWatermarkForGuests)))
            || _welcomePageIsVisible || isDesktopApp
        );
    }

    /**
     * Renders a brand watermark if it is enabled.
     *
     * @private
     * @returns {ReactElement|null} Watermark element or null.
     */
    _renderBrandWatermark() {
        let reactElement = null;

        if (this.state.showBrandWatermark) {
            reactElement = (
                <div
                    className = 'watermark rightwatermark'
                    style = { _RIGHT_WATERMARK_STYLE } />
            );

            const { brandWatermarkLink } = this.state;

            if (brandWatermarkLink) {
                reactElement = (
                    <a
                        href = { brandWatermarkLink }>
                        {reactElement}
                    </a>
                );
            }
        }

        return reactElement;
    }

    /**
     * Renders a Meet Hour watermark if it is enabled.
     *
     * @private
     * @returns {ReactElement|null}
     */
    _renderMHWatermark() {
        let reactElement = null;
        const { _customLogoUrl, _customLogoLink } = this.props;

        if (this._canDisplayMHWatermark()) {

            const link = _customLogoLink || this.state.MHWatermarkLink;
            const style = {
                styles: _customLogoUrl || interfaceConfig.DEFAULT_LOGO_URL,
                width: 167,
                height: 35

                // maxWidth: 140,
                // maxHeight: 70,
            };

            const reactElement1
                = (<img
                    className = 'img-fluid1'
                    src = { style.styles } />)
            ;

            if (link) {
                reactElement = (
                    <a
                        href = { link } >
                        {reactElement1}
                    </a>
                );
            }
        }

        return reactElement;
    }

    /**
     * Renders a powered by block if it is enabled.
     *
     * @private
     * @returns {ReactElement|null}
     */
    _renderPoweredBy() {
        if (this.state.showPoweredBy) {
            const { t } = this.props;

            return (
                <a
                    className = 'poweredby'
                    href = 'https://meethour.io?web-embed=true'
                    target = '_new'>
                    <span>{t('poweredby')}</span>
                </a>
            );
        }

        return null;
    }
}

/**
 * Maps parts of Redux store to component prop types.
 *
 * @param {Object} state - Snapshot of Redux store.
 * @returns {Props}
 */
function _mapStateToProps(state) {
    const { isGuest } = state['features/base/jwt'];
    const { customizationReady, logoClickUrl, logoImageUrl } = state[
        'features/dynamic-branding'
    ];
    const { room } = state['features/base/conference'];
    const { jwt } = state['features/base/jwt'];

    return {
        /**
         * The indicator which determines whether the local participant is a
         * guest in the conference.
         *
         * @private
         * @type {boolean}
         */
        _customLogoLink: logoClickUrl,
        _customLogoUrl: logoImageUrl,
        _isGuest: isGuest,
        _readyToDisplayMHWatermark: customizationReady,
        _welcomePageIsVisible: !room,
        _jwt: jwt,
        state
    };
}

export default connect(_mapStateToProps)(translate(Watermark));
