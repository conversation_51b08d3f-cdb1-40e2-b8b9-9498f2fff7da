export const styleWeb = {
    premeetingScreen: { background: '#fff !important' },
    premeetingScreenLobby: { height: '800px !important' },
    containers: { maxWidth: '1120px !important' },
    alignCenter: { width: '80%',
        maxWidth: '800px',
        borderRadius: '10px',
        margin: 'auto',
        backgroundColor: '#f6f6f6',
        color: '#000',
        padding: '20px',
        marginTop: '51px' },
    loadingImg: { width: '80px' },
    notAllowed: { width: '141px',
        height: '141px',
        fill: '#d51616' },
    h4Style: { marginTop: '10px',
        color: '#000',
        fontFamily: 'Poppins, sans-serif',
        fontSize: '1.5rem',
        fontWeight: '500',
        lineHeight: '1.2' },
    footerStyle: {
        bottom: '0',
        width: '100%' },
    alignh4: {
        marginTop: '20px',
        marginBottom: '20px',
        fontSize: '1.5rem',
        color: '#444444'
    },
    spanStyle: { color: '#333' },
    spanIstyle: { fontSize: '20px',
        color: '#727272' },
    spanStyle1: {
        margin: 'auto',
        textAlign: 'left',
        fontSize: '13px',
        marginTop: '10px',
        display: 'block'
    },
    aStyle: { textAlign: 'left',
        color: '#4b678f' },
    panelBody: { padding: '20px',
        marginBottom: '20px' },
    conferenceLink: {
        fontSize: '20px',
        color: '#727272'
    },
    panelTitlediv: {
        color: '#555',
        fontFamily: 'Poppins, sans-serif'
    },
    panelTitle1: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-evenly'
    },
    signInButton: {
        color: '#fff'
    },
    meetingTime: {
        marginTop: '10px',
        marginBottom: '10px',
        fontSize: '1.1rem',
        color: '#444444'
    },
    languageContainer: {
        marginRight: '25px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        border: 'none',
        padding: '4px 4px',
        borderRadius: '10px',
        backgroundColor: '#4b6790',
        width: '130px',
        float: 'right',
        position: 'relative',
        zIndex: '400'
    }

};
