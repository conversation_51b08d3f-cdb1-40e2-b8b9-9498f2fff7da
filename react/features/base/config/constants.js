/**
 * The prefix of the {@code localStorage} key into which {@link storeConfig}
 * stores and from which {@link restoreConfig} restores.
 *
 * @protected
 * @type string
 */
export const _CONFIG_STORE_PREFIX = 'config.js';

/**
 * The list of all possible UI buttons.
 *
 * @protected
 * @type Array<string>
 */
export const TOOLBAR_BUTTONS = [
    'microphone', 'camera', 'closedcaptions', 'desktop', 'embedmeeting', 'fullscreen',
    'fodeviceselection', 'hangup', 'profile', 'chat', 'recording', 'reactions',
    'livestreaming', 'etherpad', 'genericiframe', 'sharedvideo', 'settings', 'raisehand',
    'videoquality', 'filmstrip', 'invite', 'feedback', 'stats', 'shortcuts',
    'tileview', 'select-background', 'download', 'help', 'mute-everyone', 'mute-video-everyone', 'security'
];


/**
 * Api Url for joining prejoin.
 */
export const PrejoinApi = 'meeting/save_meeting_guest_details';

export const apiKEY = 'b709af18554cee13bd74d8ab20aad74a4c6d46bd520ede88b54144b40e4ff3a8';

export const mtDomain = 'meethour.io';

export const androidPackageId = 'go.meethour.io';

export const iosPackageId = 'go.meethour.io';


