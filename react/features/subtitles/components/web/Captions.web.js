/* eslint-disable max-len */
// @flow

import DropdownMenu, { DropdownItem, DropdownItemGroup } from '@atlaskit/dropdown-menu';
import React from 'react';

import { translate } from '../../../base/i18n';
import i18next, { TRANSLATION_LANGUAGES_HEAD } from '../../../base/i18n/i18next';
import { connect } from '../../../base/redux';
import { NOTIFICATION_TIMEOUT_TYPE, showErrorNotification, showNotification } from '../../../notifications';
import LIBRE_SUPPORTED_LANGS from '../../../transcribing/libre-supported-langs.json';
import { closeCaptionsModal, setRequestingSubtitles } from '../../actions.web';
import {
    AbstractCaptions,
    type AbstractCaptionsProps as Props,
    _abstractMapStateToProps
} from '../AbstractCaptions';
import { getCaptionsStyles, stylesInMethos } from '../styles';


/**
 * React {@code Component} which can display speech-to-text results from
 * Jigasi as subtitles.
 */
class Captions extends AbstractCaptions<Props> {
    state = {
        position: {
            x: -286,
            y: 20
        },
        isDragging: false,
        isDragged: false,
        dragStart: {
            x: 0,
            y: 0
        },
        dragStartPosition: {
            x: -286,
            y: 20
        },
        isExpanded: false
    };

    containerRef = React.createRef();
    topScrollRef = React.createRef();

    /**
   * Handles the mouse down event to initiate dragging.
   *
   * @param {MouseEvent} e - The mouse event.
   * @private
   * @returns {void}
   */
    _onHandleMouseDown = (e: MouseEvent) => {
        if (e.button !== 0) {
            return;
        } // Only handle left click

        this.setState({
            isDragging: true,
            isDragged: true,
            dragStart: {
                x: e.clientX,
                y: e.clientY
            },
            dragStartPosition: {
                x: this.state.position.x,
                y: this.state.position.y
            }
        });

        e.preventDefault();
    };

    /**
   * Handles the touch start event for mobile devices.
   *
   * @param {TouchEvent} e - The touch event.
   * @private
   * @returns {void}
   */
    _onHandleTouchStart = (e: TouchEvent) => {
        const touch = e.touches[0];

        this.setState({
            isDragging: true,
            isDragged: true,
            dragStart: {
                x: touch.clientX,
                y: touch.clientY
            },
            dragStartPosition: {
                x: this.state.position.x,
                y: this.state.position.y
            }
        });
    };

    /**
   * Handles the mouse move event during dragging.
   *
   * @param {MouseEvent} e - The mouse event.
   * @private
   * @returns {void}
   */
    _handleMouseMove = (e: MouseEvent) => {
        if (!this.state.isDragging) {
            return;
        }

        // Calculate the difference from the initial drag position
        const deltaX = e.clientX - this.state.dragStart.x;
        const deltaY = e.clientY - this.state.dragStart.y;

        // Calculate new position based on the initial position plus the delta
        const newX = this.state.dragStartPosition.x + deltaX;
        const newY = this.state.dragStartPosition.y + deltaY;

        // Check boundaries for Y axis
        const boundedY = Math.max(-250, Math.min(450, newY));

        this.setState({
            position: {
                x: newX,
                y: boundedY
            }
        });
    };

    /**
   * Handles the touch move event for mobile devices.
   *
   * @param {TouchEvent} e - The touch event.
   * @private
   * @returns {void}
   */
    _handleTouchMove = (e: TouchEvent) => {
        if (!this.state.isDragging) {
            return;
        }

        const touch = e.touches[0];

        // Calculate the difference from the initial drag position
        const deltaX = touch.clientX - this.state.dragStart.x;
        const deltaY = touch.clientY - this.state.dragStart.y;

        // Calculate new position based on the initial position plus the delta
        const newX = this.state.dragStartPosition.x + deltaX;
        const newY = this.state.dragStartPosition.y + deltaY;

        // Check boundaries for Y axis
        const boundedY = Math.max(-250, Math.min(450, newY));

        this.setState({
            position: {
                x: newX,
                y: boundedY
            }
        });

        e.preventDefault();
    };

    /**
   * Handles the mouse up event to end dragging.
   *
   * @private
   * @returns {void}
   */
    _handleMouseUp = () => {
        this.setState({ isDragging: false });
    };

    /**
   * Handles the touch end event for mobile devices.
   *
   * @private
   * @returns {void}
   */
    _handleTouchEnd = () => {
        this.setState({ isDragging: false });
    };

    /**
   * Sets up event listeners when component mounts.
   *
   * @returns {void}
   */
    componentDidMount() {
        document.addEventListener('mousemove', this._handleMouseMove);
        document.addEventListener('mouseup', this._handleMouseUp);
        document.addEventListener('touchmove', this._handleTouchMove);
        document.addEventListener('touchend', this._handleTouchEnd);
    }

    /**
   * Cleans up event listeners when component unmounts.
   *
   * @returns {void}
   */
    componentWillUnmount() {
        document.removeEventListener('mousemove', this._handleMouseMove);
        document.removeEventListener('mouseup', this._handleMouseUp);
        document.removeEventListener('touchmove', this._handleTouchMove);
        document.removeEventListener('touchend', this._handleTouchEnd);
    }

    /**
   * Renders the transcription text.
   *
   * @param {string} id - The ID of the transcript message from which the
   * {@code text} has been created.
   * @param {string} text - Subtitles text formatted with the participant's
   * name.
   * @protected
   * @returns {React$Element} - The React element which displays the text.
   */
    _renderParagraph(id: string, text: string): React$Element<*> {


        return (
            <div
                key = { id }
                style = { stylesInMethos.noMargin }>
                <span>{ text }</span>
            </div>
        );
    }

    _onToggleExpand = (e: MouseEvent) => {
        e.stopPropagation(); // Prevent drag start when clicking arrow
        this.setState(prevState => {
            return { isExpanded: !prevState.isExpanded };
        });
    };

    // Close captions on click of close
    _onHandleCloseCaptionsModal = () => {
        const container = this.containerRef.current;

        if (container) {
            container.classList.add('genie-exit');
        }
        setTimeout(() => {
            this.props.dispatch(closeCaptionsModal(false));
        }, 500);
    };


    /**
   * Renders the subtitles container.
   *
   * @param {Array<React$Element>} paragraphs - An array of elements created
   * for each subtitle using the {@link _renderParagraph} method.
   * @protected
   * @returns {React$Element} - The subtitles container.
   */
    _renderSubtitlesContainer(
            paragraphs: Array<React$Element<*>>
    ): React$Element<*> {
        const { position, isDragging, isExpanded, isDragged } = this.state;
        const noLanguageLabel = TRANSLATION_LANGUAGES_HEAD;
        const { _currentLanguage, _requestingCaptionsModal, _language, t, translationEnabled, iAmRecorder, tileviewStatus, isLocalModerator, confSettings, transcription } = this.props;
        const { language: currentLanguage } = i18next;


        const currentLangData = LIBRE_SUPPORTED_LANGS.find(lang => lang.code === currentLanguage);

        const targetLanguages = currentLangData ? currentLangData.targets : [];

        const wordsCount = paragraphsArray => {

            let count = 0;

            paragraphsArray.forEach(paragraphsData => {
                const text = paragraphsData.props.children.props.children;

                if (typeof text === 'string') {
                    count += text.trim().split(/\s+/).length;
                }
            });

            return count;
        };

        const languageClicked = value => {
            const selectedLanguage = value === noLanguageLabel ? null : value;
            const enabled = Boolean(selectedLanguage);
            const displaySubtitles = enabled;

            if (enabled === true) {
                if (transcription?.enabled && Boolean(selectedLanguage) === false) {

                    if (confSettings?.TRANSCRIPTION_QUOTA_EXCEEDED === 1 ?? false) {
                        if (isLocalModerator) {
                            this.props.dispatch(showErrorNotification(
                            {
                                descriptionKey: 'transcribing.transcriptionQuotaExceeded',
                                titleKey: 'transcribing.transcriptionQuotaExceededTitle'
                            }));
                        } else {
                            return;
                        }
                    } else {
                        this.props.dispatch(setRequestingSubtitles(enabled, displaySubtitles, selectedLanguage));
                    }
                } else {
                    this.props.dispatch(setRequestingSubtitles(enabled, displaySubtitles, selectedLanguage));

                }
            } else {
                this.props.dispatch(setRequestingSubtitles(enabled, displaySubtitles, selectedLanguage));
            }
        };

        const styles = getCaptionsStyles(isDragging, position, isExpanded, iAmRecorder, isDragged, tileviewStatus);

        const translationLanguageItems = targetLanguages.length > 0
            ? targetLanguages.map(lang => (
                <DropdownItem
                    key = { lang }
                    onClick = { e => {
                        e.stopPropagation();
                        languageClicked(lang);
                    } }>
                    { t(`languages:${lang}`) }
                </DropdownItem>
            ))
            : (
                <DropdownItem disabled = { true }>
                    { t('languages:not_available') }
                </DropdownItem>
            );

        const getScrolltoTop = () => {

            const topcontainer = this.topScrollRef.current;

            if (topcontainer !== null) {
                topcontainer.scrollTo(0, topcontainer.scrollHeight);
            }

        };

        return (
            _requestingCaptionsModal === true && (
                <div
                    className = { 'transcription-subtitles' }
                    onMouseDown = { this._onHandleMouseDown }
                    onTouchStart = { this._onHandleTouchStart }
                    ref = { this.containerRef }
                    style = { styles.captionsContainer }>
                    <div
                        style = { styles.flexCenter }>
                        <div>
                            <span><img
                                src = 'images/transcription.png'
                                style = { styles.margin5 }
                                width = { 26 } />
                            { this.props.t('transcribing.LiveCaptions') }</span>
                        </div>
                        { <div style = { styles.flexCenter }>
                            <span
                                onClick = { () => {
                                    this.props.dispatch(showNotification({
                                        titleKey: 'transcribing.TranscriptionLanguageCantChange',
                                        descriptionKey: 'transcribing.TranscriptionLanguageCannotBeChangedOngoingCall',
                                        concatText: true,
                                        maxLines: 2
                                    }, NOTIFICATION_TIMEOUT_TYPE.MEDIUM));
                                } }
                                style = { styles.currentLanguageText }>
                                { this.props.t(`languages:${_currentLanguage}`) }
                            </span>
                            {translationEnabled
                            && <div style = { styles.languageDropDown }>
                                <div>
                                    <img
                                        src = 'images/right-arrow.png'
                                        style = { styles.margin5 }
                                        width = { 30 } />
                                </div>

                                {
                                    translationLanguageItems.length > 0

                                        ? <DropdownMenu
                                            className = 'dropdown-min-width'
                                            trigger = { t(`languages:${_language.split('-')[0]}`) }
                                            triggerButtonProps = {{ shouldFitContainer: true,
                                                className: 'captions-language' }}
                                            triggerType = 'button'>
                                            <DropdownItemGroup>{ translationLanguageItems }</DropdownItemGroup>
                                        </DropdownMenu> : <span className = { styles.font16 }> { this.props.t('transcribing.TranslationNotAvailable') }</span> }
                            </div>
                            }

                        </div>}

                        <button
                            aria-label = 'Close captions'
                            onClick = { this._onHandleCloseCaptionsModal }
                            style = { styles.closeButton }>
                            <svg
                                height = '1em'
                                viewBox = '0 0 32 32'
                                width = '1em'
                                xmlns = 'http://www.w3.org/2000/svg'>
                                <path
                                    d = 'M17.414 16L24 9.414L22.586 8L16 14.586L9.414 8L8 9.414L14.586 16L8 22.586L9.414 24L16 17.414L22.586 24L24 22.586z'
                                    fill = 'currentColor' />
                            </svg>
                        </button>
                    </div>
                    <div
                        style = { paragraphs.length >= 2 ? getScrolltoTop() : styles.paragraphWrapper }>
                        <div
                            ref = { this.topScrollRef }
                            // eslint-disable-next-line react-native/no-inline-styles
                            style = {{
                                ...styles.scrollWrapper,
                                height: this.state.isExpanded ? '100px' : '50px',
                                overflowY: 'auto'
                            }}>
                            { paragraphs.length > 0
                                ? paragraphs
                                : this.props.t('transcribing.NoCaptionsAvailable') }
                        </div>
                    </div>

                    {
                        (paragraphs.length >= 2 || wordsCount(paragraphs) > 20)
                        && <button
                            aria-label = { isExpanded ? 'Collapse captions' : 'Expand captions' }
                            onClick = { this._onToggleExpand }
                            style = { styles.closeOrExpandButton }>
                            <svg
                                height = '1.2em'
                                viewBox = '0 0 24 24'
                                width = '1.2em'
                                xmlns = 'http://www.w3.org/2000/svg'>
                                <path
                                    d = 'm7 10l5 5l5-5'
                                    fill = 'none'
                                    stroke = 'currentColor'
                                    strokeLinecap = 'round'
                                    strokeLinejoin = 'round'
                                    strokeWidth = '1.5' />
                            </svg>
                        </button>
                    }


                </div>
            )
        );
    }
}

export default translate(connect(_abstractMapStateToProps)(Captions));
