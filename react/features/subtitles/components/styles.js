// @flow
import { BoxModel, ColorPalette, createStyleSheet } from '../../base/styles';

/**
 * The styles of the React {@code Component}s of the feature subtitles.
 */
export default createStyleSheet({

    languageItemWrapper: {
        alignItems: 'center',
        display: 'flex',
        flexDirection: 'row'
    },

    itemsContainer: {
        marginHorizontal: 24,
        marginVertical: 24
    },

    collapsibleList: {
        alignItems: 'center',
        backgroundColor: '#4B6790',
        borderRadius: 6,
        display: 'flex',
        flexDirection: 'row',
        height: 32,
        padding: 3,
        marginHorizontal: 8,
        marginTop: 10
    },

    listTile: {
        fontSize: 15,
        color: '#ffffff',
        fontWeight: 'bold',
        marginLeft: 8
    },

    arrowIcon: {
        height: 32,
        width: 32,
        borderRadius: 6,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
    },

    /**
     * Style for subtitle paragraph.
     */
    subtitle: {
        backgroundColor: ColorPalette.black,
        borderRadius: BoxModel.margin / 4,
        color: ColorPalette.white,
        paddingHorizontal: BoxModel.padding / 2
    },

    activeLanguageItemText: {
        fontSize: 16,
        lineHeight: 24,
        fontWeight: 800,
        letterSpacing: 0
    },

    languageItemText: {
        marginLeft: 8,
        marginVertical: 8
    },

    iconWrapper: {
        width: 32
    },

    /**
     * Style for the subtitles container.
     */
    subtitlesContainer: {
        alignItems: 'center',
        flexDirection: 'column',
        flexGrow: 0,
        justifyContent: 'flex-end',
        margin: BoxModel.margin,
        height: 40
    },
    subtitlesContainerNative: {
        backgroundColor: '#0045B3',
        flex: 1
    }
});


// eslint-disable-next-line max-params
export const getCaptionsStyles = (isDragging, position, isExpanded, iAmRecorder, isDragged, tileviewStatus) => createStyleSheet({

    /**
     * Style for the captions container.
     */ 
    captionsContainer: {
        cursor: isDragging ? 'grabbing' : 'grab',
        userSelect: 'none',
        transform: isDragging || isDragged
            ? `translate(${position.x}px, ${position.y}px)`
            : tileviewStatus
                ? 'translate(-286px, 30px)'
                : 'translate(-286px, 20px)',
        backgroundColor: '#000000',
        padding: '10px',
        borderRadius: '8px',
        color: 'white',
        touchAction: 'none',
        position: 'absolute',
        display: 'flex',
        flexDirection: 'column'
    },

    flexCenter: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        fontSize: '14px'
    },
    currentLanguageText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: '13px',
        textAlign: 'justify'
    },
    marginBottom15: {
        marginBottom: '15px'
    },
    closeButton: {
        background: 'none',
        border: 'none',
        color: 'white',
        cursor: 'pointer',
        padding: '0'
    },
    noCaptionsTextContainer: {

    },
    closeOrExpandButton: {
        background: 'none',
        border: 'none',
        color: 'white',
        cursor: 'pointer',
        padding: '4px',
        marginTop: '4px',
        alignSelf: 'center',
        transform: isExpanded ? 'rotate(180deg)' : 'rotate(0deg)',
        transition: 'transform 0.3s ease',
        opacity: 0.8,
        ':hover': {
            opacity: 1
        }
    },
    font16: {
        fontSize: '13px',
        margin: '2px'
    },
    gridcontainertranscription: {
        marginBottom: '20px',
        display: 'grid'
    },
    margin5: {
        margin: '5px'
    },
    paragraphWrapper: {
        position: 'relative',
        height: isExpanded ? '150px' : '80px',
        transition: 'height 0.3s ease',
        overflow: 'auto',
        scrollBehavior: 'smooth'
    },
    scrollWrapper: {
        overflowY: 'auto'
    },
    languageDropDown: {
        display: 'flex',
        alignItems: 'center'
    }

});

export const stylesInMethos = createStyleSheet({
    noMargin: {
        margin: '0px !important',
        height: '500px'
    }
});
