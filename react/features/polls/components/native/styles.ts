import { createStyleSheet } from '../../../base/styles/functions.native';


export const dialogStyles = createStyleSheet({

    customContainer: {
        marginBottom: 10,
        marginHorizontal:10,
        marginTop: 5,
    },

    questionText: {
        fontSize: 14,
lineHeight: 20,
fontWeight: '600',
letterSpacing: 0,

        color: "#FFF",
        marginLeft: 4
    },

    questionOwnerText: {
        fontSize: 14,
lineHeight: 20,
fontWeight: '600',
letterSpacing: 0,
        color: "#858585",
        marginBottom: 8,
        marginLeft: 4
    },
    fieldSeparator: {
        borderBottomWidth: 2,
        borderColor: "#666",
        marginVertical: 16
    },

    optionContainer: {
        flexDirection: 'column',
        marginTop: 16,
    },

    optionRemoveButton: {
        marginTop: 8,
    },

    optionRemoveButtonText: {
        color: "#5F82B5"
    },

    field: {
        borderWidth: 1,
        borderColor: "#858585",
        borderRadius: 6,
        color: "#FFF",
        fontSize: 14,
        paddingBottom: 8,
        paddingLeft: 16,
        paddingRight: 16,
        paddingTop: 8
    }
});

export const resultsStyles = createStyleSheet({

    title: {
        fontSize: 24,
        fontWeight: "bold"
    },

    barContainer: {
        backgroundColor: '#ccc',
        borderRadius: 3,
        width: '100%',
        height: 6,
        marginTop: 2
    },

    bar: {
        backgroundColor: "#246FE5",
        borderRadius: 6,
        height: 6
    },

    voters: {
        backgroundColor: "#525252",
        borderColor: "#3D3D3D",
        borderRadius: 6,
        borderWidth: 1,
        padding: 8,
        marginTop: 8
    },

    voter: {
        color: "#FFF"
    },

    answerContainer: {
        marginHorizontal: 4,
        marginVertical: 16,
        maxWidth: '100%'
    },

    answerHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between'
    },

    answer: {
        color: "#FFF",
        flexShrink: 1
    },

    answerVoteCount: {
        paddingLeft: 10
    },

    chatQuestion: {
        fontWeight: "bold"
    }
});

export const chatStyles = createStyleSheet({

    noPollContent: {
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        top: '25%'
    },

    noPollText: {
        flex: 1,
        color: "#858585",
        textAlign: 'center',
        maxWidth: '70%'
    },

    pollItemContainer: {
        backgroundColor: "#000000",
        borderColor: "#5F82B5",
        borderRadius: 6,
        boxShadow: 'inset 0px -1px 0px rgba(255, 255, 255, 0.15)',
        borderWidth: 2,
        padding: 8,
        margin: 16
        // backgroundColor: "#040404",
        // borderColor: "#858585",
        // borderRadius: 6,
        // boxShadow: 'inset 0px -1px 0px rgba(255, 255, 255, 0.15)',
        // borderWidth: 1,
        // padding: 8,
        // margin: 16
    },

    pollCreateContainer: {
        flex: 1,
        padding: 10
    },

    pollCreateSubContainer: {
        flex: 1,
    },

    pollCreateButtonsContainerAndroid: {
        flexDirection: "column",
        padding: 10, 
        gap: 10,
        marginBottom: 10
    },
    marginBottom10: {
      
        marginBottom: 10
    },

    pollCreateButtonsContainerIos: {
        padding: 10,
        gap: 5
    },

    pollSendLabel: {
        color: "#FFF",
        textTransform: 'capitalize'
    },

    pollSendDisabledLabel: {
        color: "#858585",
        textTransform: 'capitalize'
    },

    buttonRow: {
       
        flexDirection: 'row',
        justifyContent: 'space-between'
    },

    answerContent: {
        marginBottom: 8
    },

    switchRow: {
        alignItems: 'center',
        flexDirection: 'row',
        padding: 8
    },

    switchLabel: {
        color: "#FFF",
        marginLeft: 8
    },

    pollCreateAddButton: {
        color: "white",
        backgroundColor: "#5F82B5",
        padding: 10,
        borderRadius: 6,
        textAlign: "center"
    },
    pollCreateButtonContainer: {
        backgroundColor: "#5F82B5",
        padding: 10,
        borderRadius: 6,
        flex: 1,
        minHeight: 50
    },

    pollCreateButton: {
        color: "white",
        textAlign: 'center',
        fontWeight: "bold",
    },

    toggleText: {
        color: "#246FE5"
    },

    createPollButtonIos: {
        marginHorizontal: 20,
        marginVertical: 32
    },

    createPollButtonAndroid: {
        marginHorizontal: 20,
        color: "white",
        fontWeight: "bold",
        textAlign: "center"
    },

    pollPane: {
        flex: 1,
        padding: 10,
    },

    pollPaneContainer: {
        backgroundColor: "gray",
        flex: 1,
        // height: 500,
        padding: 10

    },

    bottomLinks: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginHorizontal: 4
    },

    unreadPollsCounterContainer: {
        display: 'flex',
        flexDirection: 'row'
    },

    unreadPollsCounterDescription: {
        color: "#FFF"
    },

    unreadPollsCounterCircle: {
        backgroundColor: "#F8AE1A",
        borderRadius: 16 / 2,
        height: 16,
        justifyContent: 'center',
        marginLeft: 8,
        width: 16
    },

    unreadPollsCounter: {
        alignSelf: 'center',
        color: "#040404"
    }
});
