/* eslint-disable react-native/no-color-literals */
// @flow

import React from 'react';
import { StyleSheet, Text, View } from 'react-native';

import { translate } from '../../../base/i18n';
import { MHModal } from '../../../base/modal';
import { connect } from '../../../base/redux';
import PollsPane from '../../../polls/components/native/PollsPane';
import { closeChat } from '../../actions.any';
import { CHAT_VIEW_MODAL_ID } from '../../constants';
import AbstractChat, { type Props, _mapStateToProps } from '../AbstractChat';

import ChatInputBar from './ChatInputBar';
import MessageContainer from './MessageContainer';
import MessageRecipient from './MessageRecipient';

export const chatTabBarOptions = {
    swipeEnabled: false,
    tabBarIndicatorStyle: {
        backgroundColor: 'gray'
    },
    tabBarStyle: {
        backgroundColor: '#5F82B5',
        borderBottomColor: 'pink',
        borderBottomWidth: 0.4,
        color: 'white'
    }
};
const styles = StyleSheet.create({
    container: {
        flex: 1
    },
    tabBarContainer: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        alignItems: 'center',
        backgroundColor: '#5F82B5'
    },
    tab: {
        paddingVertical: 15,
        fontWeight: 'bold',
        fontSize: 16,
        color: 'white',
        flex: 1,
        justifyContent: 'center',
        textAlign: 'center'
    },
    activeTab: {
        borderBottomWidth: 3,
        borderBottomColor: 'white'
    },
    tabText: {
        fontWeight: 'bold',
        fontSize: 16,
        color: 'white'
    },
    contentContainer: {
        flex: 1,
        backgroundColor: 'white'
    }
});

/**
 * Implements a React native component that renders the chat window (modal) of
 * the mobile client.
 */
class Chat extends AbstractChat<Props> {
    /**
     * Creates a new instance.
     *
     * @inheritdoc
     */
    constructor(props: Props) {
        super(props);
        this.state = {
            activeTab: 'Chat'
        };
        this._onClose = this._onClose.bind(this);
    }

    /**
     * Renders the tab.
     *
     * @param tabName - TabName.
     */
    renderTab(tabName) {
        const { activeTab } = this.state;
        const { t } = this.props;

        return (
            <Text
                onPress = { () => this.setState({ activeTab: tabName }) }
                style = { [ styles.tab, activeTab === tabName && styles.activeTab ] }>
                {tabName === 'Chat' ? t('chat.tabs.chat') : t('chat.tabs.polls')}
            </Text>
        );
    }

    /**
     * Renders the Content.
     *
     */
    renderContent() {
        const { activeTab } = this.state;

        if (activeTab === 'Chat') {
            return (
                // eslint-disable-next-line react-native/no-inline-styles
                <View style = {{ flex: 1 }}>
                    <MessageContainer messages = { this.props._messages } />
                    <MessageRecipient />
                    <ChatInputBar onSend = { this._onSendMessage } />
                </View>
            );
        } else if (activeTab === 'Polls') {
            return (
                <PollsPane />
            );
        }
    }


    /**
     * Renders the Content.
     *
     */
    toggleInputBar() {
        this.setState(prevState => {
            return {
                showInputBar: !prevState.showInputBar
            };
        });
    }

    /**
     * Implements React's {@link Component#render()}.
     *
     * @inheritdoc
     */
    render() {
        return (
            <MHModal
                headerProps = {{
                    headerLabelKey: 'chat.title'
                }}
                modalId = { CHAT_VIEW_MODAL_ID }
                onClose = { this._onClose }>
                <View style = { styles.container }>
                    <View style = { styles.tabBarContainer }>
                        {this.renderTab('Chat')}
                        {this.renderTab('Polls')}
                    </View>
                    <View style = { styles.contentContainer }>
                        {this.renderContent()}
                    </View>
                </View>
            </MHModal>
        );
    }

    _onSendMessage: (string) => void;

    _onClose: () => boolean;

    /**
     * Closes the modal.
     *
     * @returns {boolean}
     */
    _onClose() {
        this.props.dispatch(closeChat());

        return true;
    }
}

export default translate(connect(_mapStateToProps)(Chat));
