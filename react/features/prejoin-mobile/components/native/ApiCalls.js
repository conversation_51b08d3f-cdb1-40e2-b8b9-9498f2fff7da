
import axios from 'axios';
import { sha256 } from 'js-sha256';
import { isEmpty, isNil } from 'lodash';
import moment from 'moment';
import React, { useEffect, useLayoutEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import logger from '../../../app/logger';
import { apiKEY } from '../../../base/config/constants';
import { JWT_STORAGE } from '../../../base/jwt';
import { fetchPreRegNativeApi, setuserDetails } from '../../../base/mtApi';
import { updateSettings } from '../../../base/settings';
import { isDomainWeborNative } from '../../../base/util/checkOS';
import { LoadConfigOverlay } from '../../../overlay';

import PrejoinPages from './PrejoinPages';


export const rootNavigationRef = React.createRef();

/**
 * Prejoin Pages.
 *
 * @returns {any}
 */
export default function ApiCalls() {
    const dispatch = useDispatch();
    const { API_URL_PREFIX, API_VERSION_PREFIX, MH_CLIENT_ID } = useSelector(state => state['features/base/config']);
    const [ elem, setElem ] = useState(null);
    const [ getJWTFromAPI, setJWTFromAPI ] = useState(null);
    const { jwt: jwtSaved } = useSelector(state => state['features/base/jwt']);
    const roomName = useSelector(state => state['features/base/conference'].room);
    const { accessToken } = useSelector(state => state['features/base/settings']);
    const { userDetails, pregData } = useSelector(state => state['features/base/mtApi']);

    useLayoutEffect(() => {
        // Only dispatch fetchPreRegNativeApi, don't manually set pregData to null
        dispatch(fetchPreRegNativeApi());
    }, []); // Run only once on component mount


    useEffect(() => {
        if (isNil(userDetails) && pregData && accessToken) {

            const moement = moment().seconds(0)
            .milliseconds(0)
            .format('X');

            const mesh256 = sha256(`${apiKEY}:MH:${moement}`);

            const preRegObject = JSON.stringify({
                client_id: MH_CLIENT_ID,
                credentials: mesh256,
                ...isDomainWeborNative,
                'meeting_id': roomName
            });
            const urlConfg = `${API_URL_PREFIX}${API_VERSION_PREFIX}customer/user_details`;

            axios({
                url: urlConfg,
                method: 'post',
                data: preRegObject,
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${accessToken}`
                }
            }
            ).then(({ data: response }) => {
                // eslint-disable-next-line camelcase
                // const isTrueReg = Boolean(pregData?.is_pre_registration);
                // const { jwt } = response?.data ?? {};


                const namess = response?.data?.name;
                const emailss = response?.data?.email;
                const pict = response?.data?.picture;

                dispatch(updateSettings({
                    avatarURL: pict,
                    displayName: namess,
                    email: emailss
                }));
                if (response.success) {
                    setJWTFromAPI(response?.data?.jwt);
                    const getJwt = response?.data?.jwt;

                    if (!isEmpty(getJwt) && !jwtSaved) {
                        dispatch({
                            type: JWT_STORAGE,
                            jwtData: getJwt
                        });
                    }
                }

                dispatch(setuserDetails(response.data));
            })
        .catch(e => {
            logger.warn('errorResponse', e);
        });
        }
    }, [ pregData?.success ]);

    React.useEffect(() => {
        setElem(<LoadConfigOverlay />);
        setElem(<PrejoinPages />);
    }, [ getJWTFromAPI ]);

    // eslint-disable-next-line camelcase
    return elem;

}
