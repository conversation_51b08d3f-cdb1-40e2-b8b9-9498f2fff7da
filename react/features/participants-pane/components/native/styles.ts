
/**
 * The style for participant list description.
 */
const participantListDescription = {
    lineHeight: 26,
    letterSpacing: 0,
    color: '#FFF',
    fontSize: 15,
    fontWeight: 'bold',
    marginLeft: 8,
    paddingVertical: 8,
    position: 'relative',
    width: '70%'
};

/**
 * The style for content.
 */
const flexContent = {
    alignItems: 'center',
    color: '#FFF',
    display: 'flex',
    flex: 1
};

/**
 * The style for the context menu items text.
 */
const contextMenuItemText = {
    fontSize: 16,
    lineHeight: 22,
    fontWeight: '400',
    letterSpacing: 0,
    color: '#FFF'
};

/**
 * The style of the participants pane buttons.
 */
export const button = {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'center'
};

/**
 * The style of the context menu pane items.
 */
const contextMenuItem = {
    alignItems: 'center',
    display: 'flex',
    flexDirection: 'row',
    height: 48,
    marginLeft: 16
};

const participantNameContainer = {
    display: 'flex',
    flexDirection: 'row',
    overflow: 'hidden',
    paddingLeft: 16,
};

/**
 * The styles of the native components of the feature {@code participants}.
 */
export default {

    participantsBadge: {
        backgroundColor: '#3D3D3D',
        borderRadius: 8,
        borderColor: 'white',
        overflow: 'hidden',
        height: 16,
        minWidth: 16,
        color: '#FFF',
        fontSize: 12,
        lineHeight: 16,
        fontWeight: '600',
        letterSpacing: 0.16,
        position: 'absolute',
        right: -3,
        top: -3,
        textAlign: 'center',
        paddingHorizontal: 2
    },

    participantsButtonBadge: {
        display: 'flex',
        position: 'relative'
    },

    participantContainer: {
        alignItems: 'center',
        display: 'flex',
        flexDirection: 'row',
        height: 64,
        paddingLeft: 16,
        paddingRight: 16,
        width: '100%'
    },

    participantContent: {
        alignItems: 'center',
        borderBottomColor: '#292929',
        borderBottomWidth: 2.4,
        display: 'flex',
        flexDirection: 'row',
        height: '100%',
        overflow: 'hidden',
        width: '100%'
    },

    participantDetailsContainer: {
        display: 'flex',
        flexDirection: 'column',
        width: '73%'
    },

    participantDetailsContainerRaisedHand: {
        width: '65%'
    },

    participantNameContainer: {
        ...participantNameContainer,
        width: '100%'
    },

    lobbyParticipantNameContainer: {
        ...participantNameContainer,
        width: '40%'
    },

    participantName: {
        color: '#FFF',
        overflow: 'hidden'
    },

    moderatorLabel: {
        color: '#858585',
        alignSelf: 'flex-start',
        paddingLeft: 16,
        paddingTop: 4
    },

    participantStatesContainer: {
        display: 'flex',
        flexDirection: 'row',
        marginLeft: 'auto',
        width: '15%'
    },

    participantStateVideo: {
        paddingRight: 16,
    },
    participantStateAudio: {
        paddingRight: 16,
    },

    raisedHandIndicator: {
        backgroundColor: '#FFD600',
        borderRadius: 3,
        height: 24,
        width: 24,
        marginLeft: 'auto',
        marginRight: 8
    },

    raisedHandIcon: {
        ...flexContent,
        top: 4,
        color: '#040404'
    },

    buttonAdmit: {
        position: 'absolute',
        right: 16
    },

    buttonReject: {
        position: 'absolute',
        right: 112
    },

    lobbyListDescription: {
        ...participantListDescription
    },

    listDetails: {
        alignItems: 'center',
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between'
    },

    meetingListContainer: {
        paddingHorizontal: 16,
    },

    meetingListDescription: {
        ...participantListDescription
    },

    participantsPaneContainer: {
        backgroundColor: '#141414',
        // backgroundColor: '#141414',
        flex: 1,
        flexDirection: 'column',
        paddingVertical: 8
    },

    participantsPaneFooterContainer: {
        alignItems: 'center',
        bottom: 0,
        height: 128,
        left: 0,
        paddingHorizontal: 24,
        right: 0
    },

    participantsPaneFooter: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'flex-end',
        paddingBottom: 16,
        width: '100%'
    },

    inviteButton: {
        marginLeft: 16,
        marginRight: 16,
        marginVertical: 8,
        backgroundColor: '#4B6790'
    },

    breakoutRoomsButton: {
        marginBottom: 8,
        width: '100%',
        backgroundColor: '#5F82B5'
    },

    moreButton: {
        marginLeft: 8,
        backgroundColor: '#5F82B5'
    },
    primaryButtonStyle: {
        backgroundColor: '#5F82B5',
        color: '#FFFFFF'
    },

    contextMenuItem: {
        ...contextMenuItem
    },

    contextMenuItemSection: {
        ...contextMenuItem
    },

    contextMenuItemSectionAvatar: {
        alignItems: 'center',
        backgroundColor: '#141414',
        borderBottomColor: '#A3A3A3',
        borderBottomWidth: 1,
        borderTopLeftRadius: 16,
        borderTopRightRadius: 16,
        flexDirection: 'row',
        height: 48,
        paddingLeft: 16
    },

    contextMenuItemText: {
        ...contextMenuItemText,
        marginLeft: 16
    },

    contextMenuItemTextNoIcon: {
        ...contextMenuItemText,
        marginLeft: 40
    },

    contextMenuItemName: {
        color: '#040404',
        flexShrink: 1,
        fontSize: 16,
        marginLeft: 16,
        opacity: 0.90
    },

    divider: {
        backgroundColor: '#A3A3A3'
    },

    inputContainer: {
        marginLeft: 16,
        marginRight: 16,
        marginBottom: 24
    },

    centerInput: {
        paddingRight: 16,
        textAlign: 'center'
    },

    visitorsLabel: {
        fontSize: 16,
        lineHeight: 26,
        fontWeight: '600',
        letterSpacing: 0,
        color: '#FFD600',
        marginLeft: 8
    }
};
