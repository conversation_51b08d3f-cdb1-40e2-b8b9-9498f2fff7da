/* eslint-disable react-native/no-inline-styles */

// @flow

import { jitsiLocalStorage } from '@jitsi/js-utils';
import { sha256 } from 'js-sha256';
import _ from 'lodash';
import moment from 'moment';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';

import {
    MyCalyIcon,
    VideoIcon
} from '../../../../static/assets/vendor/svgIcons/svgIcons';
import logger from '../../app/logger';
import { Avatar } from '../../base/avatar';
import { apiKEY } from '../../base/config/constants';
import { JWT_STORAGE } from '../../base/jwt/actionTypes';
import { fetchApi, setuserDetails } from '../../base/mtApi';
import { Watermark } from '../../base/react';
import { getDisplayName, updateSettings } from '../../base/settings';
import { isDomainWeborNative } from '../../base/util/checkOS';
import LanguagesDropdown from '../../languages-component/components/LanguagesDropdown';
import { styleWeb } from '../../welcome/components/stylesWeb';

declare var config: Object;
declare var interfaceConfig: Object;

type Props = {
  prejoin: ?boolean,
};

export const Header = (props: Props) => {
    const { t } = useTranslation();
    const dispatch = useDispatch();
    const locationUrl = window.location.href;
    const [ userd, setUserd ] = useState(undefined);
    const [ loginButton, setLoginButton ] = useState(false);
    const [ rmoveImg, setrmoveImg ] = useState(false);

    const state1 = useSelector(state => state['features/base/jwt']);
    const name = useSelector(state => getDisplayName(state));
    const { userDetails, pregData } = useSelector(
    state => state['features/base/mtApi']
    );
    const userAccessToken = jitsiLocalStorage.getItem('accessToken');

    useEffect(async () => {
        let accesTokn = state1.accessToken ?? userAccessToken;

        if (
            accesTokn === null
      || accesTokn === 'null'
      || accesTokn === undefined
      || accesTokn === ''
        ) {
            if (locationUrl !== '') {
                const url = new URL(locationUrl);
                const location = url.searchParams.get('access_token');

                if (location !== null || location !== '' || location !== undefined) {
                    accesTokn = location;
                    await jitsiLocalStorage.setItem('accessToken', location);
                }
            }
        }

        const moement = moment().seconds(0)
      .milliseconds(0)
      .format('X');

        const mesh256 = sha256(`${apiKEY}:MH:${moement}`);

        if (accesTokn) {
            const preRegObject = JSON.stringify({
                client_id: config.MH_CLIENT_ID,
                credentials: mesh256,
                ...isDomainWeborNative
            });

            fetchApi(
        'customer/user_details',
        'post',
        {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${accesTokn}`
        },
        preRegObject
            )
        .then(response => {
            const isTrueReg: boolean = Boolean(pregData?.is_pre_registration);

            const { jwt } = response.data;

            // Also Skipping the pre-registration page redirect for Recorder
            if (
                isTrueReg
            && !state1.jwt
            && _.isEmpty(jwt)
            && !config?.iAmRecorder === true
            ) {
                return window.location.replace(
              `${config?.brandedPreRegistrationURL
                  ? config?.brandedPreRegistrationURL
                  : pregData.registration_url
              }`
                );
            }
            setUserd(response.data);
            const namess = response?.data?.name;
            const emailss = response?.data?.email;

            dispatch(
            updateSettings({
                displayName: namess,
                email: emailss
            })
            );

            if (response.success) {
                setLoginButton(true);
                const getJwt = response.data?.jwt;

                if (!_.isEmpty(getJwt) && !state1?.jwt) {
                    dispatch({
                        type: JWT_STORAGE,
                        jwtData: getJwt
                    });
                }
            }
            const ele
            = document.getElementById('preloader')
            ?? document.getElementById('preloader1');

            if (ele) {
                setTimeout(() => {
                    ele.style.opacity = '0';
                    setTimeout(() => {
                        ele.style.display = 'none';
                    }, 2000);
                }, 1500);
            }

            dispatch(setuserDetails(response.data));
        })
        .catch(e => {
            logger.warn('errorResponse', e);

            const ele
            = document.getElementById('preloader')
            ?? document.getElementById('preloader1');

            if (ele) {
                ele.style.opacity = '0';
                ele.style.display = 'none';
            }
        });
        }

        if (!accesTokn && loginButton === false) {
            const ele
        = document.getElementById('preloader')
        ?? document.getElementById('preloader1');

            if (ele) {
                setTimeout(() => {
                    ele.style.opacity = '0';
                    setTimeout(() => {
                        ele.style.display = 'none';
                    }, 2500);
                }, 2000);
            }
        }

        if (userDetails) {
            setLoginButton(true);
        }
    }, []);

    const testMouseOut = () => setrmoveImg(false);

    const testMouseOver = () => setrmoveImg(true);

    const loginButtonJSx = (
        <>
            <li className = 'signin-li'>
                <a
                    href = { `${config.URL_PREFIX}serviceLogin?client_id=${config.MH_CLIENT_ID}&redirect_uri=${window.location.href}&device_type=web&response_type=get` }>
                    {t('prejoin.signinsignup')}
                </a>
            </li>
        </>
    );

    /**
 * Event used to logout the user from the app.
 *
 * @param {event} e - Event object..
 * @private
 * @returns {void}
 */
    function logOutClicked(e) {
        e.preventDefault();
        jitsiLocalStorage.clear();
        dispatch(setuserDetails(undefined));
        dispatch(
      updateSettings({
          displayName: undefined,
          email: undefined
      })
        );
        window.location.reload();
    }
    let logoutDocument;

    if (loginButton) {
        logoutDocument = (
            <li className = 'drop-down d-flex'>
                {userd && userd?.picture ? (
                    <img
                        className = 'avatar'
                        src = { userd?.picture }
                        style = { styleWeb.widthandHeight } />
                ) : (
                    <Avatar
                        className = 'premeeting-screen-avatar'
                        displayName = { name }
                        dynamicColor = { true }
                        participantId = 'local'
                        size = { 45 } />
                )}

                <a href = ''>{userd ? userd?.name : ''}</a>
                <ul className = 'drop-down1'>
                    <li>
                        <a
                            href = { `${config.URL_PREFIX}serviceLogin?client_id=${config.MH_CLIENT_ID
                            }&redirect_uri=${config.URL_PREFIX
                            }customer/dashboard&device_type=web${state1.accessToken ?? userAccessToken
                                ? `&access_token=${state1.userAccessToken ?? userAccessToken}`
                                : ''
                            }` }>
                            <i className = 'bx bxs-dashboard' /> {t('prejoin.dashboard')}
                        </a>
                    </li>
                    <li>
                        <a
                            href = { `${config.URL_PREFIX}serviceLogin?client_id=${config.MH_CLIENT_ID
                            }&redirect_uri=${config.URL_PREFIX
                            }customer/profile&device_type=web${state1.accessToken ?? userAccessToken
                                ? `&access_token=${state1.accessToken ?? userAccessToken}`
                                : ''
                            }` }>
                            <i className = 'bx bx-user' /> {t('prejoin.profile')}
                        </a>{' '}
                    </li>
                    <li>
                        <a
                            onClick = { logOutClicked }
                            style = { styleWeb.cusrorPointer }>
                            <i className = 'bx bx-log-out' /> {t('prejoin.logout')}
                        </a>
                    </li>
                </ul>
            </li>
        );
    } else {
        logoutDocument = loginButtonJSx;
    }

    return (
        <React.Fragment>
            <header
                className = { 'fixed-top d-flex align-items-center' }
                id = 'header'
                style = { props.prejoin ? styleWeb.stickyPostion : null }>
                <div className = 'container d-flex align-items-center'>
                    <div
                        className = 'logo mr-auto'
                        style = { styleWeb.width300 }>
                        <Watermark />
                    </div>

                    {interfaceConfig
            && !interfaceConfig.disablePrejoinHeader
            && (config?.MEGAMENU_HEADER === true ? (
                <nav
                    className = 'nav-menu d-none d-lg-flex desktop-width navbar navbar-expand-lg navbar-light nav-menu-nextjs'
                    style = { styleWeb.width100 }>
                    <div className = 'container-fluid'>
                        <button
                            aria-controls = 'navbarExampleOnHover'
                            aria-expanded = 'false'
                            aria-label = 'Toggle navigation'
                            className = 'navbar-toggler px-0'
                            data-mdb-target = '#navbarExampleOnHover'
                            data-mdb-toggle = 'collapse'
                            type = 'button'>
                            <i className = 'fas fa-bars' />
                        </button>

                        <div
                            className = 'collapse navbar-collapse justify-content-between'
                            id = 'navbarExampleOnHover'>
                            <ul className = 'navbar-nav ps-lg-0'>
                                <li className = 'nav-item dropdown dropdown-hover position-static'>
                                    <a
                                        aria-expanded = 'false'
                                        className = 'nav-a dropdown-toggle'
                                        data-mdb-toggle = 'dropdown'
                                        href = '/products'
                                        id = 'navbarDropdown'>
                                        {t('welcomepage.header.products')}
                                    </a>
                                    <div
                                        aria-labelledby = 'navbarDropdown'
                                        className = 'dropdown-menu w-100 mt-0'
                                        style = {{
                                            padding: '0px 25px 0px 5px'
                                        }}>
                                        <div
                                            className = 'container'
                                            style = {{
                                                background: '#415d86'

                                                // padding:"20px",
                                                // chnage if need spacing
                                                // padding: "0px 24px"
                                            }}>
                                            <div className = 'row my-4'>
                                                <div
                                                    className = 'col-md-6 col-lg-6 mb-3 mb-lg-0'
                                                    style = {{
                                                        padding: '0px 12px'
                                                    }}>
                                                    <div
                                                        className = 'list-group list-group-flush'
                                                        style = {{
                                                            background: '#415d86'
                                                        }}>
                                                        <a
                                                            className = 'list-group-item list-group-item-action list-active'
                                                            href = { '/products/video-conference' }
                                                            onMouseOver = { testMouseOver }>
                                                            <div
                                                                style = {{
                                                                    display: 'flex',
                                                                    alignItems: 'center'
                                                                }}>
                                                                <VideoIcon />
                                                                <span
                                                                    style = {{
                                                                        marginLeft: '15px'
                                                                    }}>
                                                                    {t(
                                          'welcomepage.header.videoConference'
                                                                    )}
                                                                </span>
                                                            </div>

                                                            <span className = 'submenu-sub-text'>
                                                                {t(
                                        'welcomepage.header.hdQualityVideoConferenceApp'
                                                                )}
                                                            </span>
                                                        </a>

                                                        <a
                                                            className = 'list-group-item list-group-item-action list-active'
                                                            href = { 'https://mycaly.io/' }
                                                            onMouseOver = { testMouseOut }
                                                            rel = 'noreferrer'
                                                            target = '_blank'>
                                                            <div
                                                                style = {{
                                                                    display: 'flex',
                                                                    alignItems: 'center'
                                                                }}>
                                                                <MyCalyIcon />
                                                                <span
                                                                    style = {{
                                                                        marginLeft: '15px'
                                                                    }}>
                                                                    {t('welcomepage.header.myCaly', {
                                                                        lng: 'en'
                                                                    })}
                                                                </span>
                                                            </div>

                                                            <span className = 'submenu-sub-text'>
                                                                {t(
                                        'welcomepage.header.appointmentSchedulingVideoConference'
                                                                )}
                                                            </span>
                                                        </a>
                                                    </div>
                                                </div>

                                                <div className = 'col-md-6 col-lg-6 mb-3 mb-lg-0'>
                                                    {rmoveImg === false ? (
                                                        <img
                                                            alt = 'Meet Hour Video Conference'
                                                            height = { 216 }
                                                            src = 'images/product-thumb.png'
                                                            width = { 300 } />
                                                    ) : (
                                                        <div
                                                            className = 'list-group list-group-flush'
                                                            style = {{
                                                                padding: '0px 20px'
                                                            }}>
                                                            <h5>
                                                                {t(
                                        'welcomepage.header.videoConferencePlans'
                                                                )}
                                                            </h5>
                                                            <a
                                                                className = 'list-group-item list-group-item-action list-active'
                                                                href = { '/products/video-conference/free' }>
                                                                <div
                                                                    style = {{
                                                                        display: 'flex',
                                                                        alignItems: 'end'
                                                                    }}>
                                                                    {/* <BracketsCurlyIcon /> */}
                                                                    <img
                                                                        src = 'images/free.svg'
                                                                        style = {{
                                                                            height: '26px',
                                                                            width: '26px',
                                                                            border: 'none'
                                                                        }} />

                                                                    <span>
                                                                        {t('welcomepage.header.free', {
                                                                            lng: 'en'
                                                                        })}
                                                                    </span>
                                                                </div>

                                                                <span className = 'submenu-sub-text'>
                                                                    {t(
                                          'welcomepage.header.noTimeLimitGroupCalls'
                                                                    )}
                                                                </span>
                                                            </a>
                                                            <a
                                                                className = 'list-group-item list-group-item-action list-active'
                                                                href = { '/products/video-conference/pro' }>
                                                                <div
                                                                    style = {{
                                                                        display: 'flex',
                                                                        alignItems: 'end'
                                                                    }}>
                                                                    <img
                                                                        src = 'images/pro.svg'
                                                                        style = {{
                                                                            height: '26px',
                                                                            width: '26px',
                                                                            border: 'none'
                                                                        }} />
                                                                    <span>
                                                                        {t('welcomepage.header.pro')}
                                                                    </span>
                                                                </div>

                                                                <span className = 'submenu-sub-text'>
                                                                    {t(
                                          'welcomepage.header.noAdsRecordingLiveStreaming'
                                                                    )}
                                                                </span>
                                                            </a>
                                                            <a
                                                                className = 'list-group-item list-group-item-action list-active'
                                                                href = {
                                                                    '/products/video-conference/developer'
                                                                }>
                                                                <div
                                                                    style = {{
                                                                        display: 'flex',
                                                                        alignItems: 'end'
                                                                    }}>
                                                                    <img
                                                                        src = 'images/developer.svg'
                                                                        style = {{
                                                                            height: '26px',
                                                                            width: '26px',
                                                                            border: 'none'
                                                                        }} />
                                                                    <span>
                                                                        {t('welcomepage.header.developer', {
                                                                            lng: 'en'
                                                                        })}
                                                                    </span>
                                                                </div>

                                                                <span className = 'submenu-sub-text'>
                                                                    {t(
                                          'welcomepage.header.integrateVideoCallWithinYourWebsiteApp'
                                                                    )}
                                                                </span>
                                                            </a>

                                                            <a
                                                                className = 'list-group-item list-group-item-action list-active'
                                                                href = {
                                                                    '/products/video-conference/enterprise'
                                                                }>
                                                                <div
                                                                    style = {{
                                                                        display: 'flex',
                                                                        alignItems: 'end'
                                                                    }}>
                                                                    <img
                                                                        src = 'images/enterprise.svg'
                                                                        style = {{
                                                                            height: '26px',
                                                                            width: '26px',
                                                                            border: 'none'
                                                                        }} />
                                                                    <span>
                                                                        {t('welcomepage.header.enterprise', {
                                                                            lng: 'en'
                                                                        })}
                                                                    </span>
                                                                </div>

                                                                <span className = 'submenu-sub-text'>
                                                                    {t(
                                          'welcomepage.header.customIntegrationDedicatedSupport'
                                                                    )}
                                                                </span>
                                                            </a>

                                                            <a
                                                                className = 'list-group-item list-group-item-action list-active'
                                                                href = {
                                                                    '/products/video-conference/enterprise-selfhost'
                                                                }>
                                                                <div
                                                                    style = {{
                                                                        display: 'flex',
                                                                        alignItems: 'end'
                                                                    }}>
                                                                    <img
                                                                        src = 'images/enterprise-selfhost.svg'
                                                                        style = {{
                                                                            height: '26px',
                                                                            width: '26px',
                                                                            border: 'none'
                                                                        }} />
                                                                    <span>
                                                                        {t(
                                            'welcomepage.header.enterpriseSelfHost',
                                            { lng: 'en' }
                                                                        )}
                                                                    </span>
                                                                </div>

                                                                <span className = 'submenu-sub-text'>
                                                                    {t(
                                          'welcomepage.header.hostVideoConferenceOnYourServers'
                                                                    )}
                                                                </span>
                                                            </a>
                                                            <h5 className = 'mt-20' />
                                                            <a
                                                                className = 'list-group-item list-group-item-action list-active'
                                                                href = {
                                                                    '/products/video-conference/features'
                                                                }>
                                                                <div
                                                                    style = {{
                                                                        display: 'flex',
                                                                        alignItems: 'end'
                                                                    }}>
                                                                    <img
                                                                        src = 'images/features.svg'
                                                                        style = {{
                                                                            height: '26px',
                                                                            width: '26px',
                                                                            border: 'none'
                                                                        }} />
                                                                    <span>
                                                                        {t('welcomepage.header.features')}
                                                                    </span>
                                                                </div>

                                                                <span className = 'submenu-sub-text'>
                                                                    {t(
                                          'welcomepage.header.simplifiedAPIReferences'
                                                                    )}
                                                                </span>
                                                            </a>
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </li>

                                <li className = 'nav-item dropdown dropdown-hover position-static'>
                                    <a
                                        aria-expanded = 'false'
                                        className = 'nav-a dropdown-toggle'
                                        data-mdb-toggle = 'dropdown'
                                        href = '/solutions'
                                        id = 'navbarDropdown'
                                        role = 'button'>
                                        {t('welcomepage.header.solutions')}
                                    </a>
                                    <div
                                        aria-labelledby = 'navbarDropdown'
                                        className = 'dropdown-menu w-100 mt-0'>
                                        <div className = 'container'>
                                            <div
                                                className = 'row my-4'
                                                style = {{
                                                    padding: '20px'
                                                }}>
                                                <div className = 'col-md-6 col-lg-6 mb-3 mb-lg-0'>
                                                    <div
                                                        className = 'list-group list-group-flush'
                                                        style = {{
                                                            paddingRight: '15px'
                                                        }}>
                                                        <h5>{t('welcomepage.header.useCases')}</h5>

                                                        <a
                                                            className = 'list-group-item list-group-item-action list-active'
                                                            href = {
                                                                '/solutions/usecases/videoconferencing'
                                                            }>
                                                            <div
                                                                style = {{
                                                                    display: 'flex',
                                                                    alignItems: 'center'
                                                                }}>
                                                                <img
                                                                    src = 'images/vide-conference.svg'
                                                                    style = {{
                                                                        height: '26px',
                                                                        width: '26px',
                                                                        border: 'none'
                                                                    }} />
                                                                <span>
                                                                    {t(
                                          'welcomepage.header.videoConferencing'
                                                                    )}
                                                                </span>
                                                            </div>

                                                            <span className = 'submenu-sub-text'>
                                                                {t(
                                        'welcomepage.header.customTailoredVideoMeetings'
                                                                )}
                                                            </span>
                                                        </a>
                                                        <a
                                                            className = 'list-group-item list-group-item-action list-active'
                                                            href = { '/solutions/usecases/livestreaming' }>
                                                            <div
                                                                style = {{
                                                                    display: 'flex',
                                                                    alignItems: 'end'
                                                                }}>
                                                                <img
                                                                    src = 'images/live-stream.svg'
                                                                    style = {{
                                                                        height: '26px',
                                                                        width: '26px',
                                                                        border: 'none'
                                                                    }} />
                                                                <span>
                                                                    {t('welcomepage.header.liveStreaming')}
                                                                </span>
                                                            </div>

                                                            <span className = 'submenu-sub-text'>
                                                                {t(
                                        'welcomepage.header.highQualityLiveEventStreaming'
                                                                )}
                                                            </span>
                                                        </a>
                                                        <a
                                                            className = 'list-group-item list-group-item-action list-active'
                                                            href = '/solutions/usecases/virtualclassrooms'>
                                                            <div
                                                                style = {{
                                                                    display: 'flex',
                                                                    alignItems: 'end'
                                                                }}>
                                                                <img
                                                                    src = 'images/virtual-classroms.svg'
                                                                    style = {{
                                                                        height: '26px',
                                                                        width: '26px',
                                                                        border: 'none'
                                                                    }} />
                                                                <span>
                                                                    {t(
                                          'welcomepage.header.virtualClassrooms'
                                                                    )}
                                                                </span>
                                                            </div>

                                                            <span className = 'submenu-sub-text'>
                                                                {t(
                                        'welcomepage.header.interactiveVirtualLearningSolutions'
                                                                )}
                                                            </span>
                                                        </a>

                                                        <a
                                                            className = 'list-group-item list-group-item-action list-active'
                                                            href = { '/solutions/usecases/virtualevents' }>
                                                            <div
                                                                style = {{
                                                                    display: 'flex',
                                                                    alignItems: 'end'
                                                                }}>
                                                                <img
                                                                    src = 'images/virtual-events.svg'
                                                                    style = {{
                                                                        height: '26px',
                                                                        width: '26px',
                                                                        border: 'none'
                                                                    }} />
                                                                <span>
                                                                    {t('welcomepage.header.virtualEvents')}
                                                                </span>
                                                            </div>

                                                            <span className = 'submenu-sub-text'>
                                                                {t(
                                        'welcomepage.header.engagingVirtualEventExperiences'
                                                                )}
                                                            </span>
                                                        </a>
                                                        <a
                                                            className = 'list-group-item list-group-item-action list-active'
                                                            href = { '/solutions/usecases/ekyc' }>
                                                            <div
                                                                style = {{
                                                                    display: 'flex',
                                                                    alignItems: 'end'
                                                                }}>
                                                                <img
                                                                    src = 'images/ekyc.svg'
                                                                    style = {{
                                                                        height: '26px',
                                                                        width: '26px',
                                                                        border: 'none'
                                                                    }} />
                                                                <span>
                                                                    {t('welcomepage.header.videoKYC')}
                                                                </span>
                                                            </div>

                                                            <span className = 'submenu-sub-text'>
                                                                {t(
                                        'welcomepage.header.smoothVideoOnboardingExperience'
                                                                )}
                                                            </span>
                                                        </a>

                                                        <a
                                                            className = 'list-group-item list-group-item-action list-active'
                                                            href = { '/solutions/usecases/webinars' }>
                                                            <div
                                                                style = {{
                                                                    display: 'flex',
                                                                    alignItems: 'end'
                                                                }}>
                                                                <img
                                                                    src = 'images/webinars.svg'
                                                                    style = {{
                                                                        height: '26px',
                                                                        width: '26px',
                                                                        border: 'none'
                                                                    }} />
                                                                <span>
                                                                    {t('welcomepage.header.webinars')}
                                                                </span>
                                                            </div>

                                                            <span className = 'submenu-sub-text'>
                                                                {t(
                                        'welcomepage.header.webinarSessionsWithIndustryLeaders'
                                                                )}
                                                            </span>
                                                        </a>

                                                        <a
                                                            className = 'list-group-item list-group-item-action list-active'
                                                            href = { '/solutions/usecases/fundraising' }>
                                                            <div
                                                                style = {{
                                                                    display: 'flex',
                                                                    alignItems: 'end'
                                                                }}>
                                                                <img
                                                                    src = 'images/fundraising.svg'
                                                                    style = {{
                                                                        height: '26px',
                                                                        width: '26px',
                                                                        border: 'none'
                                                                    }} />
                                                                <span>
                                                                    {t(
                                          'welcomepage.header.fundraisingDonateOnline'
                                                                    )}
                                                                </span>
                                                            </div>

                                                            <span className = 'submenu-sub-text'>
                                                                {t(
                                        'welcomepage.header.fundraiseEffortlesslyWithinVideoConferences'
                                                                )}
                                                            </span>
                                                        </a>
                                                    </div>
                                                </div>
                                                <div className = 'col-md-6 col-lg-6 mb-3 mb-lg-0'>
                                                    <div className = 'list-group list-group-flush'>
                                                        <h5>{t('welcomepage.header.industries')}</h5>

                                                        <a
                                                            className = 'list-group-item list-group-item-action list-active'
                                                            href = { '/solutions/industries/edtech' }>
                                                            <div
                                                                style = {{
                                                                    display: 'flex',
                                                                    alignItems: 'baseline'
                                                                }}>
                                                                <img
                                                                    src = 'images/edtech.svg'
                                                                    style = {{
                                                                        height: '26px',
                                                                        width: '26px',
                                                                        border: 'none'
                                                                    }} />{' '}
                                                                <span>
                                                                    {t('welcomepage.header.edTech')}
                                                                </span>
                                                            </div>

                                                            <span className = 'submenu-sub-text'>
                                                                {t(
                                        'welcomepage.header.engagingOnlineLearningForEducators'
                                                                )}
                                                            </span>
                                                        </a>
                                                        <a
                                                            className = 'list-group-item list-group-item-action list-active'
                                                            href = '/solutions/industries/fitness'>
                                                            <div
                                                                style = {{
                                                                    display: 'flex',
                                                                    alignItems: 'baseline'
                                                                }}>
                                                                <img
                                                                    src = 'images/fitness.svg'
                                                                    style = {{
                                                                        height: '26px',
                                                                        width: '26px',
                                                                        border: 'none'
                                                                    }} />
                                                                <span>
                                                                    {t('welcomepage.header.fitness')}
                                                                </span>
                                                            </div>

                                                            <span className = 'submenu-sub-text'>
                                                                {t(
                                        'welcomepage.header.virtualSolutionForHomeFitness'
                                                                )}
                                                            </span>
                                                        </a>
                                                        <a
                                                            className = 'list-group-item list-group-item-action list-active'
                                                            href = { '/solutions/industries/healthcare' }>
                                                            <div
                                                                style = {{
                                                                    display: 'flex',
                                                                    alignItems: 'baseline'
                                                                }}>
                                                                <img
                                                                    src = 'images/telehealth.svg'
                                                                    style = {{
                                                                        height: '26px',
                                                                        width: '26px',
                                                                        border: 'none'
                                                                    }} />
                                                                <span>
                                                                    {t('welcomepage.header.telehealth')}
                                                                </span>
                                                            </div>

                                                            <span className = 'submenu-sub-text'>
                                                                {t(
                                        'welcomepage.header.tailoredSolutionsForYourHealthcareNeeds'
                                                                )}
                                                            </span>
                                                        </a>
                                                        <img
                                                            alt = 'solutions-header-thumb'
                                                            src = 'images/solutions-header-thumb.png'
                                                            width = { 300 } />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </li>

                                <li className = 'nav-item dropdown dropdown-hover position-static'>
                                    <a
                                        aria-expanded = 'false'
                                        className = 'nav-a dropdown-toggle'
                                        data-mdb-toggle = 'dropdown'
                                        href = '/developers'
                                        id = 'navbarDropdown'
                                        role = 'button'>
                                        {t('welcomepage.header.developers')}
                                    </a>
                                    <div
                                        aria-labelledby = 'navbarDropdown'
                                        className = 'dropdown-menu w-100 mt-0'>
                                        <div className = 'container'>
                                            <div
                                                className = 'row my-4'
                                                style = {{
                                                    padding: '20px'
                                                }}>
                                                <div className = 'col-md-6 col-lg-6 mb-3 mb-lg-0'>
                                                    <div className = 'list-group list-group-flush'>
                                                        <h5>{t('welcomepage.header.getStarted')}</h5>
                                                        <a
                                                            className = 'list-group-item list-group-item-action list-active'
                                                            href = { '/developers/sdks' }>
                                                            <div
                                                                style = {{
                                                                    display: 'flex',
                                                                    alignItems: 'end'
                                                                }}>
                                                                <img
                                                                    src = 'images/sdks.svg'
                                                                    style = {{
                                                                        height: '26px',
                                                                        width: '26px',
                                                                        border: 'none'
                                                                    }} />
                                                                <span>
                                                                    {t('welcomepage.header.preBuiltSDKs')}
                                                                </span>
                                                            </div>

                                                            <span className = 'submenu-sub-text'>
                                                                {t(
                                        'welcomepage.header.accelerateDevelopmentWithPrebuiltSDKs'
                                                                )}
                                                            </span>
                                                        </a>
                                                        <a
                                                            className = 'list-group-item list-group-item-action list-active'
                                                            href = 'https://docs.v-empower.com/docs/MeetHour-API'
                                                            rel = 'noreferrer'
                                                            target = '_blank'>
                                                            <div
                                                                style = {{
                                                                    display: 'flex',
                                                                    alignItems: 'end'
                                                                }}>
                                                                <img
                                                                    src = 'images/documentation.svg'
                                                                    style = {{
                                                                        height: '26px',
                                                                        width: '26px',
                                                                        border: 'none'
                                                                    }} />
                                                                <span>
                                                                    {t('welcomepage.header.documentation')}
                                                                </span>
                                                            </div>
                                                            <span className = 'submenu-sub-text'>
                                                                {t(
                                        'welcomepage.header.simplifiedAPIReferences'
                                                                )}
                                                            </span>
                                                        </a>{' '}
                                                        <img
                                                            alt = 'developers-api-documentation'
                                                            height = { 216 }
                                                            src = 'images/video-conference3.jpg'
                                                            width = { 300 } />
                                                    </div>
                                                </div>
                                                <div
                                                    className = 'col-md-6 col-lg-6 mb-3 mb-lg-0'
                                                    style = {{
                                                        paddingLeft: '30px'
                                                    }}>
                                                    <div className = 'list-group list-group-flush'>
                                                        <h5>{t('welcomepage.header.resources')}</h5>

                                                        <a
                                                            className = 'list-group-item list-group-item-action list-active'
                                                            href = { '/blog' }>
                                                            <div
                                                                style = {{
                                                                    display: 'flex',
                                                                    alignItems: 'end'
                                                                }}>
                                                                <img
                                                                    src = 'images/blog.svg'
                                                                    style = {{
                                                                        height: '26px',
                                                                        width: '26px',
                                                                        border: 'none'
                                                                    }} />
                                                                <span>
                                                                    {t('welcomepage.header.blog')}
                                                                </span>
                                                            </div>

                                                            <span className = 'submenu-sub-text'>
                                                                {t(
                                        'welcomepage.header.stayuptodateWithOurBlog'
                                                                )}
                                                            </span>
                                                        </a>
                                                        <a
                                                            className = 'list-group-item list-group-item-action list-active'
                                                            href = { '/developers/resources/api-status' }>
                                                            <div
                                                                style = {{
                                                                    display: 'flex',
                                                                    alignItems: 'end'
                                                                }}>
                                                                <img
                                                                    src = 'images/api-status.svg'
                                                                    style = {{
                                                                        height: '26px',
                                                                        width: '26px',
                                                                        border: 'none'
                                                                    }} />
                                                                <span>
                                                                    {t('welcomepage.header.apiStatus')}
                                                                </span>
                                                            </div>

                                                            <span className = 'submenu-sub-text'>
                                                                {t(
                                        'welcomepage.header.systemHealthStatusandUpdates'
                                                                )}
                                                            </span>
                                                        </a>

                                                        <a
                                                            className = 'list-group-item list-group-item-action list-active'
                                                            href = {
                                                                'http://kb.meethour.io/category/how-to'
                                                            }
                                                            rel = 'noreferrer'
                                                            target = '_Blank'>
                                                            <div
                                                                style = {{
                                                                    display: 'flex',
                                                                    alignItems: 'end'
                                                                }}>
                                                                <img
                                                                    src = 'images/knowledge-base.svg'
                                                                    style = {{
                                                                        height: '26px',
                                                                        width: '26px',
                                                                        border: 'none'
                                                                    }} />
                                                                <span>
                                                                    {t('welcomepage.header.knowledgeBase')}
                                                                </span>
                                                            </div>{' '}
                                                            <span className = 'submenu-sub-text'>
                                                                {t(
                                        'welcomepage.header.systemHealthStatusandUpdates'
                                                                )}
                                                            </span>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </li>

                                <li className = 'nav-item dropdown dropdown-hover position-static'>
                                    <a
                                        aria-expanded = 'false'
                                        className = 'nav-a dropdown-toggle'
                                        data-mdb-toggle = 'dropdown'
                                        href = '/products/video-conference/pricing'
                                        id = 'navbarDropdown'>
                                        {t('welcomepage.header.pricing')}
                                    </a>
                                    <div
                                        aria-labelledby = 'navbarDropdown'
                                        className = 'dropdown-menu mt-0'
                                        style = {{
                                            padding: '8px 0px'
                                        }}>
                                        <div
                                            className = 'container'
                                            style = {{
                                                padding: '0px 24px'
                                            }}>
                                            <div className = 'row my-4'>
                                                <div className = 'col-md-12 col-lg-12 mb-3 mb-lg-0'>
                                                    <div className = 'list-group list-group-flush'>
                                                        <a
                                                            className = 'list-group-item list-group-item-action list-active'
                                                            href = { '/products/video-conference/pricing' }>
                                                            <div
                                                                style = {{
                                                                    display: 'flex',
                                                                    alignItems: 'center'
                                                                }}>
                                                                <img
                                                                    src = 'images/meethour.svg'
                                                                    style = {{
                                                                        height: '26px',
                                                                        width: '26px',
                                                                        border: 'none'
                                                                    }} />
                                                                <span>
                                                                    {t(
                                          'welcomepage.header.videoConference'
                                                                    )}
                                                                </span>
                                                            </div>

                                                            <span className = 'submenu-sub-text'>
                                                                {t(
                                        'welcomepage.header.videoConferencePricing'
                                                                )}
                                                            </span>
                                                        </a>

                                                        <a
                                                            className = 'list-group-item list-group-item-action list-active'
                                                            href = { 'https://mycaly.io/pricing' }
                                                            rel = 'noreferrer'
                                                            target = '_blank'>
                                                            <div
                                                                style = {{
                                                                    display: 'flex',
                                                                    alignItems: 'center'
                                                                }}>
                                                                <img
                                                                    src = 'images/mycaly.svg'
                                                                    style = {{
                                                                        height: '26px',
                                                                        width: '26px',
                                                                        border: 'none'
                                                                    }} />
                                                                <span>
                                                                    {t('welcomepage.header.myCaly', {
                                                                        lng: 'en'
                                                                    })}
                                                                </span>
                                                            </div>

                                                            <span className = 'submenu-sub-text'>
                                                                {t('welcomepage.header.myCalyPricing')}
                                                            </span>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                            </ul>

                            <ul className = 'float-right-desktop d-flex'>
                                <li style = { styleWeb.languageContainer }>
                                    <LanguagesDropdown />
                                </li>{' '}
                                <li style = { styleWeb.headerliRight }>
                                    <a href = '/joinmeeting'>
                                        {t('welcomepage.header.joinAMeeting')}
                                    </a>
                                </li>
                                <li>{logoutDocument}</li>
                            </ul>
                        </div>
                    </div>

                    {/* <ul className="float-right-desktop">
                                    <li
                                        style={
                                            styleWeb.headerliRight
                                        }
                                    >
                                        <a href="/joinmeeting">
                                            Join a Meeting
                                        </a>
                                    </li>
                                    {logoutDocument}
                                </ul> */}
                </nav>
            ) : (
                <nav
                    className = 'nav-menu d-none d-lg-block desktop-width '
                    style = { styleWeb.width100 }>
                    <ul className = 'float-left-desktop'>
                        <li>
                            <a href = '#features'>{t('welcomepage.header.features')}</a>
                        </li>
                        <li className = 'drop-down'>
                            <a href = ''>{t('welcomepage.header.solutions')}</a>
                            <ul className = 'drop-down1'>
                                <li>
                                    <a href = '/explore/hospitals.html'>
                                        {t('welcomepage.forHospitals')}
                                    </a>
                                </li>
                                {/* <li><a href = '/enterprise'>For Enterprises</a>

                                            </li>
                                            <li><a href = '/universities'>For Universities</a></li>
                                            <li><a href = '/raisingfunds'>For Raising Funds</a></li>
                                            <li><a href = '/virtualclasses'>For Virtual Classes</a></li>
                                            <li><a href = '/mainstreammedia'>For Mainstream Media</a></li> */}
                            </ul>
                        </li>
                        <li className = 'drop-down'>
                            <a href = '#pricing'>{t('welcomepage.header.pricing')}</a>
                            <ul className = 'drop-down1'>
                                <li>
                                    <a href = 'https://meethour.io/#pricing'>
                                        {t('welcomepage.freePlan', { lng: 'en' })}
                                    </a>
                                    <a
                                        href = 'https://repo.meethour.io/landing/pro.html?location=meethour_homepage_pricing_menu'
                                        rel = 'noreferrer'
                                        target = '_blank'>
                                        {t('welcomepage.proPlan', { lng: 'en' })}
                                    </a>
                                    <a
                                        href = 'https://repo.meethour.io/landing/developer.html?location=meethour_homepage_pricing_menu'
                                        rel = 'noreferrer'
                                        target = '_blank'>
                                        {t('welcomepage.developerPlan', { lng: 'en' })}
                                    </a>
                                    <a
                                        href = 'https://repo.meethour.io/landing/enterprise.html?location=meethour_homepage_pricing_menu'
                                        rel = 'noreferrer'
                                        target = '_blank'>
                                        {t('welcomepage.enterpriseSelfHostPlan', {
                                            lng: 'en'
                                        })}
                                    </a>
                                </li>
                            </ul>
                        </li>
                    </ul>
                    <ul className = 'float-right-desktop'>
                        <li style = { styleWeb.languageContainer }>
                            <LanguagesDropdown />
                        </li>
                        <li
                            className = 'd-flex align-items-center'
                            style = { styleWeb.headerliRight }>
                            <a href = '/joinmeeting'>
                                {t('welcomepage.header.joinAMeeting')}
                            </a>
                        </li>
                        {logoutDocument}
                    </ul>
                </nav>
            ))}
                </div>
            </header>
        </React.Fragment>
    );
};
