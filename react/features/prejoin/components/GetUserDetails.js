/* eslint-disable */
import { jitsiLocalStorage } from '@jitsi/js-utils';
import { sha256 } from 'js-sha256';
import _ from 'lodash';
import moment from 'moment';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import MediaQuery from 'react-responsive';

import { openConnection } from '../../../../connection';
import { Avatar } from '../../base/avatar';
import { apiKEY } from '../../base/config/constants';
import { connect } from '../../base/connection';
import { JWT_STORAGE, SET_ACCESS_TOKEN } from '../../base/jwt/actionTypes';
import { MHConnectionErrors } from '../../base/lib-meet-hour';
import { fetchApi, setuserDetails } from '../../base/mtApi';
import { Watermark } from '../../base/react';
import { getDisplayName, updateSettings } from '../../base/settings';
import { getBackendSafeRoomName, parseURLParams } from '../../base/util';
import { isDomainWeborNative } from '../../base/util/checkOS';


declare var config: object;
declare var APP: object;
declare var interfaceConfig: object;

export default function GetUserDetails() {
    const dispatch = useDispatch();
    const [ userd, setUserd ] = useState(undefined);
    const [ loginButton, setLoginButton ] = useState(false);
    const isMounted = React.useRef(false);
    const state1 = useSelector(state => state['features/base/jwt']);
    const name = useSelector(state => getDisplayName(state));
    const { userDetails, pregData } = useSelector(state => state['features/base/mtApi']);

    const userAccessToken = jitsiLocalStorage.getItem('accessToken');
    const roomName = getBackendSafeRoomName(useSelector(state => state['features/base/conference'].room));
    const { locationURL } = useSelector(state => state['features/base/connection']);
    const urlPathName = locationURL?.pathname?.length;
    const accesToknn = state1.accessToken ?? userAccessToken;

    useEffect(() => {
        // console.log('userAccessToken ==>', userAccessToken, 'state1 ==>', state1.accessToken, 'userDetails ==>', userDetails);
        const accesTokn = state1.accessToken ?? userAccessToken;

        const urlTest = locationURL?.pathname?.length;
        const urrr = parseURLParams(locationURL, true, 'search');

        if ((_.isNil(userDetails) && pregData && accesTokn) && isMounted.current === true) {

            const moement = moment().seconds(0)
            .milliseconds(0)
            .format('X');

            const mesh256 = sha256(`${apiKEY}:MH:${moement}`);

            const preRegObject = JSON.stringify({
                'client_id': config.MH_CLIENT_ID,
                'credentials': mesh256,
                ...isDomainWeborNative,
                'meeting_id': roomName
            });

            fetchApi('customer/user_details', 'post', {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${state1.accessToken ?? userAccessToken}`
            },
            preRegObject
            ).then(response => {
                // eslint-disable-next-line camelcase
                // const isTrueGuest: boolean = Boolean(preRegData?.allow_guest === 1);
                const { jwt = '' } = response?.data ?? {};

                setUserd(response.data);
                const namess = response?.data?.name;
                const emailss = response?.data?.email;
                const pict = response?.data?.picture;

                dispatch(updateSettings({
                    avatarURL: pict,
                    displayName: namess,
                    email: emailss
                }));

                if (response.success) {
                    setLoginButton(true);
                    dispatch({ type: SET_ACCESS_TOKEN,
                        accessToken: state1.accessToken ?? userAccessToken });
                    const getJwt = response.data?.jwt;

                    if (!_.isEmpty(getJwt) && !state1?.jwt) {
                        dispatch({
                            type: JWT_STORAGE,
                            jwtData: getJwt
                        });
                    }
                } else {
                    jitsiLocalStorage.removeItem('accessToken');
                }


                dispatch(setuserDetails(response.data));
            })
.catch(e => {
    console.log('errorResponse1234', e);
});

        }

        isMounted.current = true;


    }, [ accesToknn ]);

    return <></>;
}
