/* eslint-disable no-negated-condition */
/* eslint-disable react/jsx-indent-props */
/* eslint-disable indent */
/* eslint-disable react/jsx-no-target-blank */
/* eslint-disable react/jsx-indent */

// @flow

import InlineDialog from '@atlaskit/inline-dialog';
import { BrowserDetection, jitsiLocalStorage } from '@jitsi/js-utils';
import { sha256 } from 'js-sha256';
import jwtDecode from 'jwt-decode';
import _, { isNil } from 'lodash';
import moment from 'moment-timezone';
import React, { Component } from 'react';
import MediaQuery from 'react-responsive';
import type { Dispatch } from 'redux';

import { SAFARI } from '../../../../browser';
import logger from '../../app/logger';
import { Avatar } from '../../base/avatar';
import { getRoomName, isLoading<PERSON>he<PERSON> } from '../../base/conference';
import { PrejoinApi, apiKEY, mtDomain } from '../../base/config/constants';
import { connect as mtConnection, setLocationURL } from '../../base/connection';
import { translate } from '../../base/i18n';
import { Icon, IconArrowDown, IconArrowUp, IconNotAllowed, IconParticipants, IconPhone, IconVolumeOff } from '../../base/icons';
import { setAccessToken, setJwtStorage, setJWT as setjWT } from '../../base/jwt/actions';
import { isVideoMutedByUser, setVideoMuted } from '../../base/media';
import { fetchApi } from '../../base/mtApi/functions';
import { ActionButton, InputField, PreMeetingScreen, ToggleButton } from '../../base/premeeting';
import { connect, equals } from '../../base/redux';
import { getDisplayName, updateSettings } from '../../base/settings';
import { getLocalMHVideoTrack, isLocalCameraTrackMuted } from '../../base/tracks';
import { getBackendSafeRoomName } from '../../base/util';
import LanguagesDropdown from '../../languages-component/components/LanguagesDropdown';
import { openLobbyScreen } from '../../lobby/actions.web';
import { showErrorNotification } from '../../notifications';
import { isButtonEnabled } from '../../toolbox/functions.web';
import { VideoBackgroundButton, checkBlurSupport } from '../../virtual-background';
import { styleWeb } from '../../welcome/components/stylesWeb';
import {
    appSetMuted,
    joinConference as joinConferenceAction,
    joinConferenceWithoutAudio as joinConferenceWithoutAudioAction,
    setPrejoinApiData as prejoinApi,
    setJoinByPhoneDialogVisiblity as setJoinByPhoneDialogVisiblityAction,
    setPrejoinPageVisibility, setSkipPrejoin as setSkipPrejoinAction } from '../actions';
import {
    disableEmailOnPrejoin,
    isDeviceStatusVisible,
    isDisplayNameRequired,
    isJoinByPhoneButtonVisible,
    isJoinByPhoneDialogVisible,
    isPrejoinSkipped
} from '../functions';

import GetUserDetails from './GetUserDetails';
import { MaterialNotifier } from './MaterialNotifier';
import OauthPopup from './OauthPopup';
import JoinByPhoneDialog from './dialogs/JoinByPhoneDialog';
import DeviceStatus from './preview/DeviceStatus';
import { stylePrejoin } from './style';


declare var interfaceConfig: Object;
declare var config: Object;

const visibleButtons = new Set(interfaceConfig.TOOLBAR_BUTTONS);

export const PrejoinRef = React.createRef();


type Props = {


    /**
     * AccessToken.
     */
     accessToken: ?string,

    /**
     * Conference settings from jwt token.
     */
    confSettings: object,

    /**
     * Connect to the conference.
     */
    connect: Function,

    /**
     * The Redux dispatch function.
     */
    dispatch: Dispatch<any>,

    /**
     * Flag signaling if the 'skip prejoin' button is toggled or not.
     */
    buttonIsToggled: boolean,

    /**
     * Flag signaling if the device status is visible or not.
     */
    deviceStatusVisible: boolean,

    /**
     * If join by phone button should be visible.
     */
    hasJoinByPhoneButton: boolean,

    /**
     * Joins the current meeting.
     */
    joinConference: Function,

    /**
     * Joins the current meeting without audio.
     */
    joinConferenceWithoutAudio: Function,


    /**
     * Invlaid Data.
     */
    _invalidData: ?Object,

    /**
     * Localparticipant data.
     */
     _localParticipant: ?Array<*>,

    /**
     * Meeting time according to the jwt.
     */
     _mtTime: object,

    /**
     * Meeting details in jwt.
     */
     _meetingDetails: ?Object,

    /**
     * The name of the user that is about to join.
     */
    name: string,

    /**
     * Email of the userr.
     */
    email: string,

    /**
     * Updates settings.
     */
    updateSettings: Function,

    /**
     * Pre Registration api repsonse object.
     */
    preRegData: object,

    /**
     * Prejoin api data objects.
     */
    prejoinApi: object,

    /**
     * The name of the meeting that is about to be joined.
     */
    roomName: string,

    /**
     * Sets visibility of the prejoin page for the next sessions.
     */
    setSkipPrejoin: Function,

    /**
     * Sets visibility of the 'JoinByPhoneDialog'.
     */
    setJoinByPhoneDialogVisiblity: Function,

    /**
     * Sets visibility of the 'PrejoinPage'.
     */
    setPrejoinPageVisibility: Function,

    /**
     * Open Lobby Screen.
     */
    openLobbyScreen: Function,

    /**
     * SetJWt function.
     */
     setJWT: Function,

    /**
     * Indicates whether the avatar should be shown when video is off.
     */
    showAvatar: boolean,

    /**
     * Error notification function.
     */
    showErrorNotification: Function,

    /**
     * Flag signaling the visibility of camera preview.
     */
    showCameraPreview: boolean,

    /**
     * If should show an error when joining without a name.
     */
    showErrorOnJoin: boolean,

    /**
     * Flag signaling the visibility of join label, input and buttons.
     */
    showJoinActions: boolean,

    /**
     * Flag signaling the visibility of the conference URL section.
     */
    showConferenceInfo: boolean,

    /**
     * If 'JoinByPhoneDialog' is visible or not.
     */
    showDialog: boolean,

    /**
     * Flag signaling the visibility of the skip prejoin toggle.
     */
    showSkipPrejoin: boolean,

    /**
     * Used for translation.
     */
    t: Function,

    /**
     * User Details.
     */
     _userDetails: ?Object,

    /**
     * Confernce.
     */
     conference: Object,

     /**
      * Participats in call.
      */
    _participantInCall: ?Object,

    /**
     * Jwt token.
     */
    jwt: ?String,

    /**
     * Display Name.
     */
    displayName: ?String,

    /**
     * Room name.
     */
     _roomName: ?String,

    /**
     * Another Jwt stored.
     */
    _jwtStored: string,

    /**
     * The MHLocalTrack to display.
     */
    videoTrack: ?Object,

    /**
     * Visible buttons.
     */
     _visibleButtons: Set<string>,

     /**
      * FetchJwtStorage for guest.
      */
    fetchJwtStorage: Function,

    /**
     * AddLoader in prejoin.
     */
    addLoader: Function,

    /**
     * Loader reducer check.
     */
     isLoader: boolean,

     /**
     * _isLobbyScreenVisible.
     */
    _isLobbyScreenVisible: boolean,

    /**
     * Sets Access Token to localStorage instance and also in state.
    */
    setAccessToken: ?Function,

    /**
     * To Disable Email Field in Prejoin page.
     */
    disableEmail: ?boolean
};

type State = {

    /**
     * Flag controlling the visibility of the error label.
     */
    showError: boolean,

    /**
     * Shows message in notificatio.
     */
    showMessage: string,


    /**
     * Flag controlling the visibility of the 'join by phone' buttons.
     */
    showJoinByPhoneButtons: boolean,

    /**
     * Token from verfication.
     */
    token: string,

    /**
     * ParticipantCount.
     */
    participantCount: ?number,

    /**
     * COnference Timer.
     */
    ConferenceTimer: ?boolean,

    /**
     * Meeting Notification Details.
     */
    meetingNotifier: ?Object,

    /**
     * Meeting Notficiation opener.
     */
     openNotification: ?boolean
}

// eslint-disable-next-line valid-jsdoc
/**
 * This component is displayed before joining a meeting.
 *
 * @class Prejoin
 */
class Prejoin extends Component<Props, State> {
    _additionalToolbarContentRef: ?HTMLTemplateElement;
    _additionalCardRef: ?HTMLTemplateElement;

    /**
     * Default values for {@code Prejoin} component's properties.
     *
     * @static
     */
    static defaultProps = {
        showConferenceInfo: true,
        showJoinActions: true,
        showSkipPrejoin: true
    };

    /**
     * Initializes a new {@code Prejoin} instance.
     *
     * @inheritdoc
     */
    constructor(props) {
        super(props);

        this.state = {
            showError: false,
            showJoinByPhoneButtons: false,
            token: '',
            userDetails: null,
            meetButton: false,
            verifyGoogle: false,
            isLoading: false,
            jwtConnect: false,
            textFlow: undefined,
            participantCount: undefined,
            ConferenceTimer: undefined,
            showMessage: undefined,
            maxparticipants: null,
            setPopUp: false,
            meetingNotifier: {
                alertType: null,
                description: null

            },
            openNotification: false
        };

        this.recaptchaRef = React.createRef();

        this._closeDialog = this._closeDialog.bind(this);
        this._showDialog = this._showDialog.bind(this);
        this._onJoinButtonClick = this._onJoinButtonClick.bind(this);
        this._onToggleButtonClick = this._onToggleButtonClick.bind(this);
        this._onDropdownClose = this._onDropdownClose.bind(this);
        this._onOptionsClick = this._onOptionsClick.bind(this);
        this._setName = this._setName.bind(this);
        this._setEmail = this._setEmail.bind(this);
        this._onHandleToken = this._onHandleToken.bind(this);
        this.onMeetingTimer = this.onMeetingTimer.bind(this);
        this.startConference = this.startConference.bind(this);
        this.onHandleClose = this.onHandleClose.bind(this);
        this.handleLoaded = this.handleLoaded.bind(this);
        this._onOpenNavbar = this._onOpenNavbar.bind(this);
    }


    // eslint-disable-next-line require-jsdoc
    // componentDidUpdate(prevProps) {
    //     if (prevProps.jwt !== this.props.jwt) {

    //         setTimeout(() => {
    //             this.setState({ showError: false });
    //             this.props.joinConference();
    //         }, 2000);
    //     }
    // }

    /**
     * Removes the classname used for custom styling of the welcome page.
     *
     * @inheritdoc
     * @returns {void}
     */
    componentDidMount() {
        // console.log('Removing classname', process.env.API_URL_PREFIX, process.env.API_VERSION_PREFIX);

        if (this.props.jwt) {
            // eslint-disable-next-line react/no-did-mount-set-state
            this.setState({
                jwtConnect: true
            });

            // this.props.connect();
        }

        this.interval = setInterval(async () => {
            const {
                _roomName
            } = this.props;

            const moement = moment().seconds(0)
            .milliseconds(0)
            .format('X');

            const mesh256 = sha256(`${apiKEY}:MH:${moement}`);
            const roomName = {
                client_id: config.MH_CLIENT_ID,
                credentials: mesh256,
                domain: mtDomain,
                // eslint-disable-next-line camelcase
                meeting_id: _roomName
            };

            const userAccessToken = jitsiLocalStorage.getItem('accessToken') ?? this.props.accessToken;

            const headers = _.isNil(userAccessToken) ? {
                'Content-Type': 'application/json'
            } : {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${userAccessToken}`
            };

            const participantCountObject = await fetchApi('meeting/totalparticipants', 'post', headers, JSON.stringify(roomName));
            // eslint-disable-next-line camelcase
            const participantCount = participantCountObject?.data?.total_participant;
            const { allow_to_join = false, subscription_active = false } = participantCountObject?.data ?? {};

            this.setState(prevState => {
                return {
                    ...prevState,
                    participantCount,
                    allow_to_join,
                    subscription_active
                };
            });
        }, 60000);


    }


    /**
     * Removes the classname used for custom styling of the welcome page.
     *
     * @inheritdoc
     * @returns {void}
     */
    componentDidUpdate(_prevProps, prevState) {
        if (prevState.participantCount !== this.state.participantCount) {
            // eslint-disable-next-line camelcase
            if (this.state.participantCount <= this.state.maxparticipants) {
                if (this.state.ConferenceTimer) {
                    this.startConference();
                }
            }
        }
    }

    /**
     * Removes the classname used for custom styling of the welcome page.
     *
     * @inheritdoc
     * @returns {void}
     */
    async UNSAFE_componentWillMount() {
        document.body.style.overflowY = 'hidden';
        if (document) {
            const script = document.createElement('script');

            script.src = '/static/assets/js/main.js';
            script.id = 'mainJS';
            script.async = true;
            document.body.appendChild(script);

            const script1 = document.createElement('script');

            script1.src = 'https://www.google.com/recaptcha/api.js?render=6LfjoE8dAAAAANPXjL8duOhU9bYg6ioazSHXs32q';
            script1.id = 'googleRecaptcha';
            script1.addEventListener('load', this.handleLoaded);
            document.body.appendChild(script1);
        }

        const {
            _roomName,
            accessToken
        } = this.props;

        this.props.addLoader(true);
        const moement = moment().seconds(0)
        .milliseconds(0)
        .format('X');

        const mesh256 = sha256(`${apiKEY}:MH:${moement}`);
        const roomName = {
            client_id: config.MH_CLIENT_ID,
            credentials: mesh256,
            domain: mtDomain,
            // eslint-disable-next-line camelcase
            meeting_id: _roomName
        };

        const userAccessToken = jitsiLocalStorage.getItem('accessToken') ?? accessToken;
        const headers = _.isNil(userAccessToken) ? {
            'Content-Type': 'application/json'
        } : {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${userAccessToken}`
        };


        const participantCountObject = await fetchApi('meeting/totalparticipants', 'post', headers, JSON.stringify(roomName));
        // eslint-disable-next-line camelcase
        const participantCount = participantCountObject?.data?.total_participant;
        const { allow_to_join = false, allow_meeting_limit = 0, total_running_meeting = 0, subscription_active = false } = participantCountObject?.data ?? {};

        if (_roomName) {
            const ele = document.getElementById('preloader') ?? document.getElementById('preloader1');

            if (ele) {

                setTimeout(() => {
                    ele.style.opacity = '0';
                    document.body.style.overflowY = 'scroll';
                    setTimeout(() => {
                        ele.style.display = 'none';
                    }, 500);
                }, 500);
            }
        }

        this.setState(prevState => {
            return {
                ...prevState,
                participantCount,
                allow_to_join,
                allow_meeting_limit,
                total_running_meeting,
                subscription_active
            };
        });

        this.props.addLoader(false);


    }


    // eslint-disable-next-line no-shadow, no-unused-vars
    handleLoaded = _ => {
        // eslint-disable-next-line no-shadow, no-unused-vars
        window.grecaptcha.ready(_ => {
            window.grecaptcha
            .execute('6LfjoE8dAAAAANPXjL8duOhU9bYg6ioazSHXs32q', { action: 'homepage' })
            .then(token => {
                // eslint-disable-next-line no-invalid-this
                this._onHandleToken(token);
            });
        });
    };

    /**
     * Removes the classname used for custom styling of the welcome page.
     *
     * @inheritdoc
     * @returns {void}
     */
    componentWillUnmount() {
        if (document) {
            // document.querySelector('#prejoinId').remove();
            if (document.body) {
                document.body.classList.remove('welcome-page');
                const googleCaptcha = document.getElementById('googleRecaptcha');
                const googleRec = document.querySelector('.grecaptcha-badge');

                if (googleCaptcha) {
                    googleCaptcha.remove();
                }
                if (googleRec) {
                    googleRec.remove();
                }
            }

        }

        clearInterval(this.interval);

    }

    _onJoinButtonClick: (audiol: boolean) => void;

    /**
     * Handler for the join button.
     *
     * @param {boolean} audiol - The synthetic event.
     * @returns {void}
     */
    async _onJoinButtonClick(audiol: boolean = false) {
        if (isNil(this.state.participantCount)) {
            return;
        }
        if (this.props.showErrorOnJoin) {

            this.setState({
                showError: true
            });

            return;
        }
        // eslint-disable-next-line no-useless-escape
        const regex = /^(([^<>()[\]\.,;:\s@\"]+(\.[^<>()[\]\.,;:\s@\"]+)*)|(\".+\"))@(([^<>()[\]\.,;:\s@\"]+\.)+[^<>()[\]\.,;:\s@\"]{2,})$/i;

        if (this.props.email && !this.props.email.match(regex) && !this.props.disableEmail) {
            this.setState({
                showError: true,
                showMessage: 'Invalid Email'
            });

            return;
        }

        if (this.props.displayName.length < 3 && (!this.props?._userDetails || !this.props?.jwt || !this.props?._jwtStored)) {
            this.setState({
                showError: true,
                showMessage: this.props.t('prejoin.errorNameLength')
            });

            return;
        }

        if (!this.state?.allow_to_join || this.state?.total_running_meeting > this.state?.allow_meeting_limit) {
            const notification = {
                description: !this.state?.allow_to_join && !this.state.subscription_active ? this.props.t('prejoin.subScriptionInactiveErr')
                    : this.props.t('prejoin.parallelMeetingsLicencesErr'),
                alertType: 'warning'
            };

            this.setState({
                meetingNotifier: notification,
                openNotification: !this.state.openNotification
            });

            return;
        }

        this.props.addLoader(true);
        const {
            preRegData,
            displayName,
            email,
            _meetingDetails,
            _roomName,
            _jwtStored,
            jwt,
            t
        } = this.props;


        if (!jwt && !_jwtStored) {

            const bowser = new BrowserDetection();
            const browserName = bowser.getName();
            const accessType = 'web';

            // for (const i in bowserNameToJitsiName) {
            //     console.log('dfdfdfdf')
            //     if (i !== browserName) {
            //         accessType = 'app';
            //     }
            // }
            let dataJson;

            try {
                const ClientIP = await fetchApi('https://api.ipify.org/?format=json');

                // eslint-disable-next-line no-negated-condition
                if (typeof ClientIP !== 'undefined') {

                    // eslint-disable-next-line no-useless-concat
                    const getData = await fetchApi('geolocation' + `?api_key=${apiKEY}&ip_address=${ClientIP?.ip}`, 'get', {
                        'Content-Type': 'application/json'
                    });

                    // eslint-disable-next-line max-depth
                    if (getData) {
                        dataJson = await getData;
                    }
                }
            } catch (error) {
                logger.error('Error in fetching geolocation bros', error);

            }

            const userName = displayName;
            const userEmail = email;
            const moement = moment().seconds(0)
            .milliseconds(0)
            .format('X');

            const mesh256 = sha256(`${apiKEY}:MH:${moement}`);
            const body = {
                client_id: config.MH_CLIENT_ID,
                credentials: mesh256,
                domain: mtDomain,
                meeting_id: _roomName,
                name: userName,
                email_id: userEmail,
                user_agent: browserName,
                access_type: accessType,
                ip: dataJson?.IPv4,
                geolocation: dataJson

            };

            const prejoinResponse = await fetchApi(PrejoinApi, 'post', {
                'Content-Type': 'application/json'
            }, JSON.stringify(body)).catch(e => logger.error('Error in fetching prejoin api For guest', e));

            const bodyJSON = await prejoinResponse?.data;


            const isTrueGuest: boolean = Boolean(preRegData?.allow_guest === 1); // || !this.state.verifyGoogle

            if (!isTrueGuest) {
                this.props.showErrorNotification({ descriptionKey: t('prejoin.guestNotAllowedMsg'),
                    titleKey: 'Unauthenticated' });

                return;
            }

            this.props.prejoinApi(bodyJSON);


            if (bodyJSON?.mt_token) {

                const tokenGuest = jwtDecode(bodyJSON?.mt_token);
                const startDate = moment(new Date())?.tz(tokenGuest.meeting?.timezone)
?.locale('en-gb')
?.utc();
                const givenDate = moment(tokenGuest.meeting?.start_time)?.locale('en-gb')
?.utc();
                const isAfter = startDate?.isAfter(moment(givenDate));

                const joinAnyTimeGuest = tokenGuest && tokenGuest.meeting && tokenGuest.meeting.settings.JOIN_ANYTIME ? tokenGuest.meeting.settings.JOIN_ANYTIME : null;
                const meetingDetails = tokenGuest.meeting;

                this.setState({
                    maxparticipants: meetingDetails?.max_participants
                });

                if (this.state.participantCount >= meetingDetails?.max_participants) {
                    const notification = {
                        description: this.props.t('prejoin.maximumAllowedParticipantsErr'),
                        alertType: 'warning'
                    };

                    this.setState(prevState => {
                        return {
                            ...prevState,
                            meetingNotifier: notification,
                            openNotification: !prevState.openNotification,
                            ConferenceTimer: true
                        };
                    });

                    return;
                }

                if (tokenGuest && ((joinAnyTimeGuest !== 1) && !isAfter)) {
                    const time = moment(givenDate)?.locale('en-gb')
                    .local()
                    ?.format('DD-MM-YYYY hh:mm a');

                    this.onMeetingTimer(t('prejoin.meetingReminder', { time }));

                    // console.log('jwt stored', !tokenGuest.meeting?.settings.JOIN_ANYTIME,
                    // tokenGuest.meeting.settings.JOIN_ANYTIME === 1, startDate);

                    // this.props.showErrorNotification({ descriptionKey: 'Meeting hasnt started yet.',
                    //     titleKey: 'Meeting hasn\'t started yet.' });
                    const notification = {
                        description: this.state.textFlow,
                        alertType: 'warning'
                    };

                    this.setState(prevState => {
                        return {
                            ...prevState,
                            meetingNotifier: notification,
                            openNotification: !prevState.openNotification
                        };
                    });
                    this.props.addLoader(false);


                    return;
                }

                // eslint-disable-next-line radix
                if (!(parseInt(this.state.participantCount) >= 1)) {
                    // eslint-disable-next-line max-len
                    this.onMeetingTimer(this.props.t('prejoin.waitForModeratorMsgDynamic', { Moderator: interfaceConfig?.CHANGE_MODERATOR_NAME ? interfaceConfig?.CHANGE_MODERATOR_NAME : 'Moderator' }));

                    // console.log('jwt stored', !tokenGuest.meeting?.settings.JOIN_ANYTIME,
                    // tokenGuest.meeting.settings.JOIN_ANYTIME === 1, startDate);

                    // this.props.showErrorNotification({ descriptionKey: 'Meeting hasnt started yet.',
                    //     titleKey: 'Meeting hasn\'t started yet.' });
                    const notification = {
                        description: this.state.textFlow,
                        alertType: 'warning'
                    };

                    this.setState(prevState => {
                        return {
                            ...prevState,
                            meetingNotifier: notification,
                            openNotification: !prevState.openNotification
                        };
                    });
                    this.props.addLoader(false);

                    this.props.setPrejoinPageVisibility(false);

                    this.props.openLobbyScreen(true);


                    return;
                }


                // this.props.fetchJwtStorage(bodyJSON?.mt_token);
                // if(!this.props._videoMuted){
                //     this.props._appSetMuted(true)
                //     this.props._setVideoMuted(!this.props._videoMuted)
                // }

                // this.props.connect();
                // interfaceConfig?.applyMeetingSettings === true && this.props.connect();

                this.setState({ showError: false });
                audiol ? this.props.joinConference({ startSilent: true }) : this.props.joinConference();

            }

            this.setState(prevState => {
                return {
                    ...prevState,
                    meetButton: !prevState.meetButton
                };
            });

            // this.setState({ isLoading: false });
            // this.props.addLoader(false);

            return;
        }

        this.setState({
            maxparticipants: _meetingDetails?.max_participants
        });
        if (this.state.participantCount >= _meetingDetails.max_participants) {
            const notification = {
                description: this.props.t('prejoin.maximumAllowedParticipantsErr'),
                alertType: 'warning'
            };

            this.setState(prevState => {
                return {
                    ...prevState,
                    meetingNotifier: notification,
                    openNotification: !prevState.openNotification,
                    ConferenceTimer: true
                };
            });

            return;
        }

        if (this.props._jwtStored && _.isNil(this.props.jwt)
        ) {
            const decoded = jwtDecode(this.props?._jwtStored);
            const startDate = moment(new Date())?.tz(decoded?.meeting?.timezone)
?.locale('en-gb')
?.utc();
            const givenDate = moment(decoded.meeting?.start_time)?.locale('en-gb')
?.utc();
            const isAfter = startDate?.isAfter(moment(givenDate));
            const joinAnyTime = this.props.confSettings && this.props.confSettings?.JOIN_ANYTIME ? this.props.confSettings?.JOIN_ANYTIME : null;

            if (joinAnyTime !== 1 && !isAfter) {
                const time = moment(givenDate)?.locale('en-gb')
?.local()
?.format('DD-MM-YYYY hh:mm a');

                this.onMeetingTimer(this.props.t('prejoin.meetingReminder', { time }));

                // console.log('jwt_stored1234', joinAnyTime, startDate, _.isNil(this.props.jwt), this.props._jwtStored);

                // console.log('jwt stored', jwtDecode(_jwtStored));
                // this.props.showErrorNotification({ descriptionKey: 'Meeting hasnt started yet.',
                //     titleKey: 'Meeting hasnt started yet.' });
                const notification = {
                    description: this.props.t('prejoin.meetingReminder', { time }),
                    alertType: 'warning'
                };

                this.setState(prevState => {
                    return {
                        ...prevState,
                        meetingNotifier: notification,
                        openNotification: !prevState.openNotification,
                        isLoading: false
                    };
                });
                this.props.addLoader(false);

                return;
            }
        }

        // eslint-disable-next-line radix
        if (!(parseInt(this.state.participantCount) >= 1) && isNil(jwt)) {
            this.onMeetingTimer(t('prejoin.waitForModeratorMsg'));

            // console.log('jwt stored', !tokenGuest.meeting?.settings.JOIN_ANYTIME,
            // tokenGuest.meeting.settings.JOIN_ANYTIME === 1, startDate);

            // this.props.showErrorNotification({ descriptionKey: 'Meeting hasnt started yet.',
            //     titleKey: 'Meeting hasn\'t started yet.' });
            const notification = {
                description: this.state.textFlow,
                alertType: 'warning'
            };

            this.setState(prevState => {
                return {
                    ...prevState,
                    meetingNotifier: notification,
                    openNotification: !prevState.openNotification
                };
            });
            this.props.addLoader(false);


            this.props.setPrejoinPageVisibility(false);

            setTimeout(() => this.props.openLobbyScreen(true), 1000);


            return;
        }

        // console.log('jwt stored', jwtDecode(_jwtStored));
        // document.getElementById('videospace').style.display = 'none';
        // const styleB = document.getElementById('new-toolbox');

        // if (styleB) {
        //     styleB.style.display = 'none';
        // }
        // document.getElementById('sideToolbarContainer').style.display = 'none';

        // if(!this.props._videoMuted){
        //     this.props._appSetMuted(true)
        //     this.props._setVideoMuted(!this.props._videoMuted)
        // }
        if (this.props.jwt && !this.state.jwtConnect) {
            // this.props.connect();
            // interfaceConfig?.applyMeetingSettings === true && this.props.connect();

        }
        this.setState({ showError: false });
        audiol ? this.props.joinConference({ startSilent: audiol }) : this.props.joinConference();
    }

    /**
     * Starts the Conference.
     *
     * @returns {void}
     */
    startConference() {
        if (this.props.jwt && !this.state.jwtConnect) {
            //   interfaceConfig?.applyMeetingSettings === true && this.props.connect();
        }
        this.setState({ showError: false });
        this.props.joinConference();

        // this.setState({ isLoading: false });
        this.props.addLoader(false);

        // setTimeout(() => {

        // }, 2000);
    }

    _onToggleButtonClick: () => void;

    /**
     * Handler for the toggle button.
     *
     * @param {Object} e - The synthetic event.
     * @returns {void}
     */
    _onToggleButtonClick() {
        this.props.setSkipPrejoin(!this.props.buttonIsToggled);
    }

    _onDropdownClose: () => void;

    /**
     * Closes the dropdown.
     *
     * @returns {void}
     */
    _onDropdownClose() {
        this.setState({
            showJoinByPhoneButtons: false
        });
    }

    /**
     * Closes the notification alert.
     *
     * @returns {void}
     */
    onHandleClose() {
        this.setState(prevState => {
            return {
                ...prevState,
                openNotification: !prevState.openNotification
            };
        });
    }

    _onOptionsClick: () => void;

    /**
     * Displays the join by phone buttons dropdown.
     *
     * @param {Object} e - The synthetic event.
     * @returns {void}
     */
    _onOptionsClick(e) {
        e.stopPropagation();

        this.setState({
            showJoinByPhoneButtons: !this.state.showJoinByPhoneButtons
        });
    }

    _setName: () => void;

    /**
     * Sets the guest participant name.
     *
     * @param {string} displayName - Participant name.
     * @returns {void}
     */
    _setName(displayName) {
        this.props.updateSettings({
            displayName
        });
    }

    _setEmail: () => void;

    /**
     * Sets the guest participant name.
     *
     * @param {string} email - Participant name.
     * @returns {void}
     */
    _setEmail(email) {
        this.props.updateSettings({
            email
        });
    }

    onMeetingTimer: string => void;

    /**
     * Adds a new text in the center.
     *
     * @param {string} textFlow - Text addded to the box.
     * @private
     * @returns {void}
     */
    onMeetingTimer(textFlow) {

        this.setState(prevState => {
            return {
                ...prevState,
                textFlow
            };
        });
    }

    _onHandleToken: () => void;

    /**
     * Handle token.
     *
     * @param {string} token - Participant name.
     * @returns {void}
     */
    async _onHandleToken(token) {
        this.setState({
            token
        });

        const moement = moment().seconds(0)
        .milliseconds(0)
        .format('X');

        const mesh256 = sha256(`${apiKEY}:MH:${moement}`);
        const body = {
            client_id: config.MH_CLIENT_ID,
            credentials: mesh256,
            domain: mtDomain,
            // eslint-disable-next-line camelcase
            'g_recaptcha_response': token
        };

        const userApi = await fetchApi('google_siteverify',
        'post', {
            'Content-Type': 'application/json'
        }, JSON.stringify(body));
        const user = await userApi;

        this.setState(prevState => {
            return {
                ...prevState,
                verifyGoogle: user?.success
            };
        }
        );


    }
    _closeDialog: () => void;

    /**
     * Closes the join by phone dialog.
     *
     * @returns {undefined}
     */
    _closeDialog() {
        this.props.setJoinByPhoneDialogVisiblity(false);
    }

    _showDialog: () => void;

    /**
     * Displays the dialog for joining a meeting by phone.
     *
     * @returns {undefined}
     */
    _showDialog() {
        this.props.setJoinByPhoneDialogVisiblity(true);
        this._onDropdownClose();
    }

    _onOpenNavbar: () => void;

    /**
     * Replaces the windows location to the depending parameter.
     *
     * @inheritdoc
     * @param {string} param - The name of the class.
     * @returns {void}
     */
    _onOpenNavbar() {
        const menuNav = document.querySelector('.mobile-nav-overly');
        const menuNav1 = document.querySelector('.mobile-nav');
        const meen = document.querySelector('.mobile-nav-toggle i');

        if (meen.classList !== null && meen.classList.contains('icofont-close')) {
            menuNav.style.display = 'none';
            document.body.classList.remove('mobile-nav-active');
            meen.classList.remove('icofont-close');
            meen.classList.add('icofont-navigation-menu');

            return;
        }
        if (menuNav) {
            menuNav.style.display = 'block';
        }
        if (menuNav1) {
            document.body.classList.add('mobile-nav-active');
        }
        if (meen) {
            meen.classList.remove('icofont-navigation-menu');
            meen.classList.add('icofont-close');
        }
    }

    _onLogOutClicked = e => {
        e.preventDefault();
        const ele = document.getElementById('preloader') ?? document.getElementById('preloader1');

        if (ele) {
            ele.style.opacity = '100%';
            ele.style.display = 'block';
        }
        jitsiLocalStorage.clear();

        window.location.reload();
    };

    onCode = (code, params) => {
        if (params.get('access_token')) {
            // eslint-disable-next-line no-invalid-this
            this.props.setAccessToken(code);

            return;
        }
        // eslint-disable-next-line no-invalid-this
        this.props.fetchJwtStorage(code);

        // eslint-disable-next-line no-invalid-this
        this.setState({
            setPopUp: true
        });
    };

    onClose = () => logger.log('closed!');

    /**
     * Implements React's {@link Component#render()}.
     *
     * @inheritdoc
     * @returns {ReactElement}
     */
    render() {
        const {
            hasJoinByPhoneButton,
            isLoader,
            joinConferenceWithoutAudio,
            _invalidData,
            name,
            jwt,
            _isLobbyScreenVisible,
            email,
            showAvatar,
            showCameraPreview,
            showDialog,
            showConferenceInfo,
            showJoinActions,
            t,
            videoTrack,
            _userDetails,
            conference,
            _jwtStored,
            _roomName,
            accessToken
        } = this.props;
        // eslint-disable-next-line no-useless-escape
        const regex = /^(([^<>()[\]\.,;:\s@\"]+(\.[^<>()[\]\.,;:\s@\"]+)*)|(\".+\"))@(([^<>()[\]\.,;:\s@\"]+\.)+[^<>()[\]\.,;:\s@\"]{2,})$/i;

        const { _closeDialog, _onDropdownClose, _onJoinButtonClick,
            _onOptionsClick, _setName, _showDialog, _setEmail } = this;

        const { showJoinByPhoneButtons, showError, participantCount } = this.state;
        let userDComponenet;

        const userAccessToken = jitsiLocalStorage.getItem('accessToken');

        const loginButtonJSx = (
            <>
                <li className = 'signin-li'><a
                    href = { `${config.URL_PREFIX}serviceLogin?client_id=${config.MH_CLIENT_ID}
                &redirect_uri=${window.location.href}%23interfaceConfig.ENABLE_DESKTOP_DEEPLINK%3Dfalse&device_type=web
                &response_type=get${_roomName ? `&meeting_id=${_roomName}` : ''}` }>{t('prejoin.signinsignup')}</a></li>
            </>
        );

        // const showAdditionalToolbarContent = this._shouldShowAdditionalToolbarContent();
        // const showResponsiveText = this._shouldShowResponsiveText();

        let logoutDocument;

        if (_userDetails) {
            const userd = _userDetails;

            logoutDocument = (<li
                className = 'drop-down'
                style = { styleWeb.marginLeft }>
                {userd && userd?.picture
                    ? <img
                        className = 'avatar'
                        src = { userd?.picture }
                        style = { styleWeb.widthandHeightimg } />
                    : <Avatar
                        className = 'premeeting-screen-avatar'
                        displayName = { name }
                        dynamicColor = { true }
                        participantId = 'local'
                        size = { 45 } /> }

                <a href = ''>{userd ? userd?.name : ''}</a>
                <ul className = 'drop-down1'>
                    <li><a
                        href = { `${config.URL_PREFIX}serviceLogin?client_id=${config.MH_CLIENT_ID}
                    &redirect_uri=${config.URL_PREFIX}customer/dashboard&device_type=web${accessToken ?? userAccessToken
                    ? `&access_token=${accessToken ?? userAccessToken}` : ''}` }><i className = 'bx bxs-dashboard' /> {t('prejoin.dashboard')}</a></li>
                    <li><a
                        href = { `${config.URL_PREFIX}serviceLogin?client_id=${config.MH_CLIENT_ID}
                    &redirect_uri=${config.URL_PREFIX}customer/profile&device_type=web${accessToken ?? userAccessToken
                    ? `&access_token=${accessToken ?? userAccessToken}` : ''}` }><i className = 'bx bx-user' />{t('prejoin.profile')}</a> </li>
                    <li><a
                        onClick = { this._onLogOutClicked }
                        style = { styleWeb.cusrorPointer }><i className = 'bx bx-log-out' />{t('prehoin.logout')}</a></li>
                </ul>
            </li>);

        } else {
            logoutDocument = loginButtonJSx;
        }


        const isLOader = (

<>
    <img
        className = 'img-fluid'
        src = 'images/loading2.gif'
        style = { stylePrejoin.loadingImg } />

    <h4
        style = { stylePrejoin.h4Style }>
        {
            this.state.ConferenceTimer ? t('prejoin.oppsMaximumAllowedParticipantsErr') : t('presenceStatus.connecting')
        }


    </h4>


        </>);

        // if (isLoading) {
        //     return (<PreMeetingScreen
        //         guestAllowed = { true }
        //         isLoadingMessage = { t('presenceStatus.connecting') } />);
        // }

        if ((_userDetails || jwt || _jwtStored) && _invalidData?.code !== 401) {
            userDComponenet = (
                <div style = { stylePrejoin.margin20 }>

                    <h3
                        style = { stylePrejoin.readyjoin }>{t('prejoin.readyToJoin')}</h3>

                    <span
                        style = { stylePrejoin.readySpan1 }>
                        <Icon
                            src = { IconParticipants }
                            style = { stylePrejoin.iconStyle } />

                        <span
                            style = { stylePrejoin.readyIconSpan }>
                            {participantCount} {t('prejoin.peopleInTheCall')}</span></span>

                    <div
                        style = { stylePrejoin.readyForm }>

                        <h4
                            style = { stylePrejoin.readyh4 }>{_userDetails?.name || name}</h4>
                        <strong
                            style = { stylePrejoin.readyStrong }>{_userDetails?.email || email}</strong>

                        <br />
                        <br />
                    </div>

                </div>
            );
        }

        const inputComponent = (
          <>
            <h3 style = { stylePrejoin.readyjoin }>{t('prejoin.readyToJoin')}</h3>
            <span style = { stylePrejoin.readySpan1 }>
              <Icon
src = { IconParticipants }
style = { stylePrejoin.iconStyle } />

              <span style = { stylePrejoin.readyIconSpan }>
                {participantCount ? participantCount : 0} {t('prejoin.peopleInTheCall')}
              </span>
            </span>
            <InputField
              autoFocus = { true }
              className = {
                showError ? 'prejoin-input-field error' : 'prejoin-input-field'
              }
              hasError = { showError }
              onChange = { _setName }
              onSubmit = { _onJoinButtonClick }
              placeHolder = { t('dialog.enterDisplayName') }
              value = { name } />
            {!this.props.disableEmail && (
              <InputField
                autoFocus = { false }
                className = {
                  showError
                    ? 'prejoin-input-field error'
                    : 'prejoin-input-field'
                }
                hasError = { showError }
                onChange = { _setEmail }
                onSubmit = { _onJoinButtonClick }
                placeHolder = { t('dialog.enterDisplayEmail') }
                value = { email } />
            )}
            {showError
              && (!name ? (
                <div
                  className = 'prejoin-error'
                  data-testid = 'prejoin.errorMessage'>
                  {t('prejoin.errorMissingName')}
                </div>
              )
              : name.length < 3 ? (
                <div
                  className = 'prejoin-error'
                  data-testid = 'prejoin.errorMessage'>
                  {t('prejoin.errorNameLength')}
                </div>
              ) : !this.props.disableEmail
                ? !email ? (
                  <div
                    className = 'prejoin-error'
                    data-testid = 'prejoin.errorMessage'>
                    {t('prejoin.errorMissingEmail')}
                  </div>
                ) : email.match(regex) ? null : this.state.showMessage ? (
                  <div
                    className = 'prejoin-error'
                    data-testid = 'prejoin.errorMessage'>
                    {this.state.showMessage}
                  </div>
                ) : null
               : this.state.showMessage ? (
                <div
                  className = 'prejoin-error'
                  data-testid = 'prejoin.errorMessage'>
                  {this.state.showMessage}
                </div>
              ) : null)}
            {/* <ReCAPTCHA
                    onChange = { this._onHandleToken }
                    ref = { this.recaptchaRef }
                    sitekey = '6LcbxF0dAAAAAOc1sa6o3M94J33MEgaitzIhvshU'
                    size = 'normal' /> */}
            <div
              className = 'g-recaptcha'
              data-sitekey = '6LfjoE8dAAAAANPXjL8duOhU9bYg6ioazSHXs32q'
              data-size = 'invisible' />
          </>
        );


        return (
            <>
                { interfaceConfig?.applyMeetingSettings !== true && <MediaQuery maxWidth = { 1000 }>
                {
                    config?.MEGAMENU_HEADER !== true ? <button
                    className = 'mobile-nav-toggle d-lg-none'
                    onClick = { this._onOpenNavbar }
                    type = 'button'><i className = 'icofont-navigation-menu' /></button> : ''
                }
                </MediaQuery>}
                {!_isLobbyScreenVisible && <div
                    id = 'preloader'
                    style = {
                        // eslint-disable-next-line react-native/no-color-literals, react-native/no-inline-styles
                        { backgroundColor: interfaceConfig?.applyMeetingSettings === true
                            ? interfaceConfig.DEFAULT_BACKGROUND : '#405170' } } />
                }
                <div
                    className = 'prejoin'
                    ref = { PrejoinRef }>
                    <PreMeetingScreen
                        conference = { conference }
                        footer = { this._renderFooter() }
                        name = { name }
                        newText = { this.state.textFlow }
                        showAvatar = { showAvatar }
                        showConferenceInfo = { showConferenceInfo }
                        skipPrejoinButton = { this._renderSkipPrejoinButton() }
                        title = { t('prejoin.joinMeeting') }
                        videoMuted = { !showCameraPreview }
                        videoTrack = { videoTrack }
                        virtualBackgroundDialog = { this._renderVBButton() } >
                        {isLoader && isLOader}
                        {
                            _invalidData?.code === 401 && !isLoader
                            && (
                                <>
                                    <Icon
                                        className = 'img-fluid'
                                        src = { IconNotAllowed }
                                        // eslint-disable-next-line react-native/no-inline-styles
                                        style = {{
                                            width: '141px',
                                            height: '141px',
                                            fill: '#d51616' }} />
                                    <h4
                                        style = { stylePrejoin.h4Style }>
                               {t('presenceStatus.invalidToken')}
                                    </h4>
                            </>
                            )
                        }
                        {showJoinActions && !isLoader && _invalidData?.code !== 401 && (
                            <div className = 'prejoin-input-area-container'>
                                <div className = 'prejoin-input-area'>
                                    {_userDetails || jwt
                                        ? userDComponenet
                                        : inputComponent
                                    }
                                    { interfaceConfig?.applyMeetingSettings === true && interfaceConfig.showJoinAsModerator === true && <GetUserDetails />}
                                    <div className = 'prejoin-preview-dropdown-container'>
                                        <InlineDialog
                                            content = { <div className = 'prejoin-preview-dropdown-btns bg-dark'>
                                                <div
                                                    className = 'prejoin-preview-dropdown-btn'
                                                    data-testid = 'prejoin.joinWithoutAudio'
                                                    // eslint-disable-next-line react/jsx-no-bind
                                                    onClick = { () => _onJoinButtonClick(true) }>
                                                    <Icon
                                                        className = 'prejoin-preview-dropdown-icon'
                                                        size = { 24 }
                                                        src = { IconVolumeOff } />
                                                    { t('prejoin.joinWithoutAudio') }
                                                </div>
                                                {hasJoinByPhoneButton && <div
                                                    className = 'prejoin-preview-dropdown-btn'
                                                    onClick = { _showDialog }>
                                                    <Icon
                                                        className = 'prejoin-preview-dropdown-icon'
                                                        data-testid = 'prejoin.joinByPhone'
                                                        size = { 24 }
                                                        src = { IconPhone } />
                                                    { t('prejoin.joinAudioByPhone') }
                                                </div>}
                                            </div> }
                                            isOpen = { showJoinByPhoneButtons }
                                            onClose = { _onDropdownClose }>
                                            <ActionButton
                                                OptionsIcon = { showJoinByPhoneButtons ? IconArrowUp : IconArrowDown }
                                                hasOptions = { true }
                                                // eslint-disable-next-line react/jsx-no-bind
                                                onClick = { () => _onJoinButtonClick(false) }
                                                onOptionsClick = { _onOptionsClick }
                                                testId = 'prejoin.joinMeeting'
                                                type = 'meeth'>
                                                { _userDetails || jwt ? t('prejoin.joinMeeting') : t('prejoin.joinMeetingGuest') }
                                            </ActionButton>
                                        </InlineDialog>
                                        {

                                            // eslint-disable-next-line no-negated-condition, max-len
                                            interfaceConfig?.applyMeetingSettings === true && interfaceConfig.showJoinAsModerator === true && (!(jwt || _userDetails || this.state.setPopUp) ? <OauthPopup
                                                // eslint-disable-next-line react/jsx-curly-spacing, max-len
                                                url = {`${config.URL_PREFIX}serviceLogin?client_id=${config.MH_CLIENT_ID}&redirect_uri=${window.location.origin}/static/oauth.html&device_type=web&response_type=get${_roomName ? `&meeting_id=${_roomName}&sc=1` : ''}`}
                                                // eslint-disable-next-line react/jsx-sort-props, react/jsx-handler-names
                                                onCode = { this.onCode }
                                                // eslint-disable-next-line react/jsx-sort-props, react/jsx-handler-names
                                                onClose = { this.onClose } >
                                                {t('presenceStatus.signInAsHost')}
                                            </OauthPopup>
                                                : <ActionButton
                                                // eslint-disable-next-line react/jsx-no-bind
                                                    onClick = { e => this._onLogOutClicked(e) }
                                                    testId = 'prejoin.joinMeeting'
                                                    type = 'meeth'>
                                                    {t('prejoin.logout')}
                                                </ActionButton>)
                                        }

                                    </div>
                                </div>
                            </div>
                        )}
                        { showDialog && (
                            <JoinByPhoneDialog
                                joinConferenceWithoutAudio = { joinConferenceWithoutAudio }
                                onClose = { _closeDialog } />
                        )}

                    </PreMeetingScreen>
                    <MaterialNotifier
                        handleClose = { this.onHandleClose }
                        notification = { this.state.meetingNotifier }
                        openNotifier = { this.state.openNotification } />
                         <><nav
                             className = 'mobile-nav d-lg-none'
                             // eslint-disable-next-line react-native/no-inline-styles
                             style = {{ width: '100%' }} >
                             <ul className = 'float-left-desktop'>

                                 <li><a
                                     href = '#features'
                                     style = { styleWeb.footerColor2 }>{t('welcomepage.header.features')}</a></li>
                                 <li className = 'drop-down'><a
                                     href = ''
                                     style = { styleWeb.footerColor2 }>{t('welcomepage.header.solutions')}</a>
                                 <ul className = 'drop-down1'>
                                     <li><a
                                         href = '/explore/hospitals.html'
                                         style = { styleWeb.footerColor2 }>{t('welcomepage.forHospitals')}</a>
                                     </li>
                                 </ul>
                                 </li>
                                 <li className = 'drop-down'><a href = '#pricing'>{t('welcomepage.header.pricing')}</a>
                                     <ul className = 'drop-down1'><li>
                                         <a
                                             href = 'https://meethour.io/#pricing'>{t('welcomepage.freePlan', { lng: 'en' })}</a>
                                         <a
                                             href = 'https://repo.meethour.io/landing/pro.html?location=meethour_homepage_pricing_menu'
                                             target = '_blank'>{t('welcomepage.proPlan', { lng: 'en' })}</a><a
                                                 href = 'https://repo.meethour.io/landing/developer.html?location=meethour_homepage_pricing_menu'
                                             target = '_blank'>{t('welcomepage.developerPlan', { lng: 'en' })}</a><a
                                                     href = 'https://repo.meethour.io/landing/enterprise.html?location=meethour_homepage_pricing_menu'
                                             target = '_blank'>{t('welcomepage.enterpriseSelfHostPlan', { lng: 'en' })}</a></li></ul></li>

                             </ul>
                             <ul className = 'float-right-desktop'>
                             <li
                                            style = { styleWeb.languageContainer }>
                                                <LanguagesDropdown />
                                        </li>
                                 <li
className = 'd-flex align-items-center'
style = { styleWeb.headerliRight }>
                                     <a
                                         href = '/joinmeeting'
                                         style = { styleWeb.footerColor2 }>
                                            {t('welcomepage.header.joinAMeeting')}</a></li>
                                 {logoutDocument}
                             </ul>
                         </nav>
                             <div
                                 className = 'mobile-nav-overly'
                                 // eslint-disable-next-line react-native/no-inline-styles
                                 style = {{ display: 'none' }} />
</>

                </div>
            </>
        );
    }

    /**
     * Renders the screen footer if any.
     *
     * @returns {React$Element}
     */
    _renderFooter() {
        return this.props.deviceStatusVisible && <DeviceStatus />;
    }

    /**
     * Renders the 'skip prejoin' button.
     *
     * @returns {React$Element}
     */
    _renderSkipPrejoinButton() {
        const { buttonIsToggled, t, showSkipPrejoin } = this.props;

        if (!showSkipPrejoin) {
            return null;
        }

        return (
            <div className = 'prejoin-checkbox-container'>
                <ToggleButton
                    isToggled = { buttonIsToggled }
                    onClick = { this._onToggleButtonClick }>
                    {t('prejoin.doNotShow')}
                </ToggleButton>
            </div>
        );
    }

    /**
     * Renders the 'skip prejoin' button.
     *
     * @returns {React$Element}
     */
    _renderVBButton() {

        const bowser = new BrowserDetection();
        const browserName = bowser.getName();

        if ((this.props._visibleButtons.has('select-background')
        || this.props._visibleButtons.has('videobackgroundblur')) && browserName !== SAFARI) {
            return (
                <div className = 'action-btn1' >
                    <VideoBackgroundButton
                        key = { 'select-background' }
                        showLabel = { true }
                        visible = { checkBlurSupport() } />
                </div>

            );
        }

        return null;
    }
}

/**
 * Maps (parts of) the redux state to the React {@code Component} props.
 *
 * @param {Object} state - The redux state.
 * @param {Object} ownProps - The props passed to the component.
 * @returns {Object}
 */
function mapStateToProps(state, ownProps): Object {
    const name = getDisplayName(state);
    const { email, displayName } = state['features/base/settings'];
    const disableEmail = disableEmailOnPrejoin(state);
    const showErrorOnJoin = (isDisplayNameRequired(state) && !name) || (!disableEmail && !email);
    const { showJoinActions } = ownProps;
    const isInviteButtonEnabled = isButtonEnabled('invite', state);
    const { userDetails } = state['features/base/mtApi'];
    const { conference, isLoading } = state['features/base/conference'];
    const { jwt, jwtData, mtTime, accessToken } = state['features/base/jwt'];
    const participants = state['features/base/participants'];
    const participant = participants.filter(i => i.id === 'local');
    const participantInCall = participants.filter(i => i.id !== 'local');
    const roomName = getBackendSafeRoomName(state['features/base/conference'].room);
    const { lobbyScreen: { _isLobbyScreenVisible } } = state['features/lobby'];

    // const email1 = email || participant[0].email;
    const { confSettings, meetingDetails } = state['features/base/jwt'];
    const { pregData, invalidData } = state['features/base/mtApi'];


    // Hide conference info when interfaceConfig is available and the invite button is disabled.
    // In all other cases we want to preserve the behaviour and control the the conference info
    // visibility trough showJoinActions.
    const showConferenceInfo
        = typeof isInviteButtonEnabled === 'undefined' || isInviteButtonEnabled === true
            ? showJoinActions
            : false;

    const buttons = new Set(interfaceConfig.TOOLBAR_BUTTONS);

    return {
        buttonIsToggled: isPrejoinSkipped(state),
        name,
        email,
        conference,
        confSettings,
        deviceStatusVisible: isDeviceStatusVisible(state),
        roomName: getRoomName(state),
        showDialog: isJoinByPhoneDialogVisible(state),
        showErrorOnJoin,
        hasJoinByPhoneButton: isJoinByPhoneButtonVisible(state),
        showCameraPreview: !isVideoMutedByUser(state),
        showConferenceInfo,
        videoTrack: getLocalMHVideoTrack(state),
        _visibleButtons: equals(visibleButtons, buttons) ? visibleButtons : buttons,
        _localParticipant: participant,
        _participantInCall: participantInCall,
        _userDetails: userDetails,
        jwt,
        _jwtStored: jwtData,
        displayName,
        disableEmail,
        _roomName: roomName,
        preRegData: pregData,
        _mtTime: mtTime,
        _meetingDetails: meetingDetails,
        isLoader: isLoading,
        _isLobbyScreenVisible,
        _invalidData: invalidData,
        accessToken,
        _videoMuted: isLocalCameraTrackMuted(state['features/base/tracks'])
    };
}

const mapDispatchToProps = {
    joinConferenceWithoutAudio: joinConferenceWithoutAudioAction,
    joinConference: joinConferenceAction,
    setJoinByPhoneDialogVisiblity: setJoinByPhoneDialogVisiblityAction,
    setSkipPrejoin: setSkipPrejoinAction,
    updateSettings,
    setJWT: setjWT,
    setLocation: setLocationURL,
    connect: mtConnection,
    showErrorNotification,
    prejoinApi,
    fetchJwtStorage: setJwtStorage,
    addLoader: isLoadingChecker,
    _setVideoMuted: setVideoMuted,
    _appSetMuted: appSetMuted,
    setAccessToken,
    openLobbyScreen,
    setPrejoinPageVisibility

};

export default connect(mapStateToProps, mapDispatchToProps)(translate(Prejoin));
