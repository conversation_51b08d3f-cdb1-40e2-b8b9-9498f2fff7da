// @flow

import { jitsiLocalStorage } from '@jitsi/js-utils';
import { Box } from '@material-ui/core';
import { createBrowserHistory } from 'history';
import _ from 'lodash';
import React from 'react';
import { ConfirmPageLeave } from 'react-leave-page-confirm';

import VideoLayout from '../../../../../modules/UI/videolayout/VideoLayout';
import { getConferenceNameForTitle, openNotification as openNotifier } from '../../../base/conference';
import { connect, disconnect } from '../../../base/connection';
import { toggleDialog } from '../../../base/dialog';
import Portal from '../../../base/dialog/components/web/Portal';
import UpperDialog from '../../../base/dialog/components/web/UpperDialog';
import { translate } from '../../../base/i18n';
import {
    getParticipantCount,
    isLocalParticipantModerator
} from '../../../base/participants';
import { connect as reactReduxConnect } from '../../../base/redux';
import { getDisplayName } from '../../../base/settings';
import { isLocalVideoTrackDesktop } from '../../../base/tracks';
import { Chat } from '../../../chat';
import { Filmstrip } from '../../../filmstrip';
import { CalleeInfoContainer } from '../../../invite';
import { LargeVideo } from '../../../large-video';
import { KnockingParticipantList, LobbyScreen } from '../../../lobby';
import { ParticipantsPane } from '../../../participants-pane/components';
import { getParticipantsPaneOpen } from '../../../participants-pane/functions';
import { Prejoin, isPrejoinPageVisible } from '../../../prejoin';
import { MaterialNotifier } from '../../../prejoin/components/MaterialNotifier';
import ReactionAnimations from '../../../reactions/components/web/ReactionsAnimations';
import RecordingConsentModalManager from '../../../recording/components/Recording/RecordingConsentModalManager';
import { fullScreenChanged, showToolbox } from '../../../toolbox/actions.web';
import { Toolbox } from '../../../toolbox/components/web';
import { LAYOUTS, getCurrentLayout } from '../../../video-layout';
import { getDashBoardUrl, maybeShowSuboptimalExperienceNotification } from '../../functions';
import {
    AbstractConference,
    abstractMapStateToProps
} from '../AbstractConference';
import type { AbstractProps } from '../AbstractConference';

import ConfigureYourMeeting from './ConfigureYourMeeting';
import Labels from './Labels';
import { default as Notice } from './Notice';

declare var APP: Object;
declare var interfaceConfig: Object;
declare var config: Object;

const history = createBrowserHistory();


/**
 * DOM events for when full screen mode has changed. Different browsers need
 * different vendor prefixes.
 *
 * @private
 * @type {Array<string>}
 */
const FULL_SCREEN_EVENTS = [
    'webkitfullscreenchange',
    'mozfullscreenchange',
    'fullscreenchange'
];

// const styleConference = {
//     display: 'none',
//     opacity: '100%'
// };

const styleAd = {
    display: 'inline-block',
    height: '200px',
    width: '100%'
};
const styleMHAd: React.CSSProperties = {
    display: 'inline-block',
    height: '150px',
    width: '80%'
};

/**
 * The CSS class to apply to the root element of the conference so CSS can
 * modify the app layout.
 *
 * @private
 * @type {Object}
 */
const LAYOUT_CLASSNAMES = {
    [LAYOUTS.HORIZONTAL_FILMSTRIP_VIEW]: 'horizontal-filmstrip',
    [LAYOUTS.TILE_VIEW]: 'tile-view',
    [LAYOUTS.VERTICAL_FILMSTRIP_VIEW]: 'vertical-filmstrip'
};

/**
 * The type of the React {@code Component} props of {@link Conference}.
 */
type Props = AbstractProps & {

    /**
     * Whether the local participant is recording the conference.
     */
    _iAmRecorder: boolean,

    /**
     * Returns true if the 'lobby screen' is visible.
     */
    _isLobbyScreenVisible: boolean,

    /**
     * If participants pane is visible or not.
     */
    _isParticipantsPaneVisible: boolean,


    /**
     * The CSS class to apply to the root of {@link Conference} to modify the
     * application layout.
     */
    _layoutClassName: string,

    /**
     * Name for this conference room.
     */
    _roomName: string,

    /**
     * If prejoin page is visible or not.
     */
    _showPrejoin: boolean,

    dispatch: Function,

    /**
     *  Password.
     */
    _password: String,

    _room: Object,

    /**
     * Is the person moderator.
     */
    _isModerator: boolean,

    /**
     * If the conference has started just that moment.
     */
    _isAlone: boolean,

    /**
     * Timer.
     */
    _timer: number,

    t: Function,

    _mhConferenceJoined: boolean,

    /**
     * _isLobbyScreenVisible in lobby reducer.
     */
     _isLobbyScreenVisibl: boolean,

     /**
      * OPen Notification.
      */
    openNotification: ?Boolean,

     /**
      * Notification Details.
      */
    notifierDetails: ?Object,

    handleClose: Function,

    /**
     * Returns boolean if the user is screensharing.
     */
    _isSharing: boolean
}

/**
 * The conference page of the Web application.
 */
class Conference extends AbstractConference<Props, *> {
    _onFullScreenChange: Function;
    _onShowToolbar: Function;
    _originalOnShowToolbar: Function;
    roomCreateInvitation: Function;

    /**
     * Initializes a new Conference instance.
     *
     * @param {Object} props - The read-only properties with which the new
     * instance is to be initialized.
     */
    constructor(props) {
        super(props);

        this.state = {
            isOpen: false,
            timeOut: null,
            isConferenceEmbedded: false
        };

        // Throttle and bind this component's mousemove handler to prevent it
        // from firing too often.
        this._originalOnShowToolbar = this._onShowToolbar;
        this._onShowToolbar = _.throttle(
            () => this._originalOnShowToolbar(),
            100,
            {
                leading: true,
                trailing: false
            });

        this.roomCreateInvitation = _.once(() => {
            const data = jitsiLocalStorage.getItem('features/onCreateconference');
            const parser = JSON.parse(data || '{}');

            if (parser.FirstOpenDialog) {

                const parse = parser.FirstOpenDialog.roomName;

                if (!parse.includes(this.props._roomName)) {
                    parser.FirstOpenDialog.roomName.push(this.props._roomName);
                    jitsiLocalStorage.setItem('features/onCreateconference', JSON.stringify(parser));
                    this.props.dispatch(toggleDialog(ConfigureYourMeeting));
                }

            }

            if (!('FirstOpenDialog' in parser)) {
                const addObject = { 'roomName': [ `${this.props._roomName}` ]
                };

                parser.FirstOpenDialog = addObject;
                jitsiLocalStorage.setItem('features/onCreateconference', JSON.stringify(parser));
                this.props.dispatch(toggleDialog(ConfigureYourMeeting));


            }

        });

        // Bind event handler so it is only bound once for every instance.
        this._onFullScreenChange = this._onFullScreenChange.bind(this);
        this._onDialogHandle = this._onDialogHandle.bind(this);
        this._onHandleTimeOut = this._onHandleTimeOut.bind(this);
        this.anInterval = null;
    }

    // eslint-disable-next-line require-jsdoc
    checkIfConferenceIsEmbeded() {
        try {
            this.setState({
                isConferenceEmbedded: false
            });
        } catch (e) {
            this.setState({
                isConferenceEmbedded: true
            });
        }
    }

    /**
     * Start the connection and get the UI ready for the conference.
     *
     * @inheritdoc
     */
    componentDidMount() {
        this.checkIfConferenceIsEmbeded();
        document.title = `${this.props._roomName} | ${interfaceConfig.APP_NAME}`;
        this._start();
        if (this.props._room && document.body) {
            document.body.style.overflow = 'hidden';
        }

        const { pregData = {}, _isSharing } = this.props;

        this.anInterval = setInterval(() => {
            if (!this.state.isOpen && !config.iAmRecorder && !_isSharing) {
                this.setState({
                    isOpen: true
                });

            }
        }, pregData?.refresh_ads ?? 600000);

    }

    /**
     * Component that will mount in the conference.
     *
     * @inheritdoc
     */
    UNSAFE_componentWillMount() {
        // const { dispatch } = this.props;

        // dispatch(fetchpreRegApi());

        // Loading the Google Ads script when needed.
        if (!this.state.isConferenceEmbedded) {
            if (document.body && (window.location.hostname === 'meethour.io')) {
                const script = document.createElement('script');

                script.src = 'https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-5885538074079594';
                script.id = 'GoogleAdsScript';
                script.async = true;
                script.crossorigin = 'anonymous';
                document.body && document.body.appendChild(script);
            }
        }
    }

    /**
     * Calls into legacy UI to update the application layout, if necessary.
     *
     * @inheritdoc
     * returns {void}
     */
    componentDidUpdate(prevProps) {
        const { pregData } = this.props;

        if (typeof pregData === 'object' && pregData !== null && pregData.hasOwnProperty('display_ads')
            && pregData?.display_ads === true && prevProps._isAlone !== this.props._isAlone && !config.iAmRecorder && !this.props._isAlone) {
            setTimeout(() => {
                this.setState({
                    isOpen: true
                });
                if (!this.state.isConferenceEmbedded) {
                    (window.adsbygoogle = window.adsbygoogle || []).push({});
                }
            }, 4500);
        }
        if (this.props._shouldDisplayTileView
            === prevProps._shouldDisplayTileView) {
            return;
        }

        // TODO: For now VideoLayout is being called as LargeVideo and Filmstrip
        // sizing logic is still handled outside of React. Once all components
        // are in react they should calculate size on their own as much as
        // possible and pass down sizings.
        VideoLayout.refreshLayout();
    }


    /**
     * Disconnect from the conference when component will be
     * unmounted.
     *
     * @inheritdoc
     */
    componentWillUnmount() {
        APP.UI.unbindEvents();

        this.anInterval && clearInterval(this.anInterval);

        FULL_SCREEN_EVENTS.forEach(name =>
            document.removeEventListener(name, this._onFullScreenChange));

        APP.conference.isJoined() && this.props.dispatch(disconnect());
    }

    /**
     * Implements React's {@link Component#render()}.
     *
     * @inheritdoc
     * @returns {ReactElement}
     */
    render() {
        const {
            _iAmRecorder,
            _isLobbyScreenVisible,
            _isParticipantsPaneVisible,
            _layoutClassName,
            _showPrejoin,
            _mhConferenceJoined,
            _isLobbyScreenVisibl,
            notifierDetails,
            openNotification,
            pregData,
            _isAlone,
            desktopapp

            // _password,
            // _isModerator,
        } = this.props;
        const hideLabels = _iAmRecorder;

        // if (_room && !_password && _isModerator && _isAlone && !interfaceConfig.ENABLE_MOBILE_BROWSER) {
        //     this.roomCreateInvitation();
        // }
        // console.log('pregDataConference', typeof pregData === 'object' && pregData !== null && pregData.hasOwnProperty('display_ads') && pregData?.display_ads === true);
        const preMeetingStyle = { background: `${interfaceConfig?.applyMeetingSettings === true ? interfaceConfig.DEFAULT_BACKGROUND : '#405170'}` };

        const prejoin = (_showPrejoin && _mhConferenceJoined) || _isLobbyScreenVisibl ? {
            display: 'none',
            opacity: '100%'
        } : {

        };

        // Showing ads to show for free users randomly
        const adsBannersArray = [
            'images/adremovalbanner.jpg',
            'images/adlivestream.jpg',
            'images/adpayasyougo.jpg',
            'images/adpreregistration.jpg',
            'images/adsdks.jpg'
        ];
        const l = adsBannersArray.length - 1;
        const n = Math.round(Math.random() * l);

        const finalAdImage = adsBannersArray[n];

        return (
            <>
                <div id = 'layout_wrapper'>
                    <div
                        id = 'preloaderConference'
                        style = {{ ...prejoin,
                            ...preMeetingStyle }} />
                    <div
                        className = { _layoutClassName }
                        id = 'videoconference_page'
                        onMouseMove = { this._onShowToolbar }>

                        <Notice />
                        <div id = 'videospace'>
                            <LargeVideo />
                            {!_isParticipantsPaneVisible && <KnockingParticipantList />}
                            <Filmstrip />
                            { hideLabels || <Labels /> }
                        </div>

                        { _showPrejoin || _isLobbyScreenVisible || <Toolbox /> }
                        <Chat />


                        { this.renderNotificationsContainer() }

                        { <MaterialNotifier
                        // eslint-disable-next-line react/jsx-no-bind
                            handleClose = { () => this.props.dispatch(openNotifier(!openNotification)) }
                            notification = { notifierDetails }
                            openNotifier = { openNotification } /> }
                        <CalleeInfoContainer />

                        { ((_showPrejoin && _mhConferenceJoined) || _isLobbyScreenVisibl) && <Prejoin /> }
                        {/* { pregData ? _showPrejoin && <Prejoin /> : <div id = 'preloader1' />} */}

                    </div>
                    <ParticipantsPane />
                    <ReactionAnimations />
                    <RecordingConsentModalManager />
                    { typeof pregData === 'object' && pregData !== null && pregData.hasOwnProperty('display_ads') && !config.iAmRecorder && pregData?.display_ads === true && !_isAlone
                        && <Portal>
                            <UpperDialog
                                dialogColor = 'rgba(247, 247, 247, 0.9)'
                                isOpen = { this.state.isOpen }
                                onDialogHandle = { this._onDialogHandle }
                                urlFunction = { getDashBoardUrl }>
                                <Box
                                    display = 'flex'
                                    justifyContent = 'center'>

                                    { desktopapp === 1 || this.state.isConferenceEmbedded ? <img
                                        alt = { 'banner ad' }
                                        src = { finalAdImage }
                                        style = { styleMHAd } />
                                        : <ins
                                            className = 'adsbygoogle'
                                            data-ad-client = 'ca-pub-5885538074079594'
                                            data-ad-slot = '2439626115'
                                            style = { styleAd } />}

                                </Box>
                            </UpperDialog>
                        </Portal>
                    }
                    <ConfirmPageLeave
                        history = { history }
                        isActive = { this.props._room } />
                </div>
            </>
        );
    }

    /**
     * Updates the Dialog
     * disabled.
     *
     * @private
     * @returns {void}
     */
    _onDialogHandle() {
        this.setState(prevState => {
            return { ...prevState,
                isOpen: !prevState.isOpen };
        });
    }

    /**
     * Updates the Dialog
     * disabled.
     *
     * @param {any} timeOuts - Time Out.
     * @returns {void}
     */
    _onHandleTimeOut(timeOuts) {
        this.setState(prevState => {
            return { ...prevState,
                timeOut: timeOuts };
        });
    }

    /**
     * Updates the Redux state when full screen mode has been enabled or
     * disabled.
     *
     * @private
     * @returns {void}
     */
    _onFullScreenChange() {
        this.props.dispatch(fullScreenChanged(APP.UI.isFullScreen()));
    }

    /**
     * Displays the toolbar.
     *
     * @private
     * @returns {void}
     */
    _onShowToolbar() {
        this.props.dispatch(showToolbox());
    }

    /**
     * Until we don't rewrite UI using react components
     * we use UI.start from old app. Also method translates
     * component right after it has been mounted.
     *
     * @inheritdoc
     */
    _start() {
        APP.UI.start();

        APP.UI.registerListeners();
        APP.UI.bindEvents();

        FULL_SCREEN_EVENTS.forEach(name =>
            document.addEventListener(name, this._onFullScreenChange));

        const { dispatch, t } = this.props;


        dispatch(connect());


        maybeShowSuboptimalExperienceNotification(dispatch, t);

        // this.props.dispatch(registerSound(USER_WAITING_REGISTER, USER_WAITING_SOUND, { loop: true }));
    }
}

/**
 * Maps (parts of) the Redux state to the associated props for the
 * {@code Conference} component.
 *
 * @param {Object} state - The Redux state.
 * @private
 * @returns {Props}
 */
function _mapStateToProps(state) {
    const { password, conference, timer, mhConferenceJoined,
        openNotification, notifierDetails } = state['features/base/conference'];
    const { pregData } = state['features/base/mtApi'];
    const { lobbyScreen: { _isLobbyScreenVisible } } = state['features/lobby'];
    const { jwt } = state['features/base/jwt'];
    const { userDetails, desktopapp } = state['features/base/mtApi'];
    const isPersonModerator = isLocalParticipantModerator(state);
    const participantCount = getParticipantCount(state);
    const { accessToken } = state['features/base/jwt'];
    const isSharing = isLocalVideoTrackDesktop(state);
    const name = getDisplayName(state);
    const isAlone = participantCount === 1;
    let timeInMins;

    if (timer) {
        const time = timer.split(':')[0];

        // eslint-disable-next-line radix
        timeInMins = parseInt(time);
    }


    return {
        ...abstractMapStateToProps(state),
        _iAmRecorder: state['features/base/config'].iAmRecorder,
        _isLobbyScreenVisible: state['features/base/dialog']?.component === LobbyScreen,
        _isParticipantsPaneVisible: getParticipantsPaneOpen(state),
        _layoutClassName: LAYOUT_CLASSNAMES[getCurrentLayout(state)],
        _roomName: getConferenceNameForTitle(state),
        _showPrejoin: isPrejoinPageVisible(state),
        _password: password,
        _isModerator: isPersonModerator,
        _room: conference,
        _isAlone: isAlone,
        _timer: timeInMins,
        pregData,
        _jwt: jwt,
        _isSharing: isSharing,
        _mhConferenceJoined: !mhConferenceJoined,
        _isLobbyScreenVisibl: _isLobbyScreenVisible,
        openNotification,
        notifierDetails,
        name,
        accessToken,
        userDetails,
        desktopapp

    };
}


export default reactReduxConnect(_mapStateToProps)(translate(Conference));
