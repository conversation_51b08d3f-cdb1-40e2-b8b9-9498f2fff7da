/* eslint-disable react-native/no-color-literals */
/* eslint-disable react-native/no-inline-styles */
// @flow

import { AtlasKitThemeProvider } from '@atlaskit/theme';
import React from 'react';

// import CookieConsent from 'react-cookie-consent';

// import UIUtil from '../../../../modules/UI/util/UIUtil';
import { DialogContainer } from '../../base/dialog';
import MHThemeProvider from '../../base/ui/components/MHThemeProvider';
import { ChromeExtensionBanner } from '../../chrome-extension-banner';

import { AbstractApp } from './AbstractApp';

// Register middlewares and reducers.
import '../middlewares';
import '../reducers';

/**
 * Root app {@code Component} on Web/React.
 *
 * @augments AbstractApp
 */
export class App extends AbstractApp {
    /**
   * Overrides the parent method to inject {@link AtlasKitThemeProvider} as
   * the top most component.
   *
   * @override
   */
    _createMainElement(component, props) {
        return (
            <MHThemeProvider>
                <>
                    <ChromeExtensionBanner />
                    {super._createMainElement(component, props)}
                </>
            </MHThemeProvider>
        );
    }

    /**
   * Renders the platform specific dialog container.
   *
   * @returns {React$Element}
   */
    _renderDialogContainer() {
        return (
            <>
                {/* <CookieConsent
                    buttonStyle = {{
                        color: '#ffffff',
                        fontSize: '15px',
                        margin: 0,
                        backgroundColor: '#1acc8d'
                    }}
                    buttonText = 'Agree'
                    buttonWrapperClasses = 'cookies-button-wrapper'
                    cookieName = 'meethourCookieConsent'
                    declineButtonStyle = {{
                        fontSize: '13px',
                        margin: 0
                    }}
                    expires = { 150 }
                    location = 'bottom'
                    style = {{
                        background: '#000000',
                        zIndex: '11111111111',
                        alignItems: 'center',
                        textAlign: 'center',
                        justifyContent: 'center'
                    }}>
                    <div
                        style = {{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '5px',
                            justifyContent: 'center'
                        }}>
                        <img
                            alt = 'cookie-image'
                            height = { 30 }
                            src = { 'images/cookies.svg' }
                            width = { 30 } />
                        <p
                            style = {{
                                margin: 0,
                                color: '#fff',
                                fontSize: '16px'
                            }}>
                            {' '}
                        We use cookies in the delivery of our services. By using our
            platform you agree to use of cookies. To know the information about
            privacy policy regarding cookies, please click <a
                                href = '#'
                                onClick = { () => UIUtil.redirect('/privacypolicy') }>Learn More.</a>
                        </p>
                    </div>
                </CookieConsent> */}
                <AtlasKitThemeProvider mode = 'dark'>
                    <DialogContainer />
                </AtlasKitThemeProvider>
            </>
        );
    }
}

